@media (max-width: 991px) {
  .container.mx-auto.px-4 {
    padding: 0px 15px;
  }
  .internet-section2 .container.mx-auto.px-4 {
    padding: 40px 15px !important;
    max-width: 720px;
  }
  .internet-section1
    + .internet-section2
    .container.mx-auto.px-4.bg-transparent {
    margin-top: -100px;
  }
  .tv-section7 .grid .section7-right-content {
    width: 100%;
    margin: auto 0px;
  }
  .internet-section2 .gray-round {
    width: 180px;
    height: 180px;
  }
  .internet-section2 .gray-round .internet-sec2-icon {
    width: 120px;
    height: 120px;
  }
  .internet-section2 .gray-round .h4 {
    font-size: 20px;
    margin: 10px 0px;
  }
  .tv-section4 .selectPlan.addon-plan-1 {
    padding: 0px 0px;
  }
  .tv-section4 .selectPlan .selectPlan__box.phone-page {
    min-width: 220px;
    max-width: 220px;
  }
  .phone-section1 img.phone-banner-img {
    height: 340px;
    width: 100%;
    object-fit: cover;
    object-position: center 0px;
  }
  .tv-section2 .tv-sec2-img {
    min-width: 100%;
  }
}
@media (max-width: 767px) {
  .blog-section {
    padding: 40px 0px;
  }
  .tv-section1 .h2,
  .phone-section1 .h2,
  .tv-section3 .h2,
  .tv-section4 .h2,
  .tv-section8 .h2,
  .tv-section10 .h2,
  .tv-section9 .h2,
  .internet-section1 .h2,
  .internet-section2 .h2,
  .internet-section4 .h2 {
    font-size: 36px;
    line-height: 40px;
  }
  .tv-section1 {
    padding-bottom: 0px;
  }
  .tv-section2 .btn-bg-new {
    width: 100%;
  }
  .home-section1 {
    padding-top: 90px;
    min-height: auto;
    position: relative;
    width: 100vw;
    overflow: hidden;
  }
  .home-section1::after {
    content: "";
    position: absolute;
    width: 60px;
    height: 120px;
    right: -30px;
    top: -30px;
    z-index: 1;
    border-radius: 30%;
    background-color: #ffffff;
    transform: rotate(-45deg);
  }
  .home-section1 .cow-right-bottom {
    display: none;
  }
  .home-section2-new .h2,
  .home-section2 .h2 {
    font-size: 36px;
    line-height: 48px;
  }
  .home-section1 .h2 {
    font-size: 42px;
    line-height: 52px;
  }
  .tv-section5 {
    padding: 40px 0px;
  }
  .internet-section3 .grid .internet-section3__left {
    width: 100%;
    max-width: 100%;
    margin: auto auto 20px auto;
  }
  .internet-section4 {
    padding: 20px 10px 10px 10px;
    max-width: calc(100vw - 30px);
  }
  .tv-section2 .tv-sec2-img {
    min-width: auto;
    margin: auto;
  }
  .internet-section2 {
    padding-top: 0px;
    max-width: calc(100vw - 30px);
    margin: auto;
  }
  .internet-section1 .h2 {
    margin-bottom: 0px !important;
  }
  .tv-section2 .h3,
  .tv-section4 .h3,
  .tv-section8 .h3 {
    font-size: 30px;
  }

  .tv-section6 .grid .tv-sec6-img {
    right: 0px;
    margin-bottom: 40px;
  }
  .tv-section9 .grid .section9-text::before {
    width: calc(100% - 50px);
  }
  .tv-sec3-icon {
    width: 70px;
    height: 70px;
    margin: 24px auto 10px auto;
  }
  .tv-sec3-box .tv-sec3-icon {
    margin: auto;
  }
  .tv-section4 .selectPlan {
    padding: 20px 0px;
  }
  .heading-h2 {
    font-size: 20px;
    padding: 0px 15px;
    max-width: 360px;
    margin: 20px auto;
  }
  .heading-h2.mtb-20 {
    margin: 20px auto !important;
  }
  .contact-center {
    padding: 0px 15px;
  }
  .contact-center .contact-center {
    padding: 0px 0px;
  }
  .btn {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
  .success-alert {
    font-size: 18px;
  }
  .steps--row {
    max-width: 240px;
    margin-bottom: 30px;
  }
  .steps--row .steps--row__box.active-half::before {
    min-width: 40px;
  }
  .steps--row .steps--row__box.active::before {
    min-width: 80px;
  }
  .steps--row .steps--row__box .steps--row__box-cont {
    display: none;
  }
  .steps-bottom-row {
    padding: 20px 15px 70px 15px;
    max-width: 360px;
    border-top: 1px solid #976dd0;
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    margin: auto !important;
  }
  .steps-bottom-row,
  .selectPlan {
    flex-direction: column;
    max-width: 360px;
    width: 100%;
    margin: auto;
  }
  .selectPlan {
    padding: 0px 15px 170px 15px;
  }
  .selectPlan.pb-0 {
    padding-bottom: 0px;
  }
  .selectPlan-channel {
    padding: 0px 15px 170px 15px;
    width: 100%;
    max-width: 360px;
    margin: auto;
  }
  .selectPlan .selectPlan__box {
    max-width: 220px;
    min-width: 220px;
    margin: auto auto 20px auto;
  }
  .selectPlan.addon-plan .selectPlan__box-inner {
    min-height: auto;
  }
  .steps-bottom-row .flex {
    display: block;
    width: 100%;
  }
  .steps-bottom-row .flex .steps-bottom-row__month-payment-box {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin-right: 0px;
    margin-bottom: 10px;
  }
  .steps-bottom-row .steps-bottom-row__month-payment-box .h3 {
    font-size: 16px;
  }
  .steps-bottom-row .button-box {
    display: flex;
    justify-content: space-between;
    max-width: 360px;
    margin-top: 15px;
    width: 100%;
    position: fixed;
    left: 0px;
    right: 0px;
    bottom: 0px;
    margin: auto;
    padding: 15px;
    background: white;
  }
  .steps-bottom-row .button-box .btn {
    margin: 0px 0px;
    min-width: 46%;
  }
  .selectPlan__optional {
    margin: auto auto 20px auto;
  }
  .chennel-modal-parent .chennels-box {
    grid-template-columns: repeat(6, 1fr);
  }
  .contact-page-center {
    width: 100%;
  }
  .contact-page-center.addon-plan {
    padding-bottom: 160px;
  }
  .contact-page-center .heading-h2 {
    max-width: 100%;
    padding: 0px 15px !important;
    text-align: left;
  }
  .information-box {
    padding: 8px 20px 8px 10px;
  }
  .information-box .information-box__row-right {
    max-width: 145px;
  }
  .information-box .arrow-info {
    right: 0px;
  }

  .arrow-info {
    right: 6px;
  }
  .checkbox-form {
    font-size: 12px;
  }
  .step-btn-right {
    text-align: right;
  }
  .steps-bottom-row .button-box.step-btn-right .btn {
    margin-left: auto;
  }
  .selectPlan .selectPlan__optional .a-link-bold {
    right: 0px;
  }
  .button-seven-step {
    padding: 0px;
    flex-direction: row;
  }
  .button-seven-step .btn {
    margin: 20px 10px 20px 10px !important;
    min-width: 120px;
  }
  .p-lr-15 {
    padding: 0px 15px;
  }
  .max-width360 {
    max-width: 360px !important;
    margin: auto;
  }
  .submit-stp-loader {
    top: 10px;
    right: 12px;
  }
  .tv-section6 {
    padding: 40px 0px 40px 0px;
  }
  .tv-section7 {
    padding: 20px 0px;
  }
  .tv-section9 .tv-sec9-img {
    display: none;
  }
  .internet-section1 img.internet-banner-img {
    margin: -20px auto 0px auto;
  }
  .internet-section1
    + .internet-section2
    .container.mx-auto.px-4.bg-transparent {
    margin-top: -35px;
  }
  .tv-section2 .grid {
    display: flex;
    flex-direction: column-reverse;
  }
  .large-view-none {
    display: flex !important;
  }
  .internet-section4 .internet-section4__box {
    min-width: 250px;
  }
  .home-section2 .container.mx-auto.px-4 {
    padding: 0;
  }
  .internet-section3__right {
    padding: 0px 15px;
  }
  .internet-section3 .container.mx-auto.px-4 {
    padding: 0;
  }
  .internet-section1
    + .internet-section2
    .container.mx-auto.px-4.bg-transparent {
    margin-top: -35px;
  }
  .home-top-feild .max-width80 {
    max-width: 100px;
    margin-left: 8px;
  }
}
@media (max-width: 575px) {
  .tv-section5-row {
    flex-direction: inherit;
    flex-wrap: wrap;
  }
  .tv-section5 .tv-section5-row {
    justify-content: space-between;
  }
  .tv-section5-row .max-width80 {
    max-width: 80%;
  }
  .tv-section5 .tv-section5-row .input1,
  .tv-section5 .tv-section5-row .input2,
  .tv-section5 .tv-section5-row .send-btn {
    width: 100%;
    margin: 10px 0px;
  }
  .tv-section5 .tv-section5-row .send-btn {
    max-width: 50px;
  }
  .tv-section5 .tv-section5-row .send-btn .send-btn-icon {
    margin: auto;
  }
  .tv-section7 .grid .section7-text {
    padding: 20px 15px;
    height: auto;
  }
  .tv-section9 .grid .section9-text {
    width: 100%;
    max-width: 320px;
  }
  .new-header-row .border-btn {
    padding: 5px 12px;
    border-radius: 4px;
    font-size: 12px;
    min-width: auto;
  }
  .message-box {
    width: 300px;
  }
  .phone-section1 img.phone-banner-img {
    object-position: 70% 0%;
  }
  .learn-more-mobile {
    display: flex;
  }
  .large-view-show {
    display: none;
  }
  .tv-section4 .h2,
  .tv-section8 .h2,
  .tv-section9 .h2,
  .tv-section3-new .h2 {
    font-size: 36px;
    line-height: 40px;
  }

  #video {
    margin: auto;
  }
}
