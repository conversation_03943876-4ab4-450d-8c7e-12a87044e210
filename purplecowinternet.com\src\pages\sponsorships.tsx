import React, { useState, useEffect } from "react"
import "../styles/custome.css"
import { formatPhoneNumber } from "../utils"
import { Link } from "gatsby"
import { StaticImage } from "gatsby-plugin-image"
import { Helmet } from "react-helmet"
import Layout from "../components/Layout"
import Loading from "../components/Loading"
import { colorTheme } from "../data/SrtingConstants"

const SponsorshipsPage = () => {
  interface FormData {
    firstName: string
    lastName: string
    email: string
    phone: string
    subject: string
    description: string
  }

  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    subject: "",
    description: "",
  })

  const clearFormData = () => {
    formData.firstName = ""
    formData.lastName = ""
    formData.email = ""
    formData.phone = ""
    formData.subject = ""
    formData.description = ""
  }

  const [phoneError, setPhoneError]: any = useState("")
  const [currError, setCurrError]: any = useState("")
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false)

  useEffect(() => {
    // Check local storage to maintain state across refreshes
    const submissionStatus = sessionStorage.getItem("formSubmitted")
    if (submissionStatus === "true") {
      setIsSubmitted(true)
    }
  }, [])

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
  }

  const handlePhoneNumberChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    let pNumber = e.target.value.replace(/\D/g, "")
    setFormData({ ...formData, phone: formatPhoneNumber(pNumber) })
    let phoneValid = pNumber.match(
      /^(\+1 ?)?\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
    )
    if (!phoneValid) {
      phoneValid = pNumber.match(
        /^(\+?1 ?)?\(?([0-9]{1})\)?[-. ]?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
      )
    }
    if (!phoneValid) {
      setPhoneError("Please enter a valid phone number")
    } else {
      setPhoneError("")
      setCurrError("")
    }
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    setIsLoading(true)
    if (phoneError !== "") {
      setIsLoading(false)
      setCurrError(phoneError)
      return
    }
    try {
      const payload = { ...formData }
      clearFormData()
      const response = await fetch(
        `${process.env.GATSBY_LAMBDA}/submit-sponsorship`,
        {
          method: "POST",
          body: JSON.stringify(payload),
        }
      )
      setIsLoading(false)
      if (!response.ok) {
        setCurrError("Please try again later.")
      } else {
        setCurrError("")
        setIsSubmitted(true)
        sessionStorage.setItem("formSubmitted", "true")
      }
    } catch (error) {
      setIsLoading(false)
      setCurrError("Please try again later.")
    }
  }

  if (isSubmitted) {
    return (
      <Layout>
        <Helmet>
          <title>Form submitted – Purple Cow Internet 💜🐄</title>
        </Helmet>
        <div className="main-sponsorships-container">
          <div className="contact-form-container">
            <div className="text-center logo-center">
              <Link to="/">
                <StaticImage
                  className="w-24"
                  layout="constrained"
                  src="../content/cow.png"
                  alt="Purple Cow Internet 💜🐄"
                />
              </Link>
            </div>
            <h2>Form Submitted!</h2>
            <h4>Thank you for your submission. We will be in touch shortly.</h4>
          </div>
        </div>
      </Layout>
    )
  }

  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center", // Center horizontally
          alignItems: "center", // Center vertically
          height: "100vh", // Full viewport height
        }}
      >
        <Helmet>
          <title>Form submitted – Purple Cow Internet 💜🐄</title>
        </Helmet>
        <Loading></Loading>
      </div>
    )
  }

  return (
    <Layout>
      <Helmet>
        {/* eslint-disable-next-line jsx-a11y/accessible-emoji */}
        <title>Sponsorship – Purple Cow Internet 💜🐄</title>
      </Helmet>
      <div className="flex justify-center items-center py-12 px-4 sm:px-8">
        <div className="w-full max-w-3xl bg-white p-8 rounded-2xl shadow-lg">
          <h2
            style={{ color: colorTheme.DARK_PURPLE }}
            className="text-3xl font-bold text-center mb-8"
          >
            Sponsorship Form
          </h2>
          <form
            id="sponsorship-form"
            onSubmit={handleSubmit}
            className="space-y-6"
          >
            <div>
              <label className="block text-sm font-medium text-gray-700">
                First Name *
              </label>
              <input
                type="text"
                name="firstName"
                placeholder="First name"
                value={formData?.firstName}
                required
                onChange={handleChange}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Last Name *
              </label>
              <input
                type="text"
                name="lastName"
                placeholder="Last name"
                value={formData?.lastName}
                required
                onChange={handleChange}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Email *
              </label>
              <input
                type="email"
                name="email"
                placeholder="Email"
                value={formData?.email}
                required
                onChange={handleChange}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Phone *
              </label>
              <span className="text-sm text-red-500 block mb-1">
                {phoneError}
              </span>
              <input
                type="tel"
                name="phone"
                placeholder="Cellular/Mobile Number"
                value={formData?.phone}
                required
                maxLength={16}
                onChange={handlePhoneNumberChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Subject *
              </label>
              <input
                type="text"
                name="subject"
                placeholder="Subject"
                value={formData?.subject}
                required
                onChange={handleChange}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                Description *
              </label>
              <textarea
                name="description"
                placeholder="Description"
                value={formData?.description}
                required
                onChange={handleChange}
                rows={5}
                className="mt-1 w-full px-4 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none"
              ></textarea>
            </div>

            <div className="text-center">
              <span className="text-sm text-red-500 block mb-2">
                {currError}
              </span>
              <button
                style={{ backgroundColor: colorTheme.DARK_PURPLE }}
                type="submit"
                className="px-6 py-2 text-white text-lg font-medium rounded-lg transition duration-200"
                id="submit_address"
              >
                Submit Form
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  )
}

export default SponsorshipsPage
