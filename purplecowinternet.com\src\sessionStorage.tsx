export const crudSessionStorage = (
  type = "add",
  key: any,
  data: any = "",
  Valuetype: any = ""
) => {
  if (type == "add") {
    try {
      data = Valuetype == "single" ? data : JSON.stringify(data)
    } catch (e: any) {}
    sessionStorage.setItem(key, data) // Store encrypted data
  }
  if (type == "delete") {
    sessionStorage.removeItem(key)
  }
  if (type == "get") {
    let value: any = sessionStorage.getItem(key)
    if (value) {
      try {
        value = Valuetype == "single" ? value : JSON.parse(value)
      } catch (e: any) {}
    }
    return value ?? ""
  }
}

export const sessionStorageKeys = {
  CART_ID: "lkjslkfjsjkshdjkhjsadvhj",
}
