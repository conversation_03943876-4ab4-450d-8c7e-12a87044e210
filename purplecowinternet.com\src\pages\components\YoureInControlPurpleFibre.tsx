import React from "react"
import purpleFibreApp from "../../content/Purplefibre_App.png"
import RoundedContainer from "../../components/RoundedContainer"

const YoureInControlPurpleFibre = () => {
  const features = [
    "Parental controls",
    "Diagnose issues",
    "Adjust credentials",
    "Speed tests",
  ]
  return (
    <RoundedContainer>
      <div className="flex flex-col md:flex-row w-full">
        {/* Image Section */}
        <div className="md:w-[50%] flex justify-center mb-5 md:mb-0">
          <img
            className="w-[55%] md:w-full max-h-screen object-contain"
            src={purpleFibreApp}
            alt="Speed"
          />
        </div>

        {/* Text Section */}
        <div className="flex justify-center items-center md:w-[50%]">
          <div className="flex flex-col justify-center items-center md:items-start text-center md:text-left">
            <h1 className="text-[40px] sm:text-[35px] md:text-[55px] lg:text-[80px] xl:text-[100px] sm:text-left font-anton text-black">
              <div>YOU'RE IN</div>
              <div>CONTROL</div>
            </h1>

            {/* Feature List Section - Centered but Left-Aligned Content */}
            <div className="text-black mt-4 md:mt-12 md:pl-5 w-full flex justify-center md:justify-start">
              <ul className="space-y-1 m-2 font-sans font-normal text-lg sm:text-xl md:text-2xl lg:text-4xl text-left">
                {features.map((feature) => (
                  <li key={feature} className="flex items-center gap-3">
                    <span className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-black rounded-full"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </RoundedContainer>
  )
}

export default YoureInControlPurpleFibre
