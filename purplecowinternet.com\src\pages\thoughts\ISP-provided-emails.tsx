import React from "react"
import Layout from "../../components/Layout"
import { Helmet } from "react-helmet"
import breakingFreeImage from "../../pages/thoughts/images/Breaking-free-from-ISP-provided-emails.png"

export default function IspProvidedEmails() {
  return (
    <Layout>
      <Helmet>
        <title>Breaking Free from ISP-Provided Emails 💜🐄</title>
      </Helmet>

      <div className="prose flex flex-col prose-lg lg:prose-xl mx-5 sm:mx-10 md:mx-40 xl:mx-80 2xl:mx-96 thought-wrapper">
        <img
          src={breakingFreeImage}
          alt="Halifax's cheapest internet provider talks about how to break free from ISP-provided email address"
        />

        <h1>Breaking Free from ISP-Provided Emails</h1>

        <p>
          In the vast realm of Internet Service Providers (ISPs), certain
          elements are crafted to make products and services “sticky” for
          customers. The stickier the product, the harder it becomes for
          customers to leave. This strategy, although clever, often anchors
          customers to services that may no longer be beneficial or
          cost-effective for them. One of the classic examples of this in the
          ISP world is the ISP-provided email.
        </p>

        <p>
          At Purple Cow Internet, we've encountered countless customers hesitant
          to make a switch purely because of the email service tethered to their
          ISP. Years of communication, a plethora of stored emails, and the
          sheer convenience of a familiar email address can bind customers to an
          ISP, even when they're aware of cheaper or better alternatives
          available. Think about it: an email address isn't just a digital ID;
          it’s where bank statements go, it’s registered on multiple platforms,
          it’s shared with friends and family. The thought of changing it feels
          analogous to changing your own name.
        </p>

        <p>
          However, what many don't realize is that sticking with an ISP merely
          for the email service could be costing them hundreds of dollars
          annually. When competitors like Purple Cow Internet offer superior or
          similar services at a fraction of the price, the cost of loyalty to an
          outdated email address adds up.
        </p>

        <p>Thankfully, there's a solution.</p>

        <p>
          For those feeling tethered to their ISP because of their email, there
          are strategies to smoothly transition without the fear of losing your
          essential emails:
        </p>

        <ol>
          <li>
            <strong>Set up a New Account with Gmail (or another free provider):</strong>{" "}
            Before making any moves, establish a new email with providers like
            Gmail. It's free, user-friendly, and comes with the reliability of
            Google's infrastructure.
          </li>

          <li>
            <strong>Forwarding Old Emails:</strong> Most ISP email services
            offer a feature where you can forward incoming emails to another
            address. Set your ISP email to automatically forward all emails to
            your new Gmail account. This way, even if someone sends an email to
            your old address, you’ll receive it in your new inbox.
          </li>

          <li>
            <strong>Notify Your Contacts:</strong> Use the old email address to
            send a notification to all your contacts (or at least the essential
            ones) informing them of your new email address. Many platforms,
            including Gmail, have features to send these notifications en masse,
            ensuring that your contacts are updated.
          </li>

          <li>
            <strong>Update Your Subscriptions:</strong> Spend a week or two
            updating your email address on essential platforms – banks, work,
            subscription services, and any other services where your old email
            is registered.
          </li>

          <li>
            <strong>Regularly Check Your Old Email:</strong> While you're in the
            transition phase, regularly check your old ISP email for any missed
            emails or updates. Over time, as you shift everything to your new
            email, the need to check the old one will diminish.
          </li>

          <li>
            <strong>Gradually Phase Out:</strong> Once you've set up forwarding,
            notified everyone, and updated your subscriptions, the reliance on
            your old ISP email will drastically decrease. After a few months,
            it'll become more of a relic than a necessity.
          </li>
        </ol>

        <p>
          In conclusion, while ISP-provided emails can seem like an unbreakable
          bond, with the right steps, anyone can transition smoothly. At Purple
          Cow Internet, we're not just about providing top-notch internet
          services; we're about ensuring our customers are informed, empowered,
          and making choices that serve them best. Remember, an email address
          shouldn't be the deciding factor in your internet experience –
          especially when it's costing you hundreds annually. Make the switch.
          It’s simpler than you think.
        </p>

        <p>
          Bradley Farquhar
          <br />
          CEO
        </p>
      </div>
    </Layout>
  )
}
