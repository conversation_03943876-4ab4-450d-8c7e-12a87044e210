import React from "react"
import Loading from "../../components/Loading"
import { formatDescription, formatMoney } from "../../utils"
import { find, map, sum } from "lodash"
import { getCouponCodes } from "../../ReferralWrapper"
import RoundedContainer from "../../components/RoundedContainer"

interface OrderSummaryProps {
  loadingEstimate: boolean
  recurringSubtotal: any
  estimateData: any
  formData: any
}
const OrderSummaryCheckout: React.FC<OrderSummaryProps> = ({
  loadingEstimate,
  recurringSubtotal,
  estimateData,
  formData,
}) => {
  let Estimate: React.FunctionComponent<{ estimate: IEstimate | null }> = ({
    estimate,
  }) => {
    if (!estimate) {
      return <Loading />
    }

    let oneTime = estimate.invoice_estimate.line_items.filter(
      ({ date_from, date_to }) => date_from === date_to
    )

    let recurring = estimate.invoice_estimate.line_items.filter(
      ({ date_from, date_to }) => date_from !== date_to
    )

    let recurringSubtotal = sum(map(recurring, "amount"))

    let referrals: any = []
    for (const couponCode of getCouponCodes()) {
      referrals.push(
        find(estimate.invoice_estimate.discounts, {
          entity_id: couponCode,
        })
      )
    }
    referrals = referrals.filter((element: any) => element !== undefined)
    let total = formData?.monthEstimate?.invoice_estimate?.amount_due
    let taxes = sum(map(estimate.invoice_estimate.taxes, "amount"))

    return (
      <div className="text-lg">
        <div className="flex justify-between">
          <p>First Month</p>
          <p>{formatMoney(recurringSubtotal / 100)}</p>
        </div>
        {oneTime.map(({ description, amount }) => (
          <React.Fragment key={description}>
            <div className="flex justify-between">
              <p>{formatDescription(description)}</p>
              <p>{formatMoney(amount / 100)}</p>
            </div>
          </React.Fragment>
        ))}
        {referrals.map((referral: any, index: number) => (
          <div key={index} className="flex justify-between">
            <p className="text-[#D4A1FE] font-bold">{referral?.description}</p>
            <p className="text-[#D4A1FE] font-bold">
              -{formatMoney(referral.amount / 100)}
            </p>
          </div>
        ))}

        <div className="flex justify-between">
          <p>Tax</p>
          <p>{formatMoney(taxes / 100)}</p>
        </div>
        <div className="flex justify-between">
          <p className="font-bold">Order Total</p>
          <p className="font-bold">{formatMoney(total / 100)} </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <h3 className="text-white  font-bold">Order Summary</h3>
      {loadingEstimate ? (
        <div className="text-center mt-5">
          <Loading loaderData="Calculating..." />
        </div>
      ) : (
        <div>
          <RoundedContainer className="!mx-0 p-3">
            <div className="flex justify-between text-lg text-black">
              <span>Monthly Renewal</span>
              <span>{formatMoney(recurringSubtotal / 100)} + tax</span>
            </div>
          </RoundedContainer>

          <RoundedContainer className="!mx-0 p-3">
            <Estimate estimate={estimateData}></Estimate>
          </RoundedContainer>
        </div>
      )}
    </>
  )
}

export default OrderSummaryCheckout
