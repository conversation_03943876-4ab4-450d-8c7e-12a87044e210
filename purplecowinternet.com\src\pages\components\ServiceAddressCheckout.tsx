import React from "react"
import RoundedContainer from "../../components/RoundedContainer"

interface ServiceAddressProps {
  formData: any
}
const ServiceAddressCheckout: React.FC<ServiceAddressProps> = ({
  formData,
}) => {
  return (
    <RoundedContainer bgColor="white" className="!m-0 p-3">
      <div className="flex justify-between">
        <div className="w-full">
          <h3 className="font-medium">Internet service address</h3>
          <p className="w-[80%] mt-1 text-lg text-black">
            {formData?.shipping_address?.SubBuilding
              ? formData?.shipping_address?.SubBuilding + "-"
              : ""}
            {formData?.shipping_address?.Line1},{" "}
            {formData?.shipping_address?.City},{" "}
            {formData?.shipping_address?.Province},{" "}
            {formData?.shipping_address?.PostalCode}
          </p>
        </div>
      </div>
    </RoundedContainer>
  )
}

export default ServiceAddressCheckout
