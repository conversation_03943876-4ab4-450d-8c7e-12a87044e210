//* This is a common component is being used for contact informatin which contains
// First name, Last name, Cell phone, Email, Mailing Address and Business name

import React, { useEffect, useRef, useState } from "react"
import { formatPhoneNumber } from "../../utils"
import { useDispatch, useSelector } from "react-redux"
import { setFormData } from "../../redux/formSlice"
import { ArrowDownIcon, CheckIcon } from "../../icons/Icons"
import Calendar from "./Calendar"
import { colorTheme } from "../../data/SrtingConstants"
import GoogleAddress from "./GoogleAddress"
import { allowedProvinces } from "../../addressValidator"

interface ContactFormProps {
  isCheckoutPage: boolean
  contactInfoErros: any
  setContactInfoErrors: any
}
const ContactComponent: React.FC<ContactFormProps> = ({
  isCheckoutPage,
  contactInfoErros,
  setContactInfoErrors,
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null)
  const unitRef = useRef<HTMLInputElement | null>(null)
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const dispatch = useDispatch()
  const formData = useSelector((state: any) => state.form)

  // Set input field Data
  const handleInputChange = (e: any) => {
    const { id, value } = e.target

    // Clear individual field error
    const updatedErrors = { ...contactInfoErros }
    if (id === "first_name") updatedErrors.firstNameError = ""
    if (id === "last_name") updatedErrors.lastNameError = ""
    if (id === "phone") updatedErrors.phoneError = ""
    if (id === "email") updatedErrors.emailError = ""
    if (id === "business") updatedErrors.businessError = ""

    setContactInfoErrors(updatedErrors)

    // Update Redux store
    let updatedCustomer = { ...formData?.customer }
    if (id === "first_name") updatedCustomer.first_name = value
    if (id === "last_name") updatedCustomer.last_name = value
    if (id === "phone")
      updatedCustomer.phone = formatPhoneNumber(value.replace(/\D/g, ""))
    if (id === "email") updatedCustomer.email = value
    if (id === "business") updatedCustomer.cf_company_name = value

    dispatch(setFormData({ customer: updatedCustomer }))
  }

  useEffect(() => {
    const updatedErrors = {
      ...contactInfoErros,
      Line1Error: "",
      PostalCodeError: "",
    }
    setContactInfoErrors(updatedErrors)

    if (
      formData?.mailing_address?.Province &&
      !allowedProvinces?.includes(formData?.mailing_address?.Province)
    ) {
      alert("This address isn't available")
      inputRef.current = null
      dispatch(
        setFormData({
          mailing_address: {
            Line1: "",
            SubBuilding: "",
            City: "",
            Province: "",
            PostalCode: "",
          },
        })
      )
    }
  }, [formData?.mailing_address])

  return (
    <>
      <div
        className={`flex flex-col gap-5 mt-10 ${isCheckoutPage ? "" : "sm:w-[90%] md:w-[75%]"}`}
      >
        <label
          style={{ color: isCheckoutPage ? "black" : "white" }}
          htmlFor="first_name"
          className="text-xl flex flex-col"
        >
          First Name
          <input
            id="first_name"
            type="text"
            autoComplete="off"
            placeholder="First Name"
            value={formData?.customer.first_name}
            required
            onChange={(e) => handleInputChange(e)}
            className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
            style={{ backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF" }}
          />
          <small className="text-red-600">
            {contactInfoErros?.firstNameError || ""}
          </small>
        </label>
        <label
          style={{ color: isCheckoutPage ? "black" : "white" }}
          htmlFor="last_name"
          className="text-xl flex flex-col"
        >
          Last Name
          <input
            id="last_name"
            type="text"
            autoComplete="off"
            value={formData?.customer.last_name}
            placeholder="Last Name"
            required
            onChange={(e) => handleInputChange(e)}
            className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
            style={{ backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF" }}
          />
          <small className="text-red-600">
            {contactInfoErros?.lastNameError || ""}
          </small>
        </label>
        <label
          style={{ color: isCheckoutPage ? "black" : "white" }}
          htmlFor="phone"
          className="text-xl flex flex-col"
        >
          Cell Phone
          <input
            maxLength={16}
            type="tel"
            id="phone"
            required
            autoComplete="off"
            placeholder="Cell Phone"
            name="phone"
            value={formData?.customer?.phone}
            onChange={(e) => handleInputChange(e)}
            className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
            style={{ backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF" }}
          />
          <small className="text-red-600">
            {contactInfoErros?.phoneError || ""}
          </small>
        </label>
        <label
          style={{ color: isCheckoutPage ? "black" : "white" }}
          htmlFor="email"
          className="text-xl flex flex-col"
        >
          Email
          <input
            className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
            id="email"
            type="email"
            autoComplete="off"
            value={formData?.customer.email}
            placeholder="Email"
            required
            onChange={(e) => handleInputChange(e)}
            style={{ backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF" }}
          />
          <small className="text-red-600">
            {contactInfoErros?.emailError || ""}
          </small>
        </label>

        <span className="flex gap-2">
          <div
            onClick={() =>
              dispatch(
                setFormData({
                  is_mailing: !formData?.is_mailing,
                })
              )
            }
            style={{
              backgroundColor: isCheckoutPage
                ? colorTheme.MEDIUM_PURPLE
                : "#FFFFFF",
            }}
            className="w-7 h-7 relative rounded-full cursor-pointer flex items-center justify-center"
          >
            {formData?.is_mailing && (
              <CheckIcon
                color={isCheckoutPage ? "white" : colorTheme.MEDIUM_PURPLE}
              />
            )}
          </div>
          <h3
            style={{ color: isCheckoutPage ? "black" : "white" }}
            className={`font-medium font-sans w-[90%]`}
          >
            Mailing and Service Address are the same
          </h3>
        </span>

        {!formData?.is_mailing && (
          <div className={`flex flex-col gap-5`}>
            <GoogleAddress
              googleAddressType={"mailing_address"}
              inputRef={inputRef}
              unitRef={unitRef}
            />

            <label
              style={{ color: isCheckoutPage ? "black" : "white" }}
              htmlFor="address"
              className="text-xl flex flex-col"
            >
              Address
              <input
                ref={inputRef}
                id="address"
                autoComplete="off"
                name="line1"
                placeholder="Address"
                type="text"
                style={{
                  backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF",
                }}
                value={formData?.mailing_address?.Line1 || ""}
                className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
                onChange={(e) => {
                  dispatch(
                    setFormData({
                      mailing_address: {
                        ...(formData?.mailing_address || {}),
                        Line1: e.target.value,
                      },
                    })
                  )
                }}
              />
              <small className="text-red-600">
                {contactInfoErros?.Line1Error || ""}
              </small>
            </label>

            <label
              style={{ color: isCheckoutPage ? "black" : "white" }}
              htmlFor="apartment"
              className="text-xl flex flex-col"
            >
              Apartment
              <input
                name="apartment"
                id="apartment"
                value={formData?.mailing_address?.SubBuilding || ""}
                autoComplete="off"
                placeholder="Apartment, suite or unit number"
                type="text"
                style={{
                  backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF",
                }}
                className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
                onChange={(e) =>
                  dispatch(
                    setFormData({
                      mailing_address: {
                        ...(formData?.mailing_address || {}),
                        SubBuilding: e.target?.value || "",
                      },
                    })
                  )
                }
              />
            </label>
            <label
              style={{ color: isCheckoutPage ? "black" : "white" }}
              htmlFor="city"
              className="text-xl flex flex-col"
            >
              City
              <input
                className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
                style={{
                  backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF",
                  cursor: "not-allowed",
                }}
                readOnly
                id="city"
                name="city"
                type="text"
                autoComplete="off"
                placeholder="City"
                required={!formData?.is_mailing}
                value={formData?.mailing_address?.City}
                onChange={(e) =>
                  dispatch(
                    setFormData({
                      mailing_address: {
                        ...formData?.mailing_address,
                        City: e.target.value,
                      },
                    })
                  )
                }
              />
            </label>

            <div className="flex gap-1 justify-between">
              <div className="w-[50%] sm:w-[25%] relative">
                <label
                  style={{ color: isCheckoutPage ? "black" : "white" }}
                  htmlFor="province"
                  className="text-xl flex flex-col"
                >
                  Province
                </label>
                <div className="rounded-2xl border-none bg-white w-full">
                  <button
                    style={{
                      backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF",
                      cursor: "not-allowed",
                    }}
                    type="button"
                    className="rounded-2xl w-full text-left px-4 py-2  placeholder:text-[#A9A9A9] placeholder:text-sm"
                    id="province"
                    // onClick={() => setIsOpen(!isOpen)}
                  >
                    <p
                      className={`${formData?.mailing_address.Province == "" ? "text-[#A9A9A9] placeholder:font-satoshi" : ""} flex items-center justify-between font-light p-1`}
                    >
                      {formData?.mailing_address.Province || "Select..."}
                      <ArrowDownIcon />
                    </p>
                  </button>
                  {isOpen && (
                    <ul className="absolute mt-1 z-10 w-full bg-white border border-gray-200 rounded-lg shadow-md">
                      {allowedProvinces?.map((province, index) => (
                        <li
                          key={index}
                          className="px-4 py-2 hover:font-bold cursor-pointer"
                          onClick={() => {
                            dispatch(
                              setFormData({
                                mailing_address: {
                                  ...formData?.mailing_address,
                                  Province: province,
                                },
                              })
                            )

                            setIsOpen(false)
                          }}
                        >
                          {province}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>

              <div className="w-[50%] sm:w-[75%]">
                <label
                  style={{ color: isCheckoutPage ? "black" : "white" }}
                  htmlFor="postal_code"
                  className="text-xl flex flex-col"
                >
                  Postal code
                  <input
                    className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
                    style={{
                      backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF",
                    }}
                    id="postal_code"
                    type="text"
                    name="postalcode"
                    autoComplete="off"
                    placeholder="Postal code"
                    required
                    value={formData?.mailing_address?.PostalCode || ""}
                    onChange={(e) =>
                      dispatch(
                        setFormData({
                          mailing_address: {
                            ...formData?.mailing_address,
                            PostalCode: e.target.value,
                          },
                        })
                      )
                    }
                  />
                  <small className="text-red-600">
                    {contactInfoErros?.PostalCodeError || ""}
                  </small>
                </label>
              </div>
            </div>
          </div>
        )}

        <div>
          <span className="flex gap-2">
            <div
              onClick={() =>
                dispatch(
                  setFormData({
                    is_business: !formData?.is_business,
                  })
                )
              }
              style={{
                backgroundColor: isCheckoutPage
                  ? colorTheme.MEDIUM_PURPLE
                  : "#FFFFFF",
              }}
              className="w-7 h-7 relative rounded-full cursor-pointer flex items-center justify-center"
            >
              {formData?.is_business && (
                <CheckIcon
                  color={isCheckoutPage ? "white" : colorTheme.MEDIUM_PURPLE}
                />
              )}
            </div>
            <h3
              style={{ color: isCheckoutPage ? "black" : "white" }}
              className={`font-medium font-sans w-[90%]`}
            >
              Is this a business location?
            </h3>
          </span>

          {formData?.is_business && (
            <div className="mt-3">
              <label
                style={{ color: isCheckoutPage ? "black" : "white" }}
                htmlFor="business"
                className="text-xl flex flex-col"
              >
                Business Name
                <input
                  className="rounded-2xl border-none font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm"
                  style={{
                    backgroundColor: isCheckoutPage ? "#F7F7F7" : "#FFFFFF",
                  }}
                  id="business"
                  type="text"
                  autoComplete="off"
                  placeholder="Business Name"
                  value={formData?.customer.cf_company_name}
                  onChange={(e) => handleInputChange(e)}
                  required
                />
                <small className="text-red-600">
                  {contactInfoErros?.businessError || ""}
                </small>
              </label>
            </div>
          )}
        </div>
        <Calendar isCheckoutPage={isCheckoutPage} />
      </div>
    </>
  )
}

export default ContactComponent
