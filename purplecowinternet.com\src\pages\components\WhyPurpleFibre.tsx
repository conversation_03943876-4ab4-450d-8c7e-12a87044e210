import React from "react"
import RoundedContainer from "../../components/RoundedContainer"
import Welcome_video from "../../content/PurpleFibreLaunchLandscape.mp4"
import Welcome_video_Mobile from "../../content/PurpleFibreLaunchPortrait.mp4"
import { FibreHeading } from "../../components/FibreHeading"
import Arrow from "../../content/DownArrow.png"
import { colorTheme } from "../../data/SrtingConstants"
const WhyPurpleFibre = () => {
  return (
    <div className="mt-5">
      <RoundedContainer className="flex">
        <div className="md:w-[70%]">
          <FibreHeading
            color={colorTheme.MEDIUM_PURPLE}
            className="text-start w-full xl:w-[80%]"
            align="text-left"
            content={
              <span className="!font-anton">
                Why Purple Fibre is rated the Best Internet in nova scotia
              </span>
            }
          />
        </div>
        <div className="hidden md:flex w-[30%] items-center">
          <img src={Arrow} alt="Arrow" />
        </div>
      </RoundedContainer>

      <RoundedContainer>
        {/* mobile video */}
        <video
          autoPlay
          loop
          controls
          muted
          playsInline
          className="object-contain rounded-3xl w-full sm:hidden"
        >
          <source src={Welcome_video_Mobile} type="video/mp4" />
          Your browser does not support the video.
        </video>

        {/* Desktop video */}
        <video
          autoPlay
          loop
          controls
          muted
          playsInline
          className="object-contain rounded-3xl w-full hidden sm:block"
        >
          <source src={Welcome_video} type="video/mp4" />
          Your browser does not support the video.
        </video>
      </RoundedContainer>
    </div>
  )
}

export default WhyPurpleFibre
