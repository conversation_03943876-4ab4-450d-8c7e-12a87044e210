.slide-up {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease-out;
}
/* Default styles for iframe */
iframe {
  width: 1120; /* Make iframe fill its container width */
  height: 630; /* Allow the height to adjust according to aspect ratio */
  display: block; /* Ensure the iframe behaves as a block-level element */
  margin: 0 auto; /* Center the iframe horizontally */
}

/* Media query for mobile devices */
@media only screen and (max-width: 600px) {
  iframe {
    width: 100%; /* Adjust the width to make the video larger */
    max-width: 600px; /* Set a maximum width to prevent the video from becoming too wide */
    height: AUTO; /* Set a fixed height for the iframe */
    min-height: 227px;
  }
}
.slide-up.visible {
  max-height: 2000px; /*todo: this value should be dynamic. */
  transition: max-height 0.8s ease-out;
}

.slide-up1 {
  max-height: 0;
  overflow: hidden;
  position: absolute;
  left: -50000px;
  transition: max-height 0.3s ease-out;
}

.slide-up1.visible {
  max-height: 111000px;
  position: unset;
  left: unset;
  transition: max-height 0.5s ease-out;
}
/* start custome css */
.body--lato * {
  font-family: "Open Sans", sans-serif;
}
.height-100-btn-bottom {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}
.height-100-btn-bottom .steps-bottom-row {
  margin-top: auto;
}
.heading-h2 {
  font-weight: 500 !important;
  color: #47525e;
  font-size: 22px;
  padding: 0px 60px;
  margin-bottom: 36px;
}
.heading-h2.mb-2 {
  margin-bottom: 8px;
}
.contact-page-center {
  max-width: 600px;
  width: 500px;
  margin: auto;
}
.contact-page-center .heading-h2 {
  padding: 0px 0px !important;
}
.contact-center {
  padding: 0px 0px;
  width: 100%;
}
.heading-h2.mb-4 {
  margin-bottom: 1rem !important;
}
.heading-h2.mb-2 {
  margin-bottom: 0.5rem !important;
}
.p4 {
  color: #47525e;
  font-size: 14px;
}
.a-link-bold {
  font-size: 12px;
  font-weight: 700;
  cursor: pointer;
  color: #00a6ff;
}
.btn,
a.btn {
  width: auto;
  height: 48px;
  line-height: 48px;
  border: 1px solid #976dd0;
  font-size: 18px;
  display: inline-block;
  text-align: center;
}
.btn.btn-primary-outline {
  border: 1px solid #976dd0;
  color: #976dd0;
}
.success-alert {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  position: absolute;
}
.logo-center {
  display: block;
  margin: 60px auto 20px auto;
}
.steps--row {
  height: 8px;
  width: 100%;
  max-width: 450px;
  background-color: #dbdbdb;
  border-radius: 12px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  margin-bottom: 72px;
}
.steps--row .steps--row__box {
  position: relative;
}
.steps--row .steps--row__box .steps--row__box-cont {
  font-size: 14px;
  color: #47525e;
  position: absolute;
  top: 24px;
  left: 0px;
  right: 0px;
  margin: auto;
  white-space: nowrap;
  display: flex;
  justify-content: center;
}
.steps--row .steps--row__box::after {
  content: "";
  position: absolute;
  top: -8px;
  left: -8px;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background-color: #dbdbdb;
}
.steps--row .steps--row__box::before {
  content: "";
  position: absolute;
  height: 8px;
  left: 0;
  top: -4px;
  min-width: 0px;
  background-color: #dbdbdb;
  border-radius: 12px;
  transition: ease-in-out 300ms;
}
.steps--row .steps--row__box:last-child::before {
  display: none;
}
.steps--row .steps--row__box.active::before {
  background-color: #976dd0;
  min-width: 146px;
  transition: ease-in-out 300ms;
}
table {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

td,
th {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}
.steps--row .steps--row__box.active-half::before {
  background-color: #976dd0;
  min-width: 55px;
  transition: ease-in-out 300ms;
}
.steps--row .steps--row__box.active::after,
.steps--row .steps--row__box.active-half::after,
.steps--row .s1teps--row__box.focus::after {
  background-color: #976dd0;
}

.purple-dot::after {
  background-color: #976dd0 !important;
}

.selectPlan {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 0px 40px 0px 60px;
  margin-bottom: 40px;
}
.selectPlan .selectPlan__box {
  max-width: 200px;
  min-width: 200px;
  width: 100%;
  position: relative;
  display: block;
  transition: ease-in-out 300ms;
  /* border: 1px solid #969FAA; */
  box-shadow: rgba(0, 0, 0, 0.125) 0px 2px 12px 0px;
  border-radius: 13px;
  margin-bottom: 20px;
  margin-right: 20px;
}
.selectPlan .selectPlan__box_wide {
  max-width: 800px;
  min-width: 200px;
  width: 100%;
  position: relative;
  display: block;
  transition: ease-in-out 300ms;
  /* border: 1px solid #969FAA; */
  box-shadow: rgba(0, 0, 0, 0.125) 0px 2px 12px 0px;
  border-radius: 13px;
  margin-bottom: 20px;
  margin-right: 20px;
}
.selectPlan .selectPlan__optional {
  position: relative;
}
.selectPlan .selectPlan__optional .a-link-bold {
  position: relative;
  text-align: center;
  bottom: -6px;
  padding: 16px 0px 0px 0px;
}
.selectPlan .selectPlan__box input[type="radio"],
.selectPlan .selectPlan__box input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  visibility: hidden;
}
.selectPlan .selectPlan__box-inner {
  width: 100%;
  min-height: 131px;
  height: auto;
  border-radius: 12px;
  border: 4px solid transparent;
  padding: 20px 16px;
  text-align: center;
  transition: ease-in-out 300ms;
}
.selectPlan.addon-plan .selectPlan__box-inner {
  min-height: 276px;
}
.selectPlan
  .selectPlan__box
  input[type="radio"]:checked
  ~ .selectPlan__box-inner,
.selectPlan
  .selectPlan__box
  input[type="checkbox"]:checked
  ~ .selectPlan__box-inner {
  border: 4px solid #976dd0;
  transition: ease-in-out 300ms;
}
.selectPlan .selectPlan__box-inner .h3 {
  padding: 0px 0px 10px 0px;
  color: #47525e;
  font-size: 20px;
  font-weight: 700;
  margin: auto;
  position: relative;
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.selectPlan .selectPlan__box-inner .h3::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0;
  right: 0;
  margin: auto;
  height: 1px;
  width: 100px;
  background-color: #47525e;
  transition: ease-in-out 300ms;
}
.selectPlan.addon-plan .selectPlan__box-inner .h3 {
  width: 100%;
}
.selectPlan.addon-plan .selectPlan__box-inner {
  padding: 20px 12px;
}
.selectPlan .selectPlan__box-inner .h2 {
  font-size: 28px;
  color: #47525e;
  font-weight: 500;
  margin: 10px 0px 4px 0px;
}
.selectPlan .selectPlan__box-inner .plan-type {
  font-size: 14px;
  color: #47525e;
  font-weight: 600;
}
.selectPlan .selectPlan__box-inner .p4 {
  font-size: 12px;
  color: #47525e;
  font-weight: 600;
  margin-top: 8px;
  line-height: 16px;
}
.chennels-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  text-align: center;
  align-items: center;
  justify-items: center;
}
.gap-16 {
  gap: 16px;
}
.chennels-box-wide {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  text-align: center;
  align-items: center;
  justify-items: center;
}
.chennel-modal-parent {
  max-height: 430px;
  overflow: auto;
}
.chennel-modal-parent .chennels-box {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
}
.chennels-box .chennels-box__icon {
  width: 40px;
  height: 40px;
  /*border-radius: 50%; */
  /* border: 1px solid #969FAA; */
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px 5px;
  overflow: hidden;
}
.chennels-box .chennels-box__icon img {
  width: 100%;
  height: 100%;
  object-fit: scale-down;
}
.chennels-box .chennels-box__img {
  object-fit: scale-down;
  max-width: 30px;
  max-height: 30px;
  border-radius: 8px;
}
.selectPlan-channel {
  padding: 0px 40px 0px 60px;
  margin-bottom: 40px;
}
.additional-chanel-box {
  position: relative;
  position: relative;
  display: flex;
  flex-wrap: wrap;
}
.additional-select-box {
  width: 50px;
  height: 50px;
  position: relative;
  margin: 5px 5px;
  cursor: pointer;
}
.additional-select-box input[type="checkbox"] {
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0;
  cursor: pointer;
  z-index: 9;
}
.additional-select {
  width: 50px;
  height: 50px;
  border-radius: 20%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  background-color: white;
  /* border: 1px solid #969faa; */
  transition: all 100ms;
}
.additional-select-box input[type="checkbox"]:checked + .additional-select {
  border: 3px solid #d4a1fe;
}
.additional-select-box .additional-select .additional-box__img {
  object-fit: scale-down;
  width: 100%;
  height: 100%;
  max-width: 30px;
  max-height: 30px;
  border-radius: 8px;
  position: relative;
}
.channel-icon-single {
  margin: 5px 5px;
  padding: 5px;
}

.steps-bottom-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 0px 60px;
  margin-bottom: 24px;
}
.steps-bottom-row .steps-bottom-row__month-payment-box {
  margin-right: 35px;
}
.steps-bottom-row .steps-bottom-row__month-payment-box:last-child {
  margin-right: 0px;
}
.steps-bottom-row .steps-bottom-row__month-payment-box .p4 {
  font-size: 14px;
  color: #47525e;
  font-weight: 500;
}
.steps-bottom-row .steps-bottom-row__month-payment-box .h3 {
  font-weight: 700;
  font-size: 24px;
  color: #47525e;
}
.steps-bottom-row .btn {
  min-width: 160px;
  margin-left: 20px;
}
.increase-decrease {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
}
.increase-decrease .increase-decrease__btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #47525e;
}
.increase-decrease .increase-decrease__input {
  width: 40px;
  padding: 0px;
  border: none;
  font-size: 20px;
  color: #47525e;
  font-weight: 600;
}
.datepicker-inline .react-datepicker {
  background-color: #ffffff;
  border: none;
  border-radius: 20px;
}
.datepicker-inline .react-datepicker__month-container {
  float: unset;
}
.datepicker-inline .react-datepicker__header {
  background-color: transparent;
  border-bottom: none;
}

.datepicker-inline .react-datepicker__day,
.datepicker-inline .react-datepicker__time-name {
  line-height: 2rem;
  width: 2rem;
  text-align: center;
  color: #a9a9a9;
  font-family: satoshi, sans-serif;
}
.datepicker-inline .react-datepicker__day-name {
  line-height: 2rem;
  width: 2rem;
  font-weight: 800;
  text-align: center;
}

.datepicker-inline .react-datepicker__day--selected,
.datepicker-inline .react-datepicker__day--selected:hover {
  color: white;
  background-color: #d4a1fe;
  border-radius: 50%;
}
.react-datepicker__navigation--next,
.react-datepicker__navigation--previous {
  top: 14px;
}
.react-datepicker__day--keyboard-selected {
  background-color: transparent !important;
}

.react-datepicker__day--selected:focus-visible,
.react-datepicker__day:focus-visible {
  outline: none !important;
}
.date-bold {
  font-weight: 700;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.checkbox-form {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  color: #47525e;
}
.checkbox-form.justify-center {
  justify-content: center !important;
}
.checkbox-form input[type="checkbox"] {
  appearance: none;
  background-color: white;
  width: 20px;
  height: 20px;
  border: 1px solid #47525e;
  border-radius: 50%;
  display: grid;
  place-content: center;
  padding: 0px;
  margin-right: 8px;
}
.checkbox-form.radius4 input[type="checkbox"] {
  border-radius: 4px;
}
.checkbox-form input[type="checkbox"]:checked {
  background-color: #24cc20;
  border: 1px solid #24cc20;
}
.checkbox-form input[type="checkbox"]::before {
  content: "";
  width: 0.65em;
  height: 0.65em;
  clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
  transform: scale(0);
  transform-origin: bottom left;
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em white;
}
.checkbox-form input[type="checkbox"]:checked::before {
  transform: scale(1) rotate(11deg) translateY(-1px) translateX(-1px);
}
.information-box {
  box-shadow: 0px 2px 12px 0px #00000020;
  border-radius: 8px;
  width: 100%;
  padding: 8px 20px;
  padding-right: 60px;
  position: relative;
  margin-bottom: 20px;
}
.information-box .arrow-info {
  position: absolute;
  right: 15px;
  top: 0;
  bottom: 0px;
  margin: auto;
  height: 24px;
  width: 24px;
  text-align: center;
}
.information-box .information-box__row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin: 4px 0px;
}
.information-box .information-box__row-left {
  color: #47525e;
  font-size: 14px;
}
.information-box .information-box__row-right {
  color: #47525e;
  font-size: 14px;
  white-space: nowrap;
  width: auto;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.save-100pr-img {
  width: 100px;
  height: auto;
  object-fit: scale-down;
  position: relative;
  bottom: -20px;
  right: -77px;
  z-index: -1;
}
.tab-type {
  padding: 5px 8px 5px 8px;
  background: #0000000d;
  border: 1px solid #47525e;
  border-radius: 20px;
  min-width: 180px;
  width: fit-content;
  margin: 0px auto 0px auto;
}
.tab-type .tab-type__option {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 20px;
  display: inline-block;
  min-width: 80px;
  width: fit-content;
  color: #47525e;
  cursor: pointer;
  border: 1px solid transparent;
}
.tab-type .tab-type__option.active {
  color: #47525e;
  background-color: #ffffff;
  border: 1px solid #47525e;
}
.btn .arrow-btn {
  width: 24px;
  height: 24px;
  position: absolute;
  right: 10px;
}

/*card css for chargebee*/
.card-input-field {
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  background-color: #f7f7f7;
  border-color: transparent;
  padding: 1rem !important;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 300;
  --tw-text-opacity: 1;
  color: rgba(55, 65, 81, var(--tw-text-opacity));
  height: 50px;
}

.chargebee-card-components iframe {
  min-height: auto !important;
  max-height: none !important;
  display: block !important; /* Ensures it doesn't behave as an inline element */
}

.loader-center-img {
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: fixed;
  margin: auto;
}
.loader-center-img .w-24 {
  margin-bottom: 20px;
}

.contact-page-center .heading-h2.pb-10 {
  padding-bottom: 10px !important;
  margin-bottom: 0px;
}
.sub-heading-address {
  margin-bottom: 20px;
}

.w-100 {
  width: 100%;
}

.m-auto {
  margin: auto;
}

.submit-stp-loader {
  position: absolute;
  top: 13px;
  right: 11px;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.alert-success {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}

.alert-danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}

.hide {
  display: none;
}

.text-align-left-p p {
  text-align: left;
}
.width-unset {
  width: unset;
}

.steps--row__box {
  cursor: pointer;
}

.first-step-btn {
  margin-left: 0px !important;
}

.address-step-form-bottom {
  justify-content: flex-end !important;
  padding-right: 0px !important;
  max-width: calc(100% - 30px);
  padding-top: 20px !important;
  margin: 10px auto 10px auto;
  margin-right: 0px;
}

.address-step-form-bottom .step-btn-right {
  max-width: 100%;
}
.button-seven-step {
  margin-top: 30px !important;
  justify-content: center;
}
.error-field {
  color: red;
}
.CardForm input,
.CardForm,
.CardForm .CardInput {
  font-family: "Open Sans", sans-serif !important;
}
.none-progress .steps--row {
  display: none;
}

.react-datepicker__day[aria-disabled="false"] {
  color: #000;
}
.react-datepicker__day--selected {
  color: #fff !important;
}
.react-datepicker .react-datepicker__day--highlighted {
  background-color: unset !important;
  color: #d4a1fe;
  font-weight: normal;
}
.border-red-500 {
  border-color: red;
}

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
}
.logo-center {
  z-index: 9;
  background: #ffffffd9;
}
.view-incluid-outside .selectPlan .selectPlan__optional .a-link-bold {
  display: block;
  width: calc(100% - 20px);
  bottom: 20px;
  padding-top: 10px;
}
.tought-sec {
  background: #65226e;
  padding: 80px 0px;
  text-align: center;
}
.tought-sec .h1 {
  font-weight: 500 !important;
  color: #ffffff;
  font-size: 32px;
  margin-bottom: 0px;
}
.line-clamp-3 {
  position: relative;
  width: 100%;
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.heading-h2.text-black {
  color: #111827;
}

.main-sponsorships-container {
  display: flex;
  justify-content: center; /* Centers the form horizontally */
  align-items: center; /* Centers the form vertically */
  min-height: 100vh; /* Makes the container take at least the full viewport height */
  padding: 20px;
}

.contact-form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
}

.contact-form-container h2,
.contact-form-container h4 {
  margin-bottom: 20px;
  text-align: center; /* Centers the h1 text */
}

.contact-form-container form div {
  margin-bottom: 15px;
}

.contact-form-container form label {
  display: block;
  margin-bottom: 5px;
}

.contact-form-container form input,
.contact-form-container form textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
}

.form-button-container {
  display: flex;
  justify-content: center; /* Centers the submit button */
}

.form-logo-center {
  display: block;
  margin: 20px auto 20px auto;
}

.table-container table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  font-size: 18px;
  text-align: left;

  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Adds subtle shadow around the table */
}
.table-container th,
.table-container td {
  border-bottom: 1px solid #ddd;
  text-align: center;

  padding: 12px 15px; /* More padding for better spacing */
  font-size: 16px;
}
.table-container th {
  background-color: #f2f2f2;
  position: sticky;
  top: 68px;
  text-align: center;
  z-index: 10;
  min-width: 130px;

  color: #333; /* Dark grey text color */
  font-weight: bold;
}

.table-container tr:hover {
  background-color: #a059eb;
}

/* Style the "Most Popular" badge */
.most-popular-badge {
  position: absolute;
  top: -10px;
  left: -10px;
  background-color: #a97ce4; /* Purple color */
  color: white;
  font-size: 0.9rem;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 5px;
  text-transform: uppercase;
}
.banner-container {
  display: flex; /* Enables flexbox */
  justify-content: center; /* Centers content horizontally */
  align-items: center; /* Centers content vertically */
}

.banner-container img {
  max-width: 100%; /* Ensures the image doesn't overflow */
  height: auto; /* Maintains the aspect ratio */
}

/* Section styling */
.review-section {
  text-align: center;
  margin-bottom: 40px;
}

/* Header styling */
.review-header {
  margin-bottom: 20px;
}

.review-title {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.review-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 20px;
}

/* Large star styling */
.large-star-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.large-star-icon {
  width: 40px;
  height: 40px;
}

/* Review container */
.review-container {
  display: flex;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.review-scroll {
  display: flex;
  animation: scroll-left 65s linear infinite;
  gap: 20px;
}

/* Review card styling */
.review-card {
  flex: 0 0 auto;
  min-width: 300px;
  max-width: 350px;
  margin: 10px;
  padding: 20px;
  background-color: #ffffff; /* Gray background */
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: left;
  position: relative;
}

.review-label {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #000; /* Black background */
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 5px;
  text-transform: uppercase;
}

.customer-name {
  margin-top: 30px; /* Space below label */
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}

.star-row {
  display: flex; /* Align stars in a single row */
  align-items: center; /* Vertically center stars */
  margin: 10px 0;
  gap: 3px; /* Space between stars */
}

.star-icon {
  width: 20px;
  height: 20px;
}

.review-card p {
  margin: 10px 0;
  line-height: 1.5;
  font-size: 0.9rem;
  color: #555;
}

/* Scrolling animation */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .large-star-icon {
    width: 30px;
    height: 30px;
  }

  .review-card {
    min-width: 250px;
  }
}

.custom-scrollbar {
  /* For Webkit-based browsers like Chrome and Safari */
  overflow: auto;
}
.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: #fbc400;
  border-radius: 10px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: white;
  border-radius: 10px;
}
