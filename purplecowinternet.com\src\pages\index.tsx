import React, { useEffect, useRef, useState } from "react"
import Layout from "../components/Layout"
import { Helmet } from "react-helmet"
import MessageModal from "./components/common/MessageModal"
import {
  hideCouponHeader,
  useReferralState,
  bannerMessage,
} from "../ReferralWrapper"
import axios from "axios"
import Wifi from "../content/WifiInEveryRoom.jpg"
import tripleplay from "../../static/images/tripleplay.png"
import CommonQuestions from "../components/CommonQuestions"
import RoundedContainer from "../components/RoundedContainer"
import { homePageQuestions } from "../data/CommonQuestionsData"
import DancingCow from "../content/Dancing_Cow.gif"
import PcPeace from "../content/pc_peace.png"
import { navigate } from "gatsby"
import SplashImg from "../content/Splash_internet.png"
import { colorTheme } from "../data/SrtingConstants"
import GoogleAddress from "./components/GoogleAddress"
import { useDispatch, useSelector } from "react-redux"
import { setFormData } from "../redux/formSlice"
import CommonButtonInternet from "../components/CommonButton"
import { FibreHeading } from "../components/FibreHeading"
import { splitDescription } from "../utils"
import { ArrowRightIcon, PurpleCheckedIcon } from "../icons/Icons"
import Circle from "../content/CircleSteps.png"
import PurpleFIbreLogo from "../content/purple_fibre_logo.png"
import TestimonialList from "./components/ReviewCard"
import mapImg from "../../static/images/map.png"
import { validationRegEx } from "../data/Regex"

const FrontHomePage = () => {
  let referral = useReferralState()
  let banner_message = bannerMessage()
  const dispatch = useDispatch()
  const formData = useSelector((state: any) => state.form)
  const [messagePopup, setMessagePopup] = useState(false)
  const inputRef = useRef<HTMLInputElement | null>(null)
  const unitRef = useRef<HTMLInputElement | null>(null)
  const [internetPlans, setInternetPlans] = useState([])
  const [tvPlans, setTvPlans] = useState([])
  const [phonePlans, setPhonePlans] = useState([])
  const [showInputCustomPostalCode, setShowInputCustomPostalCode] =
    useState(false)
  const [showErrorForPostalCode, setShowErrorForPostalCode] = useState(false)
  const switchSteps = [
    {
      title: "Sign Up Online",
      description:
        "It only takes 30 seconds to set up, making it quick and hassle-free!",
    },
    {
      title: "We’ll bring you the Wifi gear",
      description: "An all-in-one box designed to look great in any room.",
    },
    {
      title: "Just plug it in!",
      description:
        "It's as simple as plugging in your laptop, with no complicated setup required!",
    },
  ]

  const fetchData = async () => {
    try {
      const internetResponse = await axios(
        process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
          "/snapshots/latest/services/internet/plans.json"
      )
      const tvResponse = await axios(
        process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
          "/snapshots/latest/services/tv/plans.json"
      )
      const phoneResponse = await axios(
        process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
          "/snapshots/latest/services/home-phones/plans.json"
      )
      setInternetPlans(internetResponse.data)
      setTvPlans(tvResponse.data)
      setPhonePlans(phoneResponse.data)
    } catch (err) {
      console.error("Failed to fetch internet plans:", err)
    }
  }

  const showCustomText = (text: string) => {
    if (text === "100 Mbps Down") return "Amazing Customer Service"
    if (text === "10 Mbps Up") return "Money Back Guarantee"
    return text
  }
  useEffect(() => {
    fetchData()
  }, [])

  return (
    <div className="home-parent">
      <Layout>
        <Helmet>
          <title>Home – Purple Cow Internet 💜🐄</title>
        </Helmet>
        {/*{isServiceOutageMode && (*/}
        {/* <div className="top-0 px-4 py-3 text-lg font-thick tracking-widest text-center text-white bg-primary-700">*/}
        {/*     <Link to ="/service-interruptions/" className="text-white block">We're aware of network interruptions currently affecting some of our customers. Our team is working on resolving this ASAP. Thank you for your patience. <br /> </Link>*/}
        {/* </div>*/}
        {/* )}*/}
        {referral &&
          referral?.length > 0 &&
          banner_message &&
          !hideCouponHeader(referral) && (
            <div className="top-0 px-4 py-3 text-lg font-thin tracking-widest text-center text-white bg-primary-700">
              {banner_message}
            </div>
          )}
        {/* Easy to switch */}
        <RoundedContainer className="mt-5" bgColor={colorTheme.MEDIUM_PURPLE}>
          <img
            src={DancingCow}
            className="w-32 mt-2 ml-2 md:w-40 lg:w-56 sm:ml-5 sm:mt-5"
            alt="Dancing cow"
          />
          <div className="sm:p-1">
            <GoogleAddress
              googleAddressType={"shipping_address"}
              inputRef={inputRef}
              unitRef={unitRef}
              setShowInputCustomPostalCode={setShowInputCustomPostalCode}
            />
            <div className="flex flex-col justify-center items-center">
              <FibreHeading
                align="text-center"
                color="white"
                content={
                  <div className="leading-tight flex flex-col items-center gap-4 text-center">
                    <div className="relative font-anton">
                      {/* Text content */}
                      <div className="relative !font-anton z-20">
                        EASY TO SWITCH, RELIABLE
                      </div>

                      {/* Background image */}
                      <img
                        src={SplashImg}
                        alt=""
                        className="absolute top-0 left-10 md:left-0 z-10 -translate-x-12"
                      />
                    </div>

                    <div className="!font-anton flex flex-col sm:flex-row items-center gap-2 sm:gap-3 z-20">
                      INTERNET,
                      <span
                        style={{ border: `4px solid ${colorTheme.GOLD}` }}
                        className="!font-anton rounded-full px-4 py-1 sm:px-6 sm:py-1 cursor-pointer z-20"
                        role="button"
                      >
                        ONLY $60
                      </span>
                    </div>
                  </div>
                }
              />

              <h3 className="!font-sans text-white text-center mt-5">
                Love our Internet or it's Free! Guaranteed!
              </h3>
              <div className="bg-white flex flex-wrap items-center justify-center gap-2 rounded-3xl lg:rounded-full p-2 mt-10">
                <input
                  ref={inputRef}
                  type="text"
                  name="line11"
                  required
                  className="border-none px-4 py-2 flex-[2] min-w-[200px]"
                  placeholder="Service Address"
                  id="home_new_address"
                  onBlur={() => {
                    setTimeout(() => {
                      if (typeof window === "undefined") return
                      let ad: any = document.getElementById("address")
                      if (ad !== null) ad.value = ""
                      let unitField: any = document.getElementById("unitField")
                      if (unitField !== null) unitField.value = ""
                    }, 700)
                  }}
                />
                <div className="text-[#D9D9D9]">|</div>

                <input
                  ref={unitRef}
                  type="text"
                  name="line2"
                  id="line2_address"
                  autoComplete="off"
                  className="border-none px-4 py-2 flex-[0.5] min-w-[80px]"
                  placeholder="Unit"
                  required
                  onChange={(e) => {
                    if (e.target.value?.length <= 10) {
                      dispatch(
                        setFormData({
                          shipping_address: {
                            ...formData.shipping_address,
                            SubBuilding: e.target.value.toUpperCase(),
                          },
                        })
                      )
                    }
                  }}
                />

                <div className="w-full sm:w-auto flex justify-center sm:justify-end mt-2 sm:mt-0">
                  <CommonButtonInternet
                    buttonText={
                      <>
                        Check Address
                        <ArrowRightIcon />
                      </>
                    }
                    onClick={(e) => {
                      const ad: any =
                        document.getElementById("home_new_address")
                      if (!ad.value) {
                        e.preventDefault()
                      }
                      if (
                        showInputCustomPostalCode &&
                        !validationRegEx.postalcode_regex.test(
                          formData?.shipping_address?.PostalCode
                        )
                      ) {
                        e.preventDefault()
                        setShowErrorForPostalCode(true)
                        return
                      }
                      navigate("/join-the-herd/")
                    }}
                    className="px-8"
                  />
                </div>
              </div>
              {showInputCustomPostalCode && (
                <input
                  type="text"
                  autoComplete="off"
                  className="border-none mt-5 rounded-3xl px-4 py-3 flex-[0.5] min-w-[150px]"
                  placeholder="Postal Code"
                  required
                  onChange={(e) => {
                    dispatch(
                      setFormData({
                        shipping_address: {
                          ...formData.shipping_address,
                          PostalCode: e.target.value.toUpperCase(),
                        },
                      })
                    )

                    setShowErrorForPostalCode(false)
                  }}
                />
              )}
            </div>

            {showErrorForPostalCode && (
              <div className="text-white-500 flex justify-center mt-2">
                <p className="text-center rounded-xl border bg-red-100 text-red-600 p-2">
                  Enter valid postal code such as A0B 0C3
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <img
              src={PcPeace}
              className="w-20 md:w-24 mt-2 mb-1 sm:mt-0 mr-5"
              alt="Peace"
            />
          </div>
        </RoundedContainer>
        {/* FIbre internet starting */}
        <RoundedContainer className="flex flex-col items-center">
          <h1 className="!font-anton text-center uppercase mt-5">
            FIBRE INTERNET STARTING AT $60
          </h1>
          <h3 className="!font-sans text-center mt-5">
            And the price stays the same year after year!
          </h3>
          <div className="flex flex-wrap justify-center gap-6 mt-10">
            {/* Internet */}
            {internetPlans
              ?.filter((plan: any) => plan?.status === "ACTIVE")
              .slice(0, 1)
              .map((plan: any, i: number) => (
                <div
                  key={i}
                  className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
                >
                  <div>
                    <h2 className="!font-anton mb-2">Internet</h2>
                    <div className="flex my-5">
                      <h1 className="!font-anton">
                        ${plan?.billing[0]?.monthly?.price}
                      </h1>
                      <p className="text-lg flex items-end ml-2">/month</p>
                    </div>
                    <ul className="plan-list text-[#666666] my-5">
                      {splitDescription(plan.description).map((line, i) => (
                        <li
                          className="my-2 flex items-center gap-3 !font-sans"
                          key={i}
                        >
                          <PurpleCheckedIcon />
                          {/* remove "✔️" sign and add svg*/}
                          {showCustomText(line?.replace("✔️", "")?.trim())}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="flex justify-center">
                    <CommonButtonInternet
                      buttonText={<>View Internet Options</>}
                      onClick={() => navigate("/internet")}
                      className={`!w-full`}
                    />
                  </div>
                </div>
              ))}

            {/* TV */}
            {tvPlans
              ?.filter((p: any) => p?.status === "ACTIVE")
              .slice(0, 1)
              .map((tvPlan: any, i: number) => (
                <div
                  key={i}
                  className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
                >
                  <div>
                    <h2 className="!font-anton mb-2">Television</h2>
                    <div className="flex my-5">
                      <h1 className="!font-anton">
                        ${tvPlan?.billing_period[0]?.monthly?.price || 0}
                      </h1>
                      <p className="text-lg flex items-end ml-2">/month</p>
                    </div>

                    <div className="grid grid-cols-3 gap-2 mb-5">
                      {tvPlan?.featured_channels?.map(
                        (channel: any, index: number) => (
                          <img
                            key={index}
                            src={channel?.image_url}
                            alt={channel?.name || "Channel"}
                            className={`w-[50px] h-[50px] object-contain`}
                          />
                        )
                      )}
                    </div>
                    <p className="my-4 font-sans text-[#606060]">
                      {tvPlan?.description}
                    </p>
                  </div>
                  <div className="flex justify-center">
                    <CommonButtonInternet
                      className={`!w-full`}
                      buttonText={<>View TV Options</>}
                      onClick={() => navigate("/tv")}
                    />
                  </div>
                </div>
              ))}

            {/* Home Phone */}
            {phonePlans
              ?.filter((p: any) => p?.status === "ACTIVE")
              .slice(0, 1)
              .map((homePhone: any, i: number) => (
                <div
                  key={i}
                  className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
                >
                  <div>
                    <h2 className="!font-anton mb-2">Home Phone</h2>
                    <div className="flex my-5">
                      <h1 className="!font-anton">
                        ${homePhone?.billing_period[0]?.monthly?.price || 0}
                      </h1>
                      <p className="text-lg flex items-end ml-2">/month</p>
                    </div>
                    <ul className="plan-list text-[#666666] my-5">
                      {splitDescription(homePhone.description).map(
                        (line, i) => (
                          <li
                            className="my-2 flex items-center gap-3 !font-sans"
                            key={i}
                          >
                            <PurpleCheckedIcon />
                            {/* remove "✔️" sign and add svg*/}
                            {line?.replace("✔️", "")?.trim()}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                  <div className="flex justify-center">
                    <CommonButtonInternet
                      className={`!w-full`}
                      buttonText={<>View Phone Options</>}
                      onClick={() => navigate("/home-phone")}
                    />
                  </div>
                </div>
              ))}
          </div>
        </RoundedContainer>
        {/* AWESOME, RELIABLE Wi-Fi */}
        <RoundedContainer className="flex flex-col items-center">
          <h1 className="!font-anton text-center uppercase mt-10">
            AWESOME, RELIABLE Wi-Fi
          </h1>
          <h3 className="!font-sans text-[#111111] text-center mt-5">
            Wi-Fi and modem is included for Free!
          </h3>

          <div className="flex flex-col lg:flex-row mt-10">
            <div className="w-full flex justify-center lg:w-[50%]">
              <img
                className="scale-90 hover:scale-95 transition-all duration-150"
                src={Wifi}
                alt="modem"
              />
            </div>
            <div className="flex flex-col justify-center lg:w-[50%] mx-10 md:mx-20 lg:mx-5">
              <h1 className="!font-anton uppercase mt-5">
                The Incredibly Smart <br className="hidden md:block" /> Wi-Fi
              </h1>
              <h3 className="!font-sans text-[#111111] mt-5">
                Our Wi-Fi modem, which also plays the role of a router, will
                impress you with its amazing technical capabilities. Its
                band-steering function, for example, makes for smooth web
                surfing on any device.
              </h3>

              <CommonButtonInternet
                buttonText="Check My Eligibility"
                onClick={() => navigate("/join-the-herd")}
                className="last:mt-5 text-[#111111] px-5"
              />
            </div>
          </div>
        </RoundedContainer>
        {/* Want TV and Phone? We've Got Them Here! */}
        <RoundedContainer bgColor={colorTheme.MEDIUM_PURPLE}>
          <div className="flex flex-col lg:flex-row m-10 my-20">
            <div className="w-full flex flex-col justify-center lg:w-[50%]">
              <h1 className="!font-anton text-white uppercase mt-5">
                Want TV and Phone? We've Got Them Here!{" "}
              </h1>
              <h3 className="!font-sans text-white mt-5">
                Enhance your connection with Purple Cow’s TV and Home Phone!
                With our TV app, you can stream your favorite shows on most
                Apple, Google or Firestick TV device, start, stop, pause, and
                record in the cloud with ease. Our Home Phone service keeps you
                connected with crystal-clear calling featuring unlimited North
                American long distance, call display, call waiting, and more.
                Simple, reliable, and built for the herd!
              </h3>
            </div>
            <div className="w-full flex items-center lg:w-[50%]">
              <img
                className="hover:scale-105 transition-all duration-150 rounded-3xl mt-10 lg:mt-0"
                src={tripleplay}
                alt="modem"
              />
            </div>
          </div>
        </RoundedContainer>
        {/* Super Easy to Switch! */}
        <RoundedContainer className="flex flex-col items-center">
          <h1 className="!font-anton text-center uppercase mt-10">
            Super Easy to Switch!
          </h1>
          <h3 className="!font-sans text-[#111111] text-center mt-5">
            Free tech visits if needed.
          </h3>

          <div className="w-full max-w-7xl mx-auto px-4 py-5 flex flex-col items-center sm:gap-3 md:gap-6 lg:gap-20 md:flex-row md:flex-wrap md:justify-center mt-14">
            {switchSteps?.length > 0 &&
              switchSteps?.map((step, index) => (
                <div
                  key={index}
                  className="w-full sm:w-2/3 md:w-1/2 lg:w-1/3 xl:w-1/4 flex justify-center"
                >
                  <div className="w-full max-w-xs h-max flex flex-col items-center text-center box-border">
                    {/* Circle fixed at top */}
                    <div
                      className="w-48 h-48 sm:w-56 sm:h-56 bg-no-repeat bg-center bg-contain flex items-center justify-center mb-6 shrink-0"
                      style={{ backgroundImage: `url(${Circle})` }}
                    >
                      <h1 className="font-sans font-bold text-8xl">
                        {index + 1}
                      </h1>
                    </div>

                    {/* Text wrapper with scroll fallback if overflow */}
                    <div className="overflow-hidden  flex-col items-start">
                      <h3 className="font-sans font-medium text-black">
                        {step?.title}
                      </h3>
                      <h4 className="font-sans text-[#4D4D4D] mt-4">
                        {step?.description}
                      </h4>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </RoundedContainer>
        {/* Next generation fibre network */}
        <RoundedContainer className="p-5 sm:p-10 md:p-14">
          <div className="flex flex-col lg:flex-row justify-between items-center min-h-[60vh] w-full">
            <div className="flex items-center justify-center lg:justify-start w-full lg:w-[50%]">
              <img
                src={PurpleFIbreLogo}
                alt="Purple Fibre"
                className="object-contain rounded-3xl transition-all duration-300 ease-in-out hover:drop-shadow-[0_10px_25px_rgba(168,85,247,0.5)]"
              />
            </div>

            <div className="flex flex-col justify-center items-center lg:items-end text-right w-full lg:w-[50%]">
              <h1 className="!font-anton uppercase text-center lg:text-end mt-10">
                Next generation fibre network
              </h1>
              <h3 className="!font-sans text-[#111111] text-center lg:text-end mt-5">
                We built our network from the ground up make sure your
                connection stays live.
              </h3>
              <CommonButtonInternet
                buttonText={"Learn more"}
                onClick={() => navigate("/purplefibre")}
                className="my-5"
              />
            </div>
          </div>
        </RoundedContainer>
        {/* We are the highest rated internet provider in the Atlantic Provinces */}
        <RoundedContainer
          bgColor={colorTheme.MEDIUM_PURPLE}
          className="p-5 sm:p-10 md:p-14"
        >
          <div className="flex flex-col items-center">
            <h1 className="!font-anton text-white text-center uppercase mt-5">
              We are the highest rated internet provider in the Atlantic
              Provinces
            </h1>
            <h3 className="!font-sans text-white text-center my-5">
              Nothing wrong bragging a little :)
            </h3>
          </div>

          <TestimonialList
            testimonials={[
              {
                name: "Adam G, Halifax, Nova Scotia",
                rating: 5,
                description:
                  "Purple cow is the only internet provider you need, and the only one you should consider. The internet service itself is fast and reliable - I don't have issues streaming, working from home, etc. Also, the customer service is unmatched. They connect by phone call, text, or video call. That connection is quick, so no long wait times on a phone. I always feel like positive reviews read as generic, but this is the truth. Purple Cow is the best. A+, 10/10, 💯.",
              },
              {
                name: "Shane W, Charlottetown, Prince Edward Island",
                rating: 5,
                description:
                  "Not usually the type to post reviews but the service with this group warrants one. From first text to purple cow to streaming our first movie only took 3 days. Everyone was very helpful and set up was a breeze, they even checked our signal strength and did a follow up to make sure we were happy with everything. Thanks to all.",
              },
              {
                name: "Monica R, Dartmouth, Nova Scotia",
                rating: 5,
                description:
                  "Honestly, the internet is just as good as any other service in Halifax. In my experience it matches Bell and Eastlink. The customer service has been spectacular for me. That was the real winner. Try calling Bell and it’s a mess of being transferred and wait times… I found it so easy to communicate with Purple Cow customer service.",
              },

              {
                name: "Shelly Mills, Sackville Nova Scotia",
                rating: 5,
                description:
                  "Easy to set up. Quality is excellent! I wish I would have switched years ago!! Spent soooo much over the years!! My husband is happy because he can still watch all the sports he wants 👍🏻",
              },
              {
                name: "MJ Love, Windsor Nova Scotia",
                rating: 5,
                description:
                  "We set up internet at the cottage again this year and the process was very efficient. Modem arrived quickly, it was working on the day we asked for. Easy to contact them by text and have an answer quickly. Thank you very much for great customer service!",
              },
              {
                name: "Bill Red - Corner Brook Newfoundland",
                rating: 5,
                description:
                  "Recently activated internet services with Purple Cow. I must say I have been impressed, communication has been excellent , activation process was easy and you always feel supported throughout the process. Needless to say I’m impressed and will provide a further update once I’ve had an opportunity to test drive the TV services.",
              },
            ]}
          />
        </RoundedContainer>
        {/* Purple Cow's service is in Nova Scotia */}
        <RoundedContainer>
          <div className="flex flex-col lg:flex-row mt-10">
            <div className="w-full flex justify-center lg:w-[50%]">
              <img
                className="md:w-[60%] object-contain transition-all duration-150"
                src={mapImg}
                alt="modem"
              />
            </div>
            <div className="w-full flex flex-col justify-center lg:w-[50%]">
              <h1 className="!font-anton uppercase mt-5">
                Purple Cow's service is in Nova Scotia, Prince Edward Island &
                Newfoundland
              </h1>
              <h3 className="!font-sans text-[#111111] mt-5">
                We cover the majority of the above provinces. If you have any
                questions on serviceablity please check your address here online
                or shoot us a message.
              </h3>

              <CommonButtonInternet
                buttonText="Check My Eligibility"
                onClick={() => navigate("/join-the-herd")}
                className="last:mt-5 text-[#111111] px-5"
              />
            </div>
          </div>
        </RoundedContainer>
        {/* Common Questions */}
        <CommonQuestions
          textColor="black"
          bgColor="white"
          questions={homePageQuestions}
        />
        {/* Final Note. We Think You're Awesome */}
        <RoundedContainer
          className="flex flex-col justify-center items-center gap-5 sm:gap-10 p-5 sm:p-10 md:p-20"
          bgColor={colorTheme.MEDIUM_PURPLE}
        >
          <h1 className="!font-anton text-center text-white uppercase">
            Final Note. We Think You're <br className="hidden md:block" />
            Awesome
          </h1>
          <h3 className="!font-sans text-white text-center mt-5 lg:w-[60%]">
            If you still have a question, shoot us a message and our team will
            respond within mins
          </h3>

          <div className="flex justify-between gap-6">
            <CommonButtonInternet
              buttonText="Message Us"
              onClick={(e) => {
                const isMobile = /iPhone|iPad|iPod|Android/i.test(
                  navigator.userAgent
                )
                if (isMobile) {
                  e.preventDefault() // Prevent the default anchor click behavior
                  window.location.href =
                    "sms:**************?body=Hey%20Purple%20Cow%20I%20have%20a%20question.%20"
                } else {
                  // Open the existing popup on other devices
                  setMessagePopup(true)
                }
              }}
              className="px-5 !text-white border border-white"
              bgColor="transparent"
            />
            <CommonButtonInternet
              buttonText="Join the Herd"
              onClick={() => navigate("/join-the-herd")}
              className="px-5"
            />
          </div>
        </RoundedContainer>

        {/* Message Us popup */}
        {messagePopup && <MessageModal closepopup={setMessagePopup} />}
      </Layout>
    </div>
  )
}

export default FrontHomePage
