/**
 * Implement Gatsby's Browser APIs in this file.
 *
 * See: https://www.gatsbyjs.org/docs/browser-apis/
 */

import ReferralWrapper from "./src/ReferralWrapper"
import { navigate } from "gatsby"

import "./src/styles/global.css"
import "react-datepicker/dist/react-datepicker.css"
import "./src/styles/wpbr-public-main.css"
import "./src/styles/custome.css"
import "./src/styles/front-page.css"
import "./src/styles/responsive.css"
import "./src/styles/thoughts.css"

export const wrapRootElement = ReferralWrapper

export const onRouteUpdate = ({ location }) => {
  const session = JSON.parse(localStorage.getItem("authFlag"))
  const isValidSession =
    session && session?.valid && new Date().getTime() < session?.expires

  if (process.env.GATSBY_ENVIRONMENT === "DEV") {
    const isAuthenticated =
      typeof window !== "undefined" && localStorage.getItem("authFlag")

    if (
      (!isAuthenticated && location.pathname !== "/login") ||
      !isValidSession
    ) {
      navigate("/login")
    }
  }
}
