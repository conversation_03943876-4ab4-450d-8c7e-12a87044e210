import * as React from "react";

const IpPage: React.FC = () => {
  React.useEffect(() => {
    const fetchAndDownload = async () => {
      const response = await fetch('/geofeed.csv');
      const text = await response.text();

      const lines = text.split('\n').filter(
        line => line.trim() && !line.startsWith('#') && !line.startsWith('Prefix')
      );
      const ipCsv = lines.join('\n');
      const blob = new Blob([ipCsv], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'Geo_feed_Purple_cow_internet.csv';
      a.click();
      URL.revokeObjectURL(url);
    };

    fetchAndDownload();
  }, []);

  return (
    <h3>Purple Cow Internet's Geo Feed</h3>
  );
};

export default IpPage;
