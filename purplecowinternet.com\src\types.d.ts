declare module "*.svg" {
  const content: React.FunctionComponent<React.SVGAttributes<SVGElement>>
  export default content
}

declare module 'react-markup';
declare module '*.webp';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.mp4';

declare module "*.txt" {
  const content: string
  export default content
}

declare namespace Chargebee {
  const inited: boolean
  const init: (options: { site: string; publishableKey: string }) => void
}

declare module "@chargebee/chargebee-js-react-wrapper" {
  export const CardComponent: React.ComponentType<{
    styles?: {
      base: any
    }
    ref?: any
    fonts?: Array<string>
    children?: React.ReactNode; // Add this line
    onChange?: (e: {
      field: "expiry" | "cvv" | "number"
      error: string
    }) => void
  }>
  export const CardCVV: any
  export const CardNumber: any
  export const CardExpiry: any
}

declare namespace pca {
  class Eventable {
    constructor(source: any)

    listen: (event: string, action: any) => void
  }

  class Address extends Eventable {
    constructor(fields?: Address.Binding[], options?: Address.Options)
    destroy: () => void
  }

  const fieldMode: {
    COUNTRY: number
    DEFAULT: number
    NONE: number
    POPULATE: number
    PRESERVE: number
    SEARCH: number
  }

  enum FieldMode {
    COUNTRY = 8,
    DEFAULT = 3,
    NONE = 0,
    POPULATE = 2,
    PRESERVE = 4,
    SEARCH = 1,
  }

  namespace Address {
    interface Binding {
      element: string
      field: string
      mode: FieldMode
    }

    interface Options {
      key: string
      name?: string
      populate?: boolean
      onlyInputs?: boolean
      autoSearch?: boolean
      preselect?: boolean
      prompt?: boolean
      promptDelay?: number
      inlineMessages?: boolean
      setCursor?: boolean
      matchCount?: boolean
      minSearch?: number
      minItems?: number
      maxItems?: number
      manualEntry?: boolean
      manualEntryItem?: boolean
      disableTime?: number
      suppressAutocomplete?: boolean
      setCountryByIP?: boolean
      culture?: string
      languagePreference?: string
      filteringMode?: any // pca.filteringMode;
      orderingMode?: any // pca.orderingMode;
      countries?: any // pca.CountryList.Options;
      list?: any // pca.AutoComplete.Options;
      bar?: any // pca.Address.BarOptions;
      search?: any // pca.Address.SearchOptions;
    }
  }
}

type PeriodUnit = "not_applicable" | "month" | "year"

type MetaData = {
  showOnWebsite?: boolean
  website_title?: string
  priority?: number
  sale_price?: number
  type?: "tv"
  subtype: "channel" | "bundle"
  image_url: string
} & Bundle

type Bundle = {
  type: "tv"
  subtype: "bundle"
  channels: Array<string>
}

interface Addon {
  id: string
  name: string
  description: string
  pricing_model: "per_unit" | "flat_fee"
  charge_type: "non_recurring" | "recurring"
  price: number
  period_unit: PeriodUnit
  status: "active"
  currency_code: "CAD"
  taxable: true
  type: "quantity" | "non_recurring" | "recurring" | "on_off"
  period: number
  meta_data?: MetaData
  quantity?: any
}

interface Plan {
  id: string
  name: string
  price: number
  period: number
  period_unit: PeriodUnit
  pricing_model: "per_unit"
  setup_cost: number
  charge_model: "per_unit" | "flat_fee"
  quantity?: any
}

interface PlanList {
  status: any,
  message: string,
  data: any
}

interface IEstimate {
  invoice_estimate: {
    amount_due: number
    currency_code: string
    line_items: Array<LineItem>
    discounts: Array<{ amount: number; entity_id: string }>
    recurring: boolean
    round_off_amount: number
    sub_total: number
    taxes: [
      {
        amount: number
        description: string
        name: string
      }
    ]
    total: number
  }
}
