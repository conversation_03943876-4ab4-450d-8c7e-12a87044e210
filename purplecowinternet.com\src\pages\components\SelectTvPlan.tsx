import React, { useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { setFormData } from "../../redux/formSlice"
import { getAddonPlanById, getAllChannelsAvailable } from "../../customfunction"
import CommonButtonInternet from "../../components/CommonButton"
import { FibreHeading } from "../../components/FibreHeading"
import { colorTheme } from "../../data/SrtingConstants"

interface TvPlansProps {
  tvPlans: any
  addons: any
}
const SelectTvPlan: React.FC<TvPlansProps> = ({ tvPlans, addons }) => {
  const formData = useSelector((state: any) => state.form)
  const dispatch = useDispatch()
  const TvPlansList = tvPlans || []

  const getSubChannels = (id: any, type: any = "") => {
    setShowModal(true)
    setsubChannelsIcon(
      TvPlansList.filter((obj: any) => obj.id === id)[0]?.included_channels
    )
    if (type == "get") {
    }
  }

  let seletedTvPlan =
    formData?.tv_plan != null
      ? TvPlansList?.filter((obj: any) => obj?.id === formData?.tv_plan)[0]
      : null

  let optionalPlansIcons = formData?.OptionalPagePlansIcons

  const [showModal, setShowModal] = useState(false)
  const [subChannelsIcon, setsubChannelsIcon] = useState([])
  const handleClickTVPlan = async (e: any) => {
    if (formData?.tv_plan !== e?.id) {
      let seletedTvPlan =
        e?.id != null
          ? TvPlansList.filter((obj: any) => obj.id === e?.id)[0]
          : null
      dispatch(
        setFormData({
          tv_plan: Number(e.id),
          OptionalPagePlan: [
            ...(seletedTvPlan?.optional_extra_packages || []),
            ...(seletedTvPlan?.optional_iptv_products || []),
          ],
          OptionalPagePlansIcons: seletedTvPlan?.optional_single_channels,
          OptionalPagePlansIconsName: [],
          tv_optional_single_plans: [],
          tv_optional_single_plans_name: [],
        })
      )
    } else {
      let updatedFormData = {
        ...formData,
        tv_optional_plans: [],
        tv_optional_single_plans: [],
        tv_optional_single_plans_name: [],
      }
      let z = 0
      if (formData?.addons.length > 0) {
        let metaD: any = ""
        let allPlanData = formData?.addons
        let addonPlanObj: any = formData?.selectedAddonPlanData
        formData?.addons.forEach((item: any) => {
          metaD = getAddonPlanById(addons, item)
          if (metaD) {
            let typechanel = metaD?.meta_data?.is_tv_addon
            if (typeof typechanel !== "undefined" && typechanel === true) {
              allPlanData.splice(allPlanData.indexOf(item), 1)
              z = 0
              addonPlanObj.forEach((items: any) => {
                if (items.id === item) {
                  addonPlanObj.splice(z, 1)
                }
                z++
              })
            }
          }
        })
        updatedFormData.addons = allPlanData
        updatedFormData.selectedAddonPlanData = addonPlanObj
      }
      updatedFormData.tv_plan = null
      dispatch(setFormData(updatedFormData))
    }
  }
  const handleClickAdditionalTvPlan = async (id: number) => {
    if (formData?.tv_optional_plans?.includes(id)) {
      // remove if exists
      let newArr = formData?.tv_optional_plans.filter(
        (item: number) => item !== id
      )
      dispatch(setFormData({ tv_optional_plans: newArr }))
    } else {
      let newArr = [...formData?.tv_optional_plans, id]
      dispatch(setFormData({ tv_optional_plans: newArr }))
    }

    dispatch(
      setFormData({
        optionalPlanTrigger: Math.floor(Math.random() * 1000000000),
      })
    )
  }
  const handleClickAdditionalTvChannel = async (id: any) => {
    const value = Number(id)

    const existingIds = formData.tv_optional_single_plans || []
    const existingNames = formData.tv_optional_single_plans_name || []

    const apiName = seletedTvPlan?.optional_single_channels?.find(
      (obj: any) => obj.id === value
    )?.api_name

    let updatedIds = existingIds.includes(value)
      ? existingIds.filter((v: number) => v !== value)
      : [...existingIds, value]

    let updatedNames = existingNames.includes(apiName)
      ? existingNames.filter((n: string) => n !== apiName)
      : [...existingNames, apiName]

    // Deduplicate (if necessary)
    updatedIds = [...new Set(updatedIds)]
    updatedNames = [...new Set(updatedNames)]

    dispatch(
      setFormData({
        tv_optional_single_plans: updatedIds,
        tv_optional_single_plans_name: updatedNames,
        optionalPlanTrigger: Math.floor(Math.random() * 1000000000),
      })
    )
  }
  return (
    <div>
      <div className="flex flex-wrap justify-center gap-6 md:gap-9">
        {tvPlans?.map((tvPlan: any) => (
          <div
            key={tvPlan.id}
            className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
          >
            <div>
              <h2 className="!font-anton mb-2">{tvPlan?.name || ""}</h2>
              <div className="flex my-5">
                <h1 className="!font-anton">
                  ${tvPlan?.billing_period[0]?.monthly?.price || 0}
                </h1>
                <p className="text-lg flex items-end ml-2">/month</p>
              </div>
              <button
                onClick={() => getSubChannels(tvPlan?.id)}
                className="text-lg flex items-end mt-2 font-normal cursor-pointer"
              >
                View Channels
              </button>

              <div className="grid grid-cols-3 gap-2 mb-5">
                {tvPlan?.featured_channels?.map(
                  (channel: any, index: number) => (
                    <img
                      key={index}
                      src={channel?.image_url}
                      alt={channel?.name || "Channel"}
                      className={`w-[60px] h-[60px] object-contain`}
                    />
                  )
                )}
              </div>
              <p className="my-4 font-sans text-[#606060]">
                {tvPlan?.description}
              </p>
            </div>
            <div className="flex justify-center">
              <CommonButtonInternet
                className={`px-28 ${tvPlan?.id === formData?.tv_plan ? "font-bold" : ""}`}
                buttonText={tvPlan?.id === formData?.tv_plan ? "Remove" : "Add"}
                onClick={() => handleClickTVPlan(tvPlan)}
              />
            </div>
          </div>
        ))}
      </div>
      {/* show optional plan only if base tv plan selected */}
      {formData?.tv_plan && (
        <div>
          <FibreHeading
            className="text-center my-5 sm:my-10 md:my-16"
            align="text-center"
            color="white"
            content={"Add Additional Packs"}
          />
          <div className="flex flex-wrap justify-center gap-6 md:gap-9">
            {formData?.OptionalPagePlan?.filter((item: any) =>
              item.api_name.includes("EPKG")
            ).map((additionalPackage: any) => (
              <div
                key={additionalPackage.id}
                className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
              >
                <div>
                  <h2 className="!font-anton mb-2">
                    {additionalPackage?.name || ""}
                  </h2>
                  <div className="flex my-5">
                    <h1 className="!font-anton">
                      $
                      {additionalPackage?.billing_period[0]?.monthly?.price ||
                        0}
                    </h1>
                    <p className="text-lg flex items-end ml-2">/month</p>
                  </div>

                  <div className="grid grid-cols-3 gap-2 mb-5">
                    {additionalPackage?.included_channels?.map(
                      (channel: any, index: number) => (
                        <img
                          key={index}
                          src={channel.image_url}
                          alt={channel.name || "Channel"}
                          className={`w-[60px] h-[60px] object-contain`}
                        />
                      )
                    )}
                  </div>

                  <p className="my-4 font-sans text-[#606060]">
                    {additionalPackage?.description}
                  </p>
                </div>
                <div className="flex justify-center">
                  <CommonButtonInternet
                    className={`transition-all px-28 ${formData?.tv_optional_plans?.includes(additionalPackage?.id) ? "font-bold" : ""}`}
                    buttonText={
                      formData?.tv_optional_plans?.includes(
                        additionalPackage?.id
                      )
                        ? "Remove"
                        : "Add"
                    }
                    onClick={() => {
                      handleClickAdditionalTvPlan(additionalPackage?.id)
                    }}
                  />
                </div>
              </div>
            ))}
          </div>

          <FibreHeading
            className="text-center mt-10 sm:mt-10 md:mt-16"
            align="text-center"
            color="white"
            content={"Add Single Channels"}
          />

          <div className="flex justify-center text-white my-3">
            $4 each or five for $15/month
          </div>

          <div className="flex justify-center">
            <div className="flex justify-center gap-3 flex-wrap sm:max-w-xl md:max-w-2xl max-w-3xl">
              {optionalPlansIcons
                ?.filter(
                  (ch: any) =>
                    !getAllChannelsAvailable(formData, tvPlans)
                      .map((ch: any) => ch?.id)
                      .includes(ch?.id) &&
                    seletedTvPlan?.optional_single_channels
                      ?.map((ch: any) => ch?.id)
                      .includes(ch?.id)
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div
                      className={`w-[60px] h-[60px] bg-white p-1 rounded-lg flex justify-center cursor-pointer items-center ${
                        formData?.tv_optional_single_plans?.includes(item?.id)
                          ? "border-4"
                          : ""
                      }`}
                      style={{
                        borderColor:
                          formData?.tv_optional_single_plans?.includes(item?.id)
                            ? colorTheme.GOLD
                            : "transparent",
                      }}
                      key={"opt-" + index}
                      onClick={() => handleClickAdditionalTvChannel(item?.id)}
                    >
                      <img
                        src={item.image_url}
                        alt={item?.name || "Channel"}
                        className={`object-contain`}
                      />
                    </div>
                  )
                })}
            </div>
          </div>

          <FibreHeading
            className="text-center mt-5 sm:mt-10 md:mt-16"
            align="text-center"
            color="white"
            content={"Record Shows"}
          />

          <div className="flex flex-wrap justify-center gap-6 md:gap-9 mt-8">
            {formData?.OptionalPagePlan?.filter((item: any) =>
              item.api_name.includes("IPTV")
            ).map((additionalPackage: any) => (
              <div
                key={additionalPackage.id}
                className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
              >
                <div>
                  <h2 className="!font-anton mb-2">
                    {additionalPackage?.name || ""}
                  </h2>
                  <div className="flex my-5">
                    <h1 className="!font-anton">
                      $
                      {additionalPackage?.billing_period[0]?.monthly?.price ||
                        0}
                    </h1>
                    <p className="text-lg flex items-end ml-2">/month</p>
                  </div>

                  <p className="my-4 font-sans text-[#606060]">
                    {additionalPackage?.description}
                  </p>
                </div>
                <div className="flex justify-center">
                  <CommonButtonInternet
                    className={`transition-all px-28 ${formData?.tv_optional_plans?.includes(additionalPackage?.id) ? "font-bold" : ""}`}
                    buttonText={
                      formData?.tv_optional_plans?.includes(
                        additionalPackage?.id
                      )
                        ? "Remove"
                        : "Add"
                    }
                    onClick={() => {
                      handleClickAdditionalTvPlan(additionalPackage?.id)
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      {showModal && (
        <>
          <div className="justify-center items-center flex overflow-x-hidden overflow-y-auto fixed inset-0 z-50 outline-none focus:outline-none">
            <div className="relative w-auto my-6 mx-auto max-w-3xl">
              <div className="border-0 rounded-lg shadow-lg relative flex flex-col w-full bg-white outline-none focus:outline-none">
                <button
                  className="ml-auto mt-0 bg-transparent border-0 text-black leading-none outline-none focus:outline-none"
                  onClick={() => setShowModal(false)}
                >
                  <span className="bg-transparent opacity-25 text-black h-6 w-6 text-4xl block outline-none focus:outline-none mr-2">
                    ×
                  </span>
                </button>
                <div className="text-center">
                  <h4 className="text-1xl text-gray-600">Included Channels</h4>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="#47525E"
                    className="w-6 h-6 m-auto cursor-pointer"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19.5 8.25l-7.5 7.5-7.5-7.5"
                    />
                  </svg>
                </div>
                <div className="relative p-6 chennel-modal-parent">
                  <div className="chennels-box">
                    {subChannelsIcon.map((itemMain: any, _indexmains: any) => {
                      return (
                        <div
                          className="chennels-box__icon"
                          key={_indexmains + "s"}
                        >
                          <img src={itemMain.image_url}></img>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="opacity-25 fixed inset-0 z-40 bg-black"></div>
        </>
      )}
    </div>
  )
}

export default SelectTvPlan
