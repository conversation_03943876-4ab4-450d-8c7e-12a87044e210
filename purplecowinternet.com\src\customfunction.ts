import { formatMoney } from "./utils"

export function getMonthlyAndSingleTimeInternetPlan(plans: any, formData: any) {
  if (plans) {
    let plansData = getPlanData(plans)
    let showstatus: any = ""
    let position: any = ""
    let id: any = ""
    let Service_Area: any = ""
    let allPlans = plansData.filter((item: any) => {
      showstatus = item?.plan?.meta_data?.showOnWebsite
      Service_Area = item?.plan?.meta_data?.Service_Area
      position = item?.plan?.meta_data?.position
      id = item.plan.id.toLowerCase()
      if (
        id.indexOf("_yearly") < 0 &&
        id.indexOf("-yearly") < 0 &&
        showstatus == true &&
        typeof position !== "undefined" &&
        Service_Area == formData?.Service_Area
      ) {
        return item
      }
    })
    if (allPlans) {
      allPlans.sort(function (a: any, b: any) {
        return a.plan?.meta_data?.position - b.plan?.meta_data?.position
      })
    }
    return allPlans ? allPlans : []
  }
  return []
}

export function getMonthlyAndSingleTimeAddonPlan(plans: any, type: any) {
  if (plans) {
    let plansData = getPlanData(plans)
    let showstatus: any = ""
    let id: any = ""
    let plan_type = ""
    let position: any = ""
    let allPlans = plansData.filter((item: any) => {
      showstatus = item?.addon?.meta_data?.showOnWebsite
      plan_type = item?.addon?.meta_data?.plan_type
      id = item.addon.id.toLowerCase()
      position = item?.addon?.meta_data?.position
      if (type == "tv") {
        if (
          id.indexOf("_yearly") < 0 &&
          id.indexOf("-yearly") < 0 &&
          showstatus == true &&
          plan_type == "tv" &&
          typeof position !== "undefined"
        ) {
          return item
        }
      } else {
        if (
          id.indexOf("_yearly") < 0 &&
          id.indexOf("-yearly") < 0 &&
          showstatus == true &&
          plan_type != "additional_tv_package" &&
          plan_type != "tv" &&
          typeof position !== "undefined"
        ) {
          return item
        }
      }
    })
    if (allPlans) {
      allPlans.sort(function (a: any, b: any) {
        return a.addon?.meta_data?.position - b.addon?.meta_data?.position
      })
    }
    return allPlans ? allPlans : []
  }
  return []
}

export function getInternetPlanById(plans: any, id: any) {
  if (plans) {
    let plansData = getPlanData(plans)
    let allPlans = plansData.filter((item: any) => item.id == id)
    return allPlans.length ? allPlans[0].plan : []
  }
  return []
}

export function getAddonPlanById(plans: any, id: any) {
  if (plans) {
    let allPlans =
      plans?.length > 0 && plans?.filter((item: any) => item?.id == id)
    return allPlans ? allPlans[0]?.addon : []
  }
  return []
}

export function getPlanData(plans: any) {
  if (plans) {
    return plans
  }
  return []
}

export function defaultFormData() {
  return {
    isUpdateCart: 1,
    formData: 0,
    shipping_address: {
      Line1: "",
      SubBuilding: "",
      City: "",
      Province: "",
      PostalCode: "",
    },
    mailing_address: {
      Line1: "",
      SubBuilding: "",
      City: "",
      Province: "",
      PostalCode: "",
    },
    plan_id: null,
    addons: [],
    optional_addons: [],
    Service_Area: "",
    tv_plan: null,
    tv_optional_plans: [],
    tv_optional_single_plans: [],
    tv_optional_single_plans_name: [],
    selectedAddonPlanData: [],
    is_mailing: true,
    is_new_phone: true,
    is_existing_phone: false,
    is_business: false,
    customer: {
      first_name: "",
      last_name: "",
      port_number: "",
      phone: "",
      email: "",
      cf_suggested_turn_on_date: "",
      cf_company_name: "",
    },
    total: 0,
    SA_SFRecordID: "",
    monthly_yearly: "M",
    monthEstimate: null,
    yearEstimate: null,
    optionalPlanTrigger: 0,
    OptionalPagePlan: [],
    OptionalPagePlansIcons: [],
    OptionalPagePlansIconsName: [],
    optionaPlansIcons: [],
    hideTvPlan: "",
    selectedItems: {
      internet: true,
      tv: false,
      home_phone: false,
      mobile: false,
    },
  }
}

export function getSelectedPlanData(
  formData: any,
  plans: any,
  addons: any,
  dtype: any
) {
  let plan_id: any = ""
  let allAddons: any = []
  let planNames: any = []
  let additionalAddons: any = []
  let optionalAddons: any = []
  let planInternetdata = plans?.internet?.filter(
    (p: any) => p.id === formData?.plan_id
  )[0]
  let planTVdata =
    formData.tv_plan != null
      ? plans.tv.filter((p: any) => p.id === formData?.tv_plan)[0]
      : null
  if (dtype == "Y") {
    plan_id = planInternetdata?.billing[0]?.yearly?.api_name
    planNames.push(plan_id)
    if (formData.tv_plan != null) {
      const tvApiName = planTVdata?.billing_period[0]?.yearly?.api_name
      allAddons.push({ id: tvApiName })
      planNames.push(tvApiName)
      let extraTVPackages = formData.tv_optional_plans
        ?.filter((p: any) =>
          planTVdata.optional_extra_packages.map((p: any) => p.id).includes(p)
        )
        .map(
          (p: any) =>
            addons?.tv?.filter((a: any) => a.id === p)[0].billing_period[0]
              .yearly.api_name
        )
      let extraIPTVPackages = formData.tv_optional_plans
        ?.filter((p: any) =>
          planTVdata.optional_iptv_products.map((p: any) => p.id).includes(p)
        )
        .map(
          (p: any) =>
            addons.tv.filter((a: any) => a.id === p)[0].billing_period[0].yearly
              .api_name
        )
      extraTVPackages.forEach((p: any) => {
        optionalAddons.push({ id: p })
      })
      extraIPTVPackages.forEach((p: any) => {
        allAddons.push({ id: p })
        planNames.push(p)
      })
      const pick5Qty = Math.floor(formData.tv_optional_single_plans.length / 5)
      if (pick5Qty > 0) {
        optionalAddons.push({ id: "yearly_X_PICK_5", quantity: pick5Qty })
      }
      const singleQty = formData.tv_optional_single_plans.length % 5
      if (singleQty > 0) {
        optionalAddons.push({
          id: "yearly_X_SNGL_CHANNEL",
          quantity: singleQty,
        })
      }
    }
    let extraOptionalAddonsData = [...plans.homePhone, ...addons.misc]
    for (const addon of formData.addons) {
      let data = extraOptionalAddonsData.filter(
        (p) =>
          ("monthly" in p.billing_period[0]
            ? p.billing_period[0].monthly.api_name
            : p.billing_period[0].non_recurring.api_name) === addon
      )[0]
      if ("yearly" in data.billing_period[0]) {
        allAddons.push({ id: data.billing_period[0].yearly.api_name })
        planNames.push(data.billing_period[0].yearly.api_name)
      } else {
        allAddons.push({ id: data.billing_period[0].non_recurring.api_name })
        planNames.push(data.billing_period[0].non_recurring.api_name)
      }
    }
    addons.chargebee
      .filter(
        (a: any) =>
          "meta_data" in a.addon &&
          "additional_channel" in a.addon.meta_data &&
          planNames.includes(
            a.addon.meta_data.additional_channel.replace("monthly_", "yearly_")
          )
      )
      .forEach((a: any) => {
        additionalAddons.push({ id: a.addon.id })
      })
  } else {
    plan_id = planInternetdata?.billing[0]?.monthly?.api_name
    planNames.push(plan_id)
    if (formData.tv_plan != null) {
      const tvApiName = planTVdata?.billing_period[0]?.monthly.api_name
      allAddons.push({ id: tvApiName })
      planNames.push(tvApiName)
      let extraTVPackages = formData.tv_optional_plans
        ?.filter((p: any) =>
          planTVdata?.optional_extra_packages?.map((p: any) => p.id).includes(p)
        )
        .map(
          (p: any) =>
            addons.tv.filter((a: any) => a.id === p)[0].billing_period[0]
              .monthly.api_name
        )
      let extraIPTVPackages = formData.tv_optional_plans
        ?.filter((p: any) =>
          planTVdata?.optional_iptv_products?.map((p: any) => p.id).includes(p)
        )
        .map(
          (p: any) =>
            addons.tv.filter((a: any) => a.id === p)[0].billing_period[0]
              .monthly.api_name
        )
      extraTVPackages.forEach((p: any) => {
        optionalAddons.push({ id: p })
      })
      extraIPTVPackages.forEach((p: any) => {
        allAddons.push({ id: p })
        planNames.push(p)
      })
      const pick5Qty = Math.floor(formData.tv_optional_single_plans.length / 5)
      if (pick5Qty > 0) {
        optionalAddons.push({ id: "monthly_X_PICK_5", quantity: pick5Qty })
      }
      const singleQty = formData.tv_optional_single_plans.length % 5
      if (singleQty > 0) {
        optionalAddons.push({
          id: "monthly_X_SNGL_CHANNEL",
          quantity: singleQty,
        })
      }
    }
    let extraOptionalAddonsData = [
      ...(plans.homePhone || []),
      ...(addons.misc || []),
    ]
    for (const addon of formData.addons) {
      let data = extraOptionalAddonsData.filter(
        (p) =>
          ("monthly" in p.billing_period[0]
            ? p.billing_period[0].monthly.api_name
            : p.billing_period[0].non_recurring.api_name) === addon
      )[0]
      if ("monthly" in data.billing_period[0]) {
        allAddons.push({ id: data.billing_period[0].monthly.api_name })
        planNames.push(data.billing_period[0].monthly.api_name)
      } else {
        allAddons.push({ id: data.billing_period[0].non_recurring.api_name })
        planNames.push(data.billing_period[0].non_recurring.api_name)
      }
    }

    for (const addon of formData?.optional_addons) {
      let data = extraOptionalAddonsData.filter(
        (p) =>
          ("monthly" in p.billing_period[0]
            ? p.billing_period[0].monthly.api_name
            : p.billing_period[0].non_recurring.api_name) === addon
      )[0]
      if ("monthly" in data.billing_period[0]) {
        allAddons.push({ id: data.billing_period[0].monthly.api_name })
        planNames.push(data.billing_period[0].monthly.api_name)
      } else {
        allAddons.push({ id: data.billing_period[0].non_recurring.api_name })
        planNames.push(data.billing_period[0].non_recurring.api_name)
      }
    }
    addons?.chargebee
      ?.filter(
        (a: any) =>
          "meta_data" in a.addon &&
          "additional_channel" in a.addon.meta_data &&
          planNames.includes(a.addon.meta_data.additional_channel)
      )
      .forEach((a: any) => {
        additionalAddons.push({ id: a.addon.id })
      })
  }

  return {
    internet_plan_id: plan_id,
    all_addons: allAddons,
    plan_name: planNames.join(", "),
    additional_plans: additionalAddons,
    optional_plans: optionalAddons,
  }
}

export function getReminder(dividend: any, divisor: any) {
  let quotient = Math.floor(dividend / divisor)
  let remainder = dividend % divisor
  return {
    quotient: quotient,
    remainder: remainder,
  }
}

export function getSelectedPlanData_old(
  fordata: any,
  plan: any,
  addon: any,
  dtype: any
) {
  let plan_id: any = ""
  let allAddons: any = []
  let pdata: any = ""
  let pdetails: any = ""
  let qty: any = ""
  let qtyexist: any = 1
  let planName: any = []
  let planInternetdata
  if (dtype == "Y") {
    plan_id = getYearlyInternetPlanById(plan, fordata.plan_id)
    planInternetdata = getInternetPlanById(plan, plan_id)
    planName.push(planInternetdata.id)
    if (fordata.tv_plan != null) {
      pdata = getYearlyAddonPlanById(addon, fordata.tv_plan)
      if (pdata) {
        pdetails = getAddonPlanById(addon, pdata)
        if (pdetails) {
          if (pdetails.type != "on_off") {
            allAddons.push({ id: pdetails.id, quantity: 1 })
          } else {
            allAddons.push({ id: pdetails.id })
          }
          planName.push(pdetails.id)
        }
      }
    }
    if (fordata.selectedAddonPlanData.length > 0) {
      for (let k = 0; k < fordata.selectedAddonPlanData.length; k++) {
        qty = fordata.selectedAddonPlanData[k]?.quantity
        if (typeof qty !== "undefined" && typeof qty !== "undefined") {
          qtyexist = qty
        }
        pdata = getYearlyAddonPlanById(
          addon,
          fordata.selectedAddonPlanData[k].id
        )
        if (pdata) {
          pdetails = getAddonPlanById(addon, pdata)
          if (pdetails) {
            if (pdetails.type != "on_off") {
              allAddons.push({ id: pdetails.id, quantity: qtyexist })
            } else {
              allAddons.push({ id: pdetails.id })
            }
            planName.push(pdetails.id)
          }
        }
      }
    }
  } else {
    plan_id = fordata.plan_id
    planInternetdata = getInternetPlanById(plan, plan_id)
    planName.push(planInternetdata.id)
    if (fordata.tv_plan != null) {
      pdetails = getAddonPlanById(addon, fordata.tv_plan)
      if (pdetails) {
        if (pdetails.type != "on_off") {
          allAddons.push({ id: pdetails.id, quantity: 1 })
        } else {
          allAddons.push({ id: pdetails.id })
        }
        planName.push(pdetails.id)
      }
    }
    if (fordata.selectedAddonPlanData.length > 0) {
      for (let k = 0; k < fordata.selectedAddonPlanData.length; k++) {
        qty = fordata.selectedAddonPlanData[k]?.quantity
        if (typeof qty !== "undefined" && typeof qty !== "undefined") {
          qtyexist = qty
        }
        pdetails = getAddonPlanById(addon, fordata.selectedAddonPlanData[k].id)
        if (pdetails) {
          if (pdetails.type != "on_off") {
            allAddons.push({ id: pdetails.id, quantity: qtyexist })
          } else {
            allAddons.push({ id: pdetails.id })
          }
          planName.push(pdetails.id)
        }
      }
    }
  }

  return [plan_id, allAddons, planName.join(", ")]
}

export function getYearlyInternetPlanById(plans: any, id: any) {
  let allPlans = []
  let orgid = id
  if (plans) {
    let plansData = getPlanData(plans)
    let planDetails = getInternetPlanById(plans, id)
    id = planDetails?.id.replaceAll("monthly_", "").toLowerCase()
    allPlans = plansData.filter((item: any) => {
      if (item.plan.id.toLowerCase() == "yearly_" + id) {
        return item
      }
    })
    return allPlans.length > 0 ? allPlans[0]?.plan?.id : orgid
  }
  return ""
}

export function getYearlyAddonPlanById(plans: any, id: any) {
  let allPlans = []
  let orgid = id
  let planDetails = getAddonPlanById(plans, id)
  if (plans) {
    let plansData = getPlanData(plans)
    id = planDetails?.id.replaceAll("monthly_", "").toLowerCase()
    allPlans = plansData.filter((item: any) => {
      if (item.addon.id.toLowerCase() == "yearly_" + id) {
        return item
      }
    })
    if (planDetails.charge_type == "non_recurring") {
      return allPlans.length > 0 ? allPlans[0]?.addon?.id : orgid
    } else {
      return allPlans.length > 0 ? allPlans[0]?.addon?.id : orgid
    }
  }
  return ""
}

export function calculateYearlyPrice(price: any) {
  let prices: any = (price * 12 * 0.9) / 12
  return prices.toFixed(2).replaceAll(".00", "")
}

export function getPrice(
  addon: Addon | Plan,
  withqty: any = null,
  formData: any = null
) {
  if (formData?.tv_plan != null && addon?.id == "monthly_X_SNGL_CHANNEL") {
    if (formData?.tv_optional_single_plans.length > 0) {
      let remider: any = getReminder(
        formData?.tv_optional_single_plans.length,
        5
      )
      let totalchannelPrice: any = (addon.price / 100) * remider?.remainder
      return addon.period_unit == "month"
        ? `${formatMoney(totalchannelPrice)} / month`
        : `${formatMoney(totalchannelPrice / 12)} / ${addon.period_unit}`
    }
  }
  if (formData?.tv_plan != null && addon?.id == "monthly_X_PICK_5") {
    if (formData?.tv_optional_single_plans.length > 0) {
      let remider: any = getReminder(
        formData?.tv_optional_single_plans.length,
        5
      )
      let totalchannelPrice: any = (addon.price / 100) * remider?.quotient
      return addon.period_unit == "month"
        ? `${formatMoney(totalchannelPrice)} / month`
        : `${formatMoney(totalchannelPrice / 12)} / ${addon.period_unit}`
    }
  }

  let qtys = 0
  if ("charge_type" in addon && addon.charge_type === "non_recurring") {
    return `${formatMoney(addon.price / 100)}`
  } else {
    if (addon.period === 1) {
      if (withqty !== null) {
        qtys = addon?.quantity
        if (typeof qtys !== "undefined") {
          return addon.period_unit != "month"
            ? `${formatMoney((addon.price * qtys) / 100 / 12)} / month`
            : `${formatMoney((addon.price * qtys) / 100)} / ${
                addon.period_unit
              }`
        }
      }
      return addon.period_unit != "month"
        ? `${formatMoney(addon.price / 100 / 12)} / month`
        : `${formatMoney(addon.price / 100 / 1)} / ${addon.period_unit}`
    } else {
      qtys = addon?.quantity
      if (typeof qtys !== "undefined") {
        return addon.period_unit != "month"
          ? `${formatMoney((addon.price * qtys) / 100 / 12)} / ${
              addon.period
            } months}`
          : `${formatMoney((addon.price * qtys) / 100)} / ${addon.period} ${
              addon.period_unit
            }s}`
      }
      return addon.period_unit != "month"
        ? `${formatMoney(addon.price / 100 / 12)} / ${addon.period} months}`
        : `${formatMoney(addon.price / 100)} / ${addon.period} ${
            addon.period_unit
          }s}`
    }
  }
}

export function getMonthlyPlanPriceByYearlyId(
  monthlyplans: any,
  addonandplans: any,
  yearly_plan_id: any
) {
  let recurring: any = monthlyplans?.invoice_estimate?.line_items.filter(
    ({ date_from, date_to }: any) => date_from !== date_to
  )
  if (recurring.length) {
    let planid: any = []
    recurring.map((item: any) => {
      if (item?.entity_id) {
        let plnid = item?.entity_id.replace("monthly_", "")
        let yearly_plan_ids = yearly_plan_id.replace("yearly_", "")
        if (plnid == yearly_plan_ids) {
          planid.push(item?.entity_id)
        }
      }
    })
    if (planid.length > 0) {
      if (planid[0] == yearly_plan_id) {
        return ""
      }
      let price: any = []
      addonandplans.map((item: any) => {
        if (item?.id == planid[0]) {
          price.push(
            (item?.price / 100).toLocaleString("en-US", {
              style: "currency",
              currency: "USD",
            })
          )
        }
      })
      if (price.length > 0) {
        return price[0]
      }
    }
  }
  return ""
}

export function transformReferralCodeToName(referrals: string[]) {
  return referrals
    .map((referral) => referral.split("-").slice(0, -1).join(" "))
    .join(", ")
}

export function getAllTvOptionalPlan(singleplans: any, allAddons: any) {
  let meta_data: any = {}
  let parentPlans: any = ""
  let plns = allAddons?.data?.list.filter((item: any) => {
    meta_data = item?.addon?.meta_data
    parentPlans = meta_data?.Parent
    parentPlans =
      typeof parentPlans !== undefined
        ? parentPlans
        : parentPlans.split(",")
          ? []
          : []
    if (
      typeof meta_data !== "undefined" &&
      meta_data?.plan_type == "additional_tv_package" &&
      parentPlans.indexOf(singleplans?.id) !== -1 &&
      (meta_data?.showOnWebsite == true || meta_data?.showOnWebsite == "true")
    ) {
      return item
    }
  })

  plns.sort(function (positionA: any, positionB: any) {
    if (positionA !== undefined && positionB !== undefined) {
      return positionA - positionB
    } else if (positionA === undefined && positionB === undefined) {
      return 0
    }
  })

  return plns
}

export function getMetaDataValue(value: any, key: any) {
  if (typeof value != "undefined") {
    return typeof value[key] !== "undefined" ? value[key] : ""
  }
}

export function getOptionalChannlesPrice(formData: any, addons: any = null) {
  if (formData?.tv_plan != null) {
    let totalplan: any = formData?.tv_optional_single_plans.length
    let fivepackprice = 0
    let singlepackprice = 0
    if (addons != null) {
      let firvpack = addons.filter((item: any) => {
        if (item?.api_name == "monthly_X_PICK_5") {
          return item
        }
      })
      let singlepack = addons.filter((item: any) => {
        if (item?.api_name == "monthly_X_SNGL_CHANNEL") {
          return item
        }
      })
      singlepackprice = singlepack.length > 0 ? singlepack[0]?.addon?.price : 0
      fivepackprice = firvpack.length > 0 ? firvpack[0]?.addon?.price : 0
    }

    let remider: any = getReminder(totalplan, 5)
    let totalprice = 0
    if (remider?.quotient > 0) {
      totalprice = remider?.quotient * (fivepackprice / 100)
    }
    if (remider?.remainder) {
      totalprice += remider?.remainder * (singlepackprice / 100)
    }
    return totalprice
  }
  return 0
}

export function getAllChannelsAvailable(formData: any, tvPlans: any) {
  let tvPlan =
    formData?.tv_plan != null
      ? tvPlans?.filter((obj: any) => obj.id === formData?.tv_plan)[0]
      : null
  let tvAddons = formData.tv_optional_plans.map(
    (id: any) =>
      [
        ...tvPlan.optional_extra_packages,
        ...tvPlan.optional_iptv_products,
      ].filter((obj) => obj.id === id)[0]
  )
  let allChannels = [...tvPlan?.included_channels]
  const allChannelsSet: Set<any> = new Set(allChannels)

  for (const addon of tvAddons) {
    for (const ch of addon?.included_channels) {
      allChannelsSet.add(ch)
    }
  }
  return Array.from(allChannelsSet)
}

export function resetMainUrl() {
  var url = window.location.href
  var newUrl = url.split("?")[0]
  window.history.pushState({}, "", newUrl)
}
