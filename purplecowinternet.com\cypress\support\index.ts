/// <reference types="cypress" />
/// <reference types="@testing-library/cypress" />

import { Customer, Subscription } from "chargebee-typescript/lib/resources"
import "cypress-iframe"
import { Address } from "../../src/addressValidator"

interface CustomCustomer extends Customer {
  cf_mailing_line1: string
  cf_mailing_line2: string
  cf_mailing_line3: string
  cf_mailing_zip: string
  cf_mailing_company: string
  cf_suggested_turn_on_date: string
  cf_mailing_state_code: string
  cf_mailing_country: string
}

export type CustomerDetails = {
  address: string
  firstName: string
  lastName: string
  postal: string
  phoneNumber: string
  email: string
  suggestedTurnOnDate: string
  cardNumber: string
  cvv: string
  expiry: string
  company?: string
  mailingAddress?: string
  mailingPostal?: string
  addressType?: string
  Line1?: string
  Apt?: string
  PostalCode?: string
  City?: string
  Province?: string
}

let provinceMap = {
  novascotia: "NS",
  newbrunswick: "NB",
  princeedwardisland: "PE",
  newfoundlandandlabrador: "NL",
  newfoundland: "NL",
}

function provinceTransformer(province) {
  return (
    provinceMap[province.toLowerCase().replace(/ /g, "")] ||
    province.toUpperCase()
  )
}

declare global {
  namespace Cypress {
    interface Chainable<Subject> {
      checkout(
        customer: CustomerDetails,
        completeCheckout?: boolean
      ): Chainable<any>
    }
  }
}

declare global {
  namespace Cypress {
    interface Chainable<Subject> {
      checkCheckout(
        customer: CustomerDetails,
        subscriptionsExpected?: number
      ): Chainable<any>
      address(
        address: string,
        postal: string,
        placeholder: string | RegExp
      ): Chainable<any>
      manualAddress(
        placeholder: string | RegExp,
        address: {
          Line1: string
          Apt: string
          City: string
          PostalCode: string
          Province: string
        }
      ): Chainable<any>
    }
  }
}

const address = (address, postal, placeholder) => {
  cy.intercept({
    method: "GET",
    url: "http://ws1.postescanada-canadapost.ca/AddressComplete/Interactive/RetrieveFormatted/v2.10/json3ex.ws**",
  }).as("addressRetrieved")

  cy.intercept({
    method: "GET",
    url: "http://ws1.postescanada-canadapost.ca/AddressComplete/Interactive/Find/v2.10/json3ex.ws**",
  }).as("addressRequested")

  cy.findByPlaceholderText(placeholder).click()
  cy.get(".pcaautocomplete").siblings(":visible")

  cy.findByPlaceholderText(placeholder).type(address, {
    delay: 50,
  })
  // cy.wait(1000)

  // cy.wait(500)

  // cy.findByPlaceholderText(/Service Address/i).type(customer.address.slice(1))
  // cy.findByPlaceholderText(/Service Address/i).type(customer.address[0])
  // cy.wait(1000)

  // // cy.wait(500)

  // cy.findByPlaceholderText(/Service Address/i).type(customer.address.slice(1))

  cy.get("@addressRequested.all")
  cy.wait("@addressRequested")

  // Forcing here is super hacky but if it works...
  cy.findByText(postal, { exact: false }).click({ force: true })
  return cy.wait("@addressRetrieved")
}

const manualAddress = (
  placeholder,
  { Line1, Apt, PostalCode, City, Province }
) => {
  cy.findByPlaceholderText(placeholder).type(Line1)
  cy.findByPlaceholderText(/Apartment/i)
    .click()
    .type(Apt)
  cy.findByPlaceholderText(/city/i).type(City, { force: true })
  cy.findByPlaceholderText(/postal/i).type(PostalCode, { force: true })
  cy.findByPlaceholderText(/province/i).type(Province, { force: true })

  return cy.wrap({
    response: {
      body: {
        Items: [
          {
            Province: provinceTransformer(Province),
            City,
            SubBuilding: Apt,
            PostalCode,
            Line1: Apt ? `${Apt}-${Line1}` : Line1,
            CountryIso2: "CA",
          },
        ],
      },
    },
  })
}

const checkout = (
  customer: CustomerDetails,
  completeCheckout: boolean = true
) => {
  cy.visit("/join-the-herd/")

  cy.intercept({
    method: "POST",
    url: Cypress.env("LAMBDA_URL") + "tokenized-subscription",
  }).as("checkoutComplete")

  if (customer.addressType === "manual") {
    cy.manualAddress(/Service Address/i, {
      Line1: customer.Line1,
      Apt: customer.Apt,
      PostalCode: customer.PostalCode,
      City: customer.City,
      Province: customer.Province,
    }).as("serviceAddress")
  } else {
    cy.address(customer.address, customer.postal, /Service Address/i).as(
      "serviceAddress"
    )
  }

  cy.findByText(/next/i).click({ force: true })

  // Information Screen
  cy.findByPlaceholderText(/First Name/i).type(customer.firstName)
  cy.findByPlaceholderText(/Last Name/i).type(customer.lastName)
  cy.findByPlaceholderText(/Phone Number/i).type(customer.phoneNumber)
  cy.findByPlaceholderText(/Email/i).type(customer.email)
  cy.findByPlaceholderText(/Suggested Turn on Date/i).type(
    customer.suggestedTurnOnDate
  )

  if (customer.mailingAddress) {
    cy.findByText(/Mailing and Service Address are the same./i).click()

    cy.address(
      customer.mailingAddress,
      customer.mailingPostal,
      /Mailing Address/i
    ).as("mailingAddress")
  } else {
    cy.wrap({}).as("mailingAddress")
  }

  if (customer.company) {
    cy.findByText(/is this a business location/i).click()
    cy.findByPlaceholderText(/Business Name/i).type(customer.company)
  }

  cy.findByText("Next").click()

  // Addons
  cy.findByText(/next/i).click()

  // cb-component-number-0
  // cb-component-expiry-1
  // cb-component-cvv-2

  // Payment
  cy.findByPlaceholderText(/First Name/i).type(customer.firstName)
  cy.findByPlaceholderText(/Last Name/i).type(customer.lastName)

  cy.iframe("iframe[name='cb-component-number-0']")
    .findByPlaceholderText(/Card Number/i)
    .type(customer.cardNumber)

  cy.iframe("iframe[name=cb-component-expiry-1]")
    .findByPlaceholderText(/MM \/ YY/i)
    .type(customer.expiry)

  cy.iframe("iframe[name=cb-component-cvv-2]")
    .findByPlaceholderText(/CVV/i)
    .type(customer.cvv)

  cy.findByPlaceholderText(/Postal Code/i).type(customer.postal)
  cy.findByText(/agree to/i).click()

  if (completeCheckout) {
    cy.findByText(/Join the Herd/i).click()

    cy.wait("@checkoutComplete", { timeout: 15000 })

    return cy.checkCheckout(customer)
  }
}

function checkCheckout(customer: CustomerDetails, subscriptionsExpected) {
  return cy
    .task("getChargebeeUserAndSubscriptions", customer.email)
    .then(
      (result: {
        customer: CustomCustomer
        subscriptions: Array<Subscription>
      }) => {
        let { customer: chargeBeeCustomer, subscriptions } = result
        //@ts-ignore
        let serviceAddress: Address
        cy.get("@serviceAddress")
          .then((tempServiceAddress) => {
            //@ts-ignore
            serviceAddress = tempServiceAddress.response.body.Items[0]
            return cy.get("@mailingAddress")
          })
          //@ts-ignore
          .then((tempMailingAddress) => {
            let mailingAddress: Address =
              //@ts-ignore
              tempMailingAddress.response?.body?.Items?.[0] ?? {}
            let subscription = subscriptions[0]

            if (subscriptionsExpected) {
              expect(subscriptions.length).to.equal(subscriptionsExpected)
            }

            const expectedFields = {
              email: customer.email,
              firstName: customer.firstName,
              lastName: customer.lastName,
              // @ts-ignore
              company: customer.company,
              // @ts-ignore
              suggestedTurnOnDate: customer.suggestedTurnOnDate,
              phoneNumber: customer.phoneNumber,

              serviceLine1: serviceAddress.Line1 || undefined,
              serviceLine2: serviceAddress.Line2 || undefined,
              serviceLine3: serviceAddress.Line3 || undefined,
              servicePostalCode: serviceAddress.PostalCode || undefined,
              serviceProvince: serviceAddress.Province || undefined,
              serviceCountryIso2: serviceAddress.CountryIso2 || undefined,
              serviceCompany: serviceAddress.Company || undefined,

              mailingLine1:
                mailingAddress.Line1 || serviceAddress.Line1 || undefined,
              mailingLine2:
                mailingAddress.Line2 || serviceAddress.Line2 || undefined,
              mailingLine3:
                mailingAddress.Line3 || serviceAddress.Line3 || undefined,
              mailingPostalCode:
                mailingAddress.PostalCode ||
                serviceAddress.PostalCode ||
                undefined,
              mailingProvince:
                mailingAddress.Province ||
                serviceAddress.Province ||
                undefined,
              mailingCountryIso2:
                mailingAddress.CountryIso2 ||
                serviceAddress.CountryIso2 ||
                undefined,
              mailingCompany:
                mailingAddress.Company || serviceAddress.Company || undefined,
            }

            let tokens = chargeBeeCustomer.cf_suggested_turn_on_date.split("-")

            let actualFields = {
              email: chargeBeeCustomer.email,
              firstName: chargeBeeCustomer.first_name,
              lastName: chargeBeeCustomer.last_name,
              // @ts-ignore
              company: chargeBeeCustomer.cf_company_name,
              // @ts-ignore
              suggestedTurnOnDate: `${tokens[1]}-${tokens[2]}-${tokens[0]}`,
              phoneNumber: chargeBeeCustomer.phone,

              serviceLine1: subscription.shipping_address.line1,
              serviceLine2: subscription.shipping_address.line2,
              serviceLine3: subscription.shipping_address.line3,
              servicePostalCode: subscription.shipping_address.zip,
              serviceProvince: subscription.shipping_address.state_code,
              serviceCountryIso2: subscription.shipping_address.country,
              serviceCompany: subscription.shipping_address.company,

              mailingLine1: chargeBeeCustomer.cf_mailing_line1,
              mailingLine2: chargeBeeCustomer.cf_mailing_line2,
              mailingLine3: chargeBeeCustomer.cf_mailing_line3,
              mailingPostalCode: chargeBeeCustomer.cf_mailing_zip,
              mailingProvince: chargeBeeCustomer.cf_mailing_state_code,
              mailingCountryIso2: chargeBeeCustomer.cf_mailing_country,
              mailingCompany: chargeBeeCustomer.cf_mailing_company,
            }

            // TODO: this could test addons too.
            // TODO: test taxes
            cy.wrap(actualFields).should("deep.equal", expectedFields)

            // TODO: this has all revealed that there are issues with the data. Need to tell Brad about it and ask what he wants done.
            // TODO: there is no city yet city: address.City,
          })
      }
    )
}

Cypress.Commands.add("checkout", checkout)
Cypress.Commands.add("address", address)
Cypress.Commands.add("manualAddress", manualAddress)
Cypress.Commands.add("checkCheckout", checkCheckout)
