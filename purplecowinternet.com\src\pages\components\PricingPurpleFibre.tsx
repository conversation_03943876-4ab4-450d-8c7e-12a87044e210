import React, { useEffect, useState } from "react"
import axios from "axios"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import CommonButtonInternet from "../../components/CommonButton"
import { navigate } from "gatsby"
import { PurpleCheckedIcon } from "../../icons/Icons"
import { splitDescription } from "../../utils"

const PricingPurpleFibre = () => {
  const [plans, setPlans] = useState<any>(null)
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios(
          process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
            "/snapshots/latest/services/internet/plans.json"
        )
        setPlans(
          response.data?.length > 0 &&
            response.data?.filter((plan: any) => plan?.provider === "PurpleCow")
        )
      } catch (err) {
        console.error("Failed to fetch internet plans:", err)
      }
    }
    fetchData()
  }, [])
  return (
    <RoundedContainer bgColor="black">
      <div className="h-max w-full p-3 sm:p-6">
        <FibreHeading
          color="white"
          className="md:w-[70%]"
          align="text-left"
          content={"Industry changing Pricing and Speed"}
        />
        <FibreDescription
          color="white"
          align="text-left"
          className="my-3 sm:my-10 md:w-[70%]"
          content={
            "All plans feature unlimited data and have a fixed price, so you don’t need to worry about any increases after a year"
          }
        />
        {/* Plan Cards */}
        <div className="flex flex-wrap justify-center gap-6 md:gap-9">
          {plans?.length > 0 &&
            plans?.map((plan: any) => (
              <div
                key={plan.id}
                className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-72 p-6 flex flex-col justify-between"
              >
                <div>
                  <h2 className="!font-anton mb-2">{plan?.name || ""}</h2>
                  <div className="flex">
                    <h1 className="!font-anton">
                      ${plan?.billing[0]?.monthly?.price}
                    </h1>
                    <p className="text-lg flex items-end ml-2">/month</p>
                  </div>
                  <ul className="plan-list text-[#666666] my-5">
                    {splitDescription(plan.description).map((line, i) => (
                      <li
                        className="my-2 flex items-center gap-3 !font-sans"
                        key={i}
                      >
                        <PurpleCheckedIcon />
                        {/* remove "✔️" sign and add svg*/}
                        {line?.replace("✔️", "")?.trim()}
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex justify-center">
                  <CommonButtonInternet
                    buttonText={"Select Plan"}
                    onClick={() => navigate("/join-the-herd")}
                  />
                </div>
              </div>
            ))}
        </div>{" "}
      </div>
      <FibreDescription
        align="text-center"
        color="White"
        content={"Zero contract, Zero install fee, Zero setup fees"}
        className="my-6"
      />
    </RoundedContainer>
  )
}

export default PricingPurpleFibre
