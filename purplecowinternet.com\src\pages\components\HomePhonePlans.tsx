import React, { useEffect, useState } from "react"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import { calculateTotalPrice, formatMoney, scrollToTop } from "../../utils"
import CommonButtonInternet from "../../components/CommonButton"
import { useDispatch, useSelector } from "react-redux"
import { PageStep, setFormData } from "../../redux/formSlice"
import { colorTheme } from "../../data/SrtingConstants"
import SelectHomePhonePlan from "./SelectHomePhonePlan"
import ProgressBarComponent from "./ProgressBarComponent"
import CheckoutBgIcons from "./Checkout_Bg_Icons"
import { validationErrorMessages } from "../../data/Regex"

interface HomePhoneProps {
  plans: any
  addons: any
}
const HomePhonePlans: React.FC<HomePhoneProps> = ({ plans, addons }) => {
  const dispatch = useDispatch()
  const formData = useSelector((state: any) => state.form)
  const [phonePortError, setPhonePortError]: any = useState("")

  useEffect(() => {
    scrollToTop()
  }, [])
  return (
    <div>
      <RoundedContainer
        className="relative pb-8"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <ProgressBarComponent status={PageStep.HOME_PHONE_PLAN} />
        <CheckoutBgIcons />
        <FibreHeading
          className="text-start ml-5 mt-5 sm:ml-10 sm:mt-10 md:ml-16 md:mt-16"
          align="text-left"
          color="white"
          content={"Add Home Phone"}
        />
        <FibreDescription
          color="white"
          className="text-start m-5 sm:ml-10 md:ml-16"
          align="text-left"
          content={"Home phone price can’t be beat"}
        />

        <SelectHomePhonePlan
          plans={plans}
          addons={addons}
          phonePortError={phonePortError}
          setPhonePortError={setPhonePortError}
        />
      </RoundedContainer>
      <div className="flex sticky bg-white py-3 bottom-0 flex-col sm:flex-row items-center justify-around">
        {!formData.monthEstimate ? (
          <h3 className="font-sans font-bold">Calculating....</h3>
        ) : (
          <h3 className="font-sans font-bold">
            Monthly: {formatMoney(calculateTotalPrice(formData?.monthEstimate))}
          </h3>
        )}
        <div className="flex justify-between gap-5">
          <CommonButtonInternet
            onClick={() => dispatch(setFormData({ page: PageStep.TV_PLAN }))}
            buttonText="Back"
            textColor="black"
            bgColor="white"
            className="border-black border font-bold"
          />

          <CommonButtonInternet
            onClick={() => {
              const isExistingPhone = formData?.is_existing_phone
              const portNumber = formData?.customer?.port_number || ""
              const cleanedNumber = portNumber.replace(/\D/g, "") // Remove non-numeric characters

              if (isExistingPhone && portNumber.trim() === "") {
                setPhonePortError("Enter mobile number")
                return
              }

              if (isExistingPhone) {
                let phoneValid = cleanedNumber.match(
                  /^(\+1 ?)?\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
                )

                if (!phoneValid) {
                  phoneValid = cleanedNumber.match(
                    /^(\+?1 ?)?\(?([0-9]{1})\)?[-. ]?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
                  )
                }

                if (!phoneValid) {
                  setPhonePortError(
                    validationErrorMessages.ENTER_CELL_PHONE_NUMBER
                  )
                  return
                }
              }

              dispatch(setFormData({ page: PageStep.CONTACT_INFORMATION }))
            }}
            buttonText="Next"
            textColor="black"
            className="font-bold"
          />
        </div>
      </div>
    </div>
  )
}

export default HomePhonePlans
