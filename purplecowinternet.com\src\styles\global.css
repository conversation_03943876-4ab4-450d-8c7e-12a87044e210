/* @import url("https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"); */

@import url("https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");
@import url("https://api.fontshare.com/v2/css?f[]=satoshi@400,500,700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Anton&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Anton&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: "Roboto", sans-serif;
    font-variant-ligatures: none !important;
  }

  input,
  textarea,
  .input-container {
    @apply text-base px-4 py-3 focus:border-primary-700 focus:outline-none rounded-lg border border-gray-300 font-sans font-light text-gray-700 placeholder-gray-300;
  }

  h1 {
    @apply text-5xl md:text-6xl font-light text-gray-900;
  }

  h2 {
    @apply text-3xl md:text-4xl font-light text-gray-900;
  }

  h3 {
    @apply text-xl md:text-2xl font-light text-gray-900;
  }

  .prose {
    @apply font-thin;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    @apply font-normal;
  }

  .prose a {
    @apply text-primary-700;
  }
}
