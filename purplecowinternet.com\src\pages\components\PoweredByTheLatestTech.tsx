import React from "react"
import purpleFibreont from "../../content/PurpleFibreOnt.png"
import dancingCow from "../../content/Dancing_Cow.gif"
import RoundedContainer from "../../components/RoundedContainer"
const PoweredByTheLatestTech = () => {
  return (
    <RoundedContainer bgColor="white">
      <div className="flex flex-col-reverse md:flex-row items-stretch">
        <div className="flex items-center justify-center text-center md:text-left w-full md:w-[50%] my-4">
          <h1 className="text-[40px] sm:text-[35px] md:text-[55px] lg:text-[80px] xl:text-[100px] text-black">
            <div className="flex font-anton flex-wrap mt-5 items-center justify-center md:flex-col md:items-start gap-x-2">
              <span>POWERED BY</span>
              <span>THE LATEST</span>
              <span className="flex">
                TECH
                <img src={dancingCow} className="h-[1em]" alt="Dancing Cow" />
              </span>
            </div>
          </h1>
        </div>

        <div className="flex justify-center md:w-1/2 h-full">
          <img
            className="h-full max-w-[50%] md:max-w-[90%] md:w-auto object-contain"
            src={purpleFibreont}
            alt="Speed"
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default PoweredByTheLatestTech
