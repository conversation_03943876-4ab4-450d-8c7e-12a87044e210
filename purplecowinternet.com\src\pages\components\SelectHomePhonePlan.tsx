import React, { useEffect } from "react"
import { <PERSON><PERSON><PERSON>, PurpleCheckedIcon } from "../../icons/Icons"
import { formatPhoneNumber, splitDescription } from "../../utils"
import CommonButtonInternet from "../../components/CommonButton"
import { useDispatch, useSelector } from "react-redux"
import { setFormData } from "../../redux/formSlice"
import { colorTheme } from "../../data/SrtingConstants"

interface HomePhoneProps {
  plans: any
  addons: any
  phonePortError: string
  setPhonePortError: (err: string) => void
}
const SelectHomePhonePlan: React.FC<HomePhoneProps> = ({
  plans,
  addons,
  phonePortError,
  setPhonePortError,
}) => {
  const dispatch = useDispatch()
  const formData = useSelector((state: any) => state.form)
  let seletedAddonPlan: any = formData?.addons

  const phoneAddons = plans?.homePhone
  const miscAddons = addons?.misc
  const optionalAddons = [...(phoneAddons || []), ...(miscAddons || [])]

  const CheckExistPlan = (id: any) => {
    if (seletedAddonPlan?.indexOf(id) > -1) {
      return true
    }
    return false
  }

  const PlanChangeRadio = async (apiName: any) => {
    let value = String(apiName)
    let updatedAddonPlan = [...seletedAddonPlan]
    if (updatedAddonPlan.includes(value)) {
      // remove the value from the array if it already exists
      updatedAddonPlan = updatedAddonPlan.filter((item) => item !== value)
    } else {
      updatedAddonPlan.push(value)
    }
    dispatch(
      setFormData({
        addons: updatedAddonPlan,
        optionalPlanTrigger: Math.floor(Math.random() * 1000000000),
      })
    )
  }
  const handleClickExistingPhone = () => {
    dispatch(
      setFormData({
        is_existing_phone: true,
        is_new_phone: false,
      })
    )
  }
  const handleClickNewPhone = () => {
    dispatch(
      setFormData({
        is_new_phone: true,
        is_existing_phone: false,
        customer: {
          ...formData?.customer,
          port_number: "",
        },
      })
    )
    setPhonePortError("")
  }
  const handleChangePortNumber = (e: any) => {
    setPhonePortError("") // Reset error message on input change
    let pNumber = e.target.value.replace(/\D/g, "") // Remove non-numeric characters
    const updatedCustomer = {
      ...formData.customer,
      port_number: formatPhoneNumber(pNumber),
    }
    dispatch(setFormData({ customer: updatedCustomer }))
  }

  // reset state if user de-select the home phone
  useEffect(() => {
    if (formData?.addons?.length === 0) {
      dispatch(
        setFormData({
          is_new_phone: true,
          is_existing_phone: false,
          customer: {
            ...formData?.customer,
            port_number: "",
          },
        })
      )
      setPhonePortError("")
    }
  }, [formData?.addons?.length])
  return (
    <div>
      {" "}
      <div className="flex flex-wrap justify-center gap-6 md:gap-9">
        {optionalAddons?.slice(0, 1)?.map((homePhone: any, index: number) => (
          <div
            key={index}
            className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
          >
            <div>
              <h2 className="!font-anton mb-2">
                {homePhone?.name || "Home Phone"}
              </h2>
              <div className="flex my-5">
                <h1 className="!font-anton">
                  ${homePhone?.billing_period[0]?.monthly?.price || 0}
                </h1>
                <p className="text-lg flex items-end ml-2">/month</p>
              </div>
              <ul className="plan-list text-[#666666] my-5">
                {splitDescription(homePhone.description).map((line, i) => (
                  <li className="my-2 flex items-center gap-3 !font-sans" key={i}>
                    <PurpleCheckedIcon />
                    {/* remove "✔️" sign and add svg*/}
                    {line?.replace("✔️", "").trim()}
                  </li>
                ))}
              </ul>
            </div>
            <div className="flex justify-center">
              <CommonButtonInternet
                className={`px-28 ${
                  CheckExistPlan(
                    homePhone?.billing_period[0]?.monthly
                      ? homePhone?.billing_period[0]?.monthly?.api_name
                      : homePhone?.billing_period[0]?.non_recurring?.api_name
                  )
                    ? "font-bold"
                    : ""
                }`}
                buttonText={
                  CheckExistPlan(
                    homePhone?.billing_period[0]?.monthly
                      ? homePhone?.billing_period[0]?.monthly?.api_name
                      : homePhone?.billing_period[0]?.non_recurring?.api_name
                  )
                    ? "Remove"
                    : "Add"
                }
                onClick={() =>
                  PlanChangeRadio(
                    homePhone?.billing_period[0]?.monthly?.api_name
                  )
                }
              />
            </div>
          </div>
        ))}
      </div>
      {formData?.addons?.length > 0 && (
        <div className="flex justify-center w-full mt-10">
          <div className="w-[90%] sm:w-[70%]">
            <h3 className="text-white font-medium font-sans">
              Please indicate one of the two options below for your home phone
              number.
            </h3>

            <span className="flex gap-2 mt-5">
              <div
                onClick={() => handleClickNewPhone()}
                className="w-7 h-7 relative bg-white rounded-full cursor-pointer flex items-center justify-center"
              >
                {formData?.is_new_phone && (
                  <CheckIcon color={colorTheme.MEDIUM_PURPLE} />
                )}
              </div>
              <h3 className="text-white font-medium font-sans">
                I would like a new phone number
              </h3>
            </span>
            <br />

            <span className="flex gap-2">
              <div
                onClick={() => handleClickExistingPhone()}
                className="w-7 h-7 relative bg-white rounded-full cursor-pointer flex items-center justify-center"
              >
                {formData?.is_existing_phone && (
                  <CheckIcon color={colorTheme.MEDIUM_PURPLE} />
                )}
              </div>

              <h3 className="text-white font-medium font-sans w-[90%]">
                I would like to port my existing number
              </h3>
            </span>

            {formData?.is_existing_phone === true ? (
              <div className="mt-5">
                <label htmlFor="port_number" className="hidden">
                  Phone number
                </label>
                <input
                  className="rounded-2xl border-none placeholder:text-[#A9A9A9] placeholder:text-sm bg-[#F7F7F7] w-full focus:z-10"
                  id="port_number"
                  autoComplete="off"
                  placeholder="Phone number"
                  type="text"
                  value={formData?.customer?.port_number}
                  onChange={(e) => handleChangePortNumber(e)}
                  required
                />
                <small className="text-red-600">{phonePortError}</small>
              </div>
            ) : (
              ""
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default SelectHomePhonePlan
