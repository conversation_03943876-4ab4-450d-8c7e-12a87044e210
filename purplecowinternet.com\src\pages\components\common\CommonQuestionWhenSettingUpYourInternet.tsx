import React, { useState } from "react"
import { Icon } from "../../../icons/Icons"

const CommonQuestion = () => {
  const [open, setOpen] = useState(0)

  const handleOpen = (value: number) => {
    setOpen(open === value ? 0 : value)
  }

  const questions = [
    {
      id: 1,
      question: (
        <>I waited 15 mins and the 5 lights are not lit, what do I do?</>
      ),
      answer: (
        <>
          Sorry it's not working for you on your first try. This could mean that
          the coax port you're plugged into isn't live or is disconnected
          somewhere. If you've already tried other coax ports in your home, try
          tracing the line back to see where it might be hooked. Sometimes, the
          line could be cut, in which case we would need to send a technician to
          assist. Other times, all the coax ports lead to a communications panel
          where it just needs to be plugged in. We can assist if you send us a
          photo from inside the communications panel.
        </>
      ),
    },
    {
      id: 2,
      question: (
        <>My device says I'm connected to the internet however nothing loads.</>
      ),
      answer: (
        <>
          No worries. Take a look at your modem, are all five lights lit?
          Normally if you can not load anything it’s because your modem is not
          connected to a coax port that is connected to the internet. See if you
          can find another coax port to plug into or feel free to try to
          following this coax line through your home to see if it's cut or
          unhooked somewhere. Let us know if you find the line cut, we can help!
        </>
      ),
    },
    {
      id: 3,
      question: (
        <>
          I have my wifi setup however I am seeing two different networks. Which
          one do I connect to?
        </>
      ),
      answer: (
        <>
          After setting up your WiFi, you'll notice two different networks. One
          is labeled with '5G' at the end, which is ideal for devices in the
          same room as the modem as it often doesn't travel through walls well
          but carries a lot of bandwidth. The other network extends farther and
          penetrates walls more effectively. Feel free to use the network that
          works best for each device in your home.
        </>
      ),
    },
    {
      id: 4,
      question: (
        <> What if one of my devices can not see my Purple Cow network?</>
      ),
      answer: (
        <>
          Feel free to unplug and plug your device and modem back in. That often
          does the trick.
        </>
      ),
    },
  ]

  return (
    <>
      <section className="tv-section8">
        <div className="container mx-auto px-4">
          <h2 className="h2 text-secondary text-center">
            Some common setup questions
          </h2>
          <div className="max-width1000">
            {questions.map(({ id, question, answer }) => (
              <div key={id} className="border-b">
                <div
                  onClick={() => handleOpen(id)}
                  className="w-full cursor-pointer font-semibold text-left py-4 px-2 flex justify-between items-center text-xl"
                >
                  {question}
                  <Icon id={id} open={open ?? 0} />
                </div>
                {open === id && (
                  <div className="p-2 text-sm text-gray-700">{answer}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  )
}

export default CommonQuestion
