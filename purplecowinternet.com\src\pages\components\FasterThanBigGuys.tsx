import React from "react"
import speedImg from "../../content/Pc_lightning_rgb.png"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import { colorTheme } from "../../data/SrtingConstants"

const FasterThanBigGuys = () => {
  return (
    <RoundedContainer bgColor="black">
      <div className="flex flex-col py-4 md:flex-row justify-between items-center w-full h-auto md:min-h-[55vh] lg:min-h-[75vh]">
        <div className="md:flex justify-center items-center w-full md:w-[40%] hidden">
          <img
            src={speedImg}
            alt="Speed"
            className="w-full md:w-[50%] max-h-[200px] md:max-h-[300px] lg:max-h-[400px] object-contain"
          />
        </div>

        <div className="flex flex-col justify-center pr-5 text-right items-end w-full md:w-[60%] md:mx-10 my-3 md:my-10">
          <FibreHeading
            color="white"
            align="text-right"
            content={
              <span className="flex flex-col ">
                <span
                  style={{ color: colorTheme.MEDIUM_PURPLE }}
                  className={`!font-anton`}
                >
                  10X FASTER
                </span>
                <span className="!font-anton">THAN THE BIG</span>
                <span className="!font-anton">GUY'S FASTEST</span>
              </span>
            }
            className="text-end"
          />
          <FibreDescription
            content={
              "Up to 8Gbps upload and download. Nova Scotia has not seen anything like this!"
            }
            className="mt-5 text-end"
            color="white"
            align="text-right"
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default FasterThanBigGuys
