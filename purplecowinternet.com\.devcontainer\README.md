# Purple Cow Internet DevContainer

This directory contains configuration for running the project in a VS Code DevContainer.

## Getting Started

1. **Open in DevContainer**
   - Open this project in VS Code.
   - If you have the Dev Containers extension installed, you will be prompted to reopen in a container. Accept the prompt.

2. **Environment Variables**
   - Copy `.devcontainer/devcontainer.env.example` to `.env.development` in the project root.
   - Fill in the required values for your environment. See the main project README for details on each variable.

3. **Install Dependencies**
   - Dependencies are installed automatically on first container build via `yarn install`.

4. **Start Development Server**
   - Run `npx gatsby develop -H 127.0.0.1 -p 8000` inside the container.
   - The app will be available at [http://localhost:8000](http://localhost:8000).

5. **Production Build**
   - Run `npm run build` and `npm run serve` to test the production build at [http://localhost:9000](http://localhost:9000).

## Notes
- Ports 8000 and 9000 are forwarded by default.
- Node.js 20.x and Yarn are pre-installed.
- Recommended VS Code extensions are included. 