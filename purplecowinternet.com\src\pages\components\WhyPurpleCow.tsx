import React from "react"
import Circle from "../../content/CircleSteps.png"

interface HowToGetStartedProps {
  steps: string[]
}

const HowToGetStarted: React.FC<HowToGetStartedProps> = ({ steps }) => {
  if (!steps) return
  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-5 flex flex-col items-center sm:gap-3 md:gap-6 lg:gap-20 md:flex-row md:flex-wrap md:justify-center">
      {steps?.length > 0 &&
        steps?.map((content, index) => (
          <div
            key={index}
            className="w-full sm:w-2/3 md:w-1/2 lg:w-1/3 xl:w-1/4 flex justify-center"
          >
            <div className="w-full max-w-xs h-max md:h-[500px] lg:h-[600px] flex flex-col items-center text-center p-4 box-border">
              {/* Circle fixed at top */}
              <div
                className="w-48 h-48 sm:w-56 sm:h-56 bg-no-repeat bg-center bg-contain flex items-center justify-center mb-6 shrink-0"
                style={{ backgroundImage: `url(${Circle})` }}
              >
                <h1 className="font-sans font-bold text-8xl">{index + 1}</h1>
              </div>

              {/* Text wrapper with scroll fallback if overflow */}
              <div className="overflow-hidden flex-1 flex items-start">
                <h3 className="font-sans">{content}</h3>
              </div>
            </div>
          </div>
        ))}
    </div>
  )
}

export default HowToGetStarted
