import { map, sum } from "lodash"
import { Address } from "./addressValidator"
import CryptoJS from "crypto-js"
import { holidayList } from "./data/HolidayList"

export function formatAddress(address: Address) {
  if (address.Label) {
    return address.Label
  }
  return `${optionallyAddPrefixDash(address.Line1, address.SubBuilding)}, \n${
    address.City
  }, \n${address.PostalCode}`
}

export function formatMoney(amount: number) {
  if (!amount) return 0

  return amount
    .toLocaleString("en-US", {
      style: "currency",
      currency: "CAD",
      minimumFractionDigits: 2, // Always show two decimal places
      maximumFractionDigits: 2, // Ensures only two decimal places
    })
    .slice(2) // Removes the "$" symbol
}

export function formatDate(date: Date) {
  if (date) {
    let yyyy = date.getFullYear().toString()
    let mm = (date.getMonth() + 1).toString()
    let dd = date.getDate().toString()
    let mmChars = mm.split("")
    let ddChars = dd.split("")
    return (
      yyyy +
      "-" +
      (mmChars[1] ? mm : "0" + mmChars[0]) +
      "-" +
      (ddChars[1] ? dd : "0" + ddChars[0])
    )
  }
  return date
}

export function greaterLessDate(start: any, end: any) {
  if (Date.parse(formatDate(start)) > Date.parse(formatDate(end))) {
    return 1
  } else {
    return 2
  }
}

declare global {
  interface Date {
    getWeeks(start?: number): [Date, Date]
  }
}

Date.prototype.getWeeks = function (start) {
  start = start || 0
  var today = new Date(this.setHours(0, 0, 0, 0))
  var day = today.getDay() - start
  var date = today.getDate() - day

  var StartDate = new Date(today.setDate(date))
  var EndDate = new Date(today.setDate(date + 6))
  return [StartDate, EndDate]
}

export function priceFormat(price: any) {
  if (typeof price === "number") {
    const decimalPart = price % 1 // Get the decimal part of the number
    if (decimalPart === 0) {
      return price.toFixed(0) // If the decimal part is 0, format as whole number
    } else {
      return price.toFixed(2) // If the decimal part exists, format with two decimal places
    }
  }
  return "0" // Return '0' as a string if the input is not a number
}

export function formatPhoneNumber(phoneNumber: string) {
  if (phoneNumber) {
    phoneNumber = phoneNumber.replace(/\D/g, "")
    phoneNumber = phoneNumber.slice(0, 11)
  }
  var match = phoneNumber.match(
    /^(\+1 ?)?\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
  )
  if (phoneNumber) {
    if (phoneNumber.length > 10) {
      match = phoneNumber.match(
        /^(\+?1 ?)?\(?([0-9]{1})\)?[-. ]?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
      )
      if (match) {
        return [match[2], "(", match[3], ") ", match[4], "-", match[5]].join("")
      }
      return phoneNumber
    } else {
    }
  }

  if (match) {
    return ["(", match[2], ") ", match[3], "-", match[4]].join("")
  }
  return phoneNumber
}

export function optionallyAddPrefixDash(string: string, prefix: string) {
  if (prefix) {
    return prefix + "-" + string
  } else {
    return string
  }
}

export function formatDescription(description: string) {
  if (description.toLowerCase().indexOf("setup fee") !== -1) {
    return "Delivery & Provisioning"
  } else {
    return description
  }
}

const key = CryptoJS.enc.Hex.parse(
  process.env.GATSBY_CART_KEY || "your-cart-key"
)
const iv = CryptoJS.enc.Hex.parse(process.env.GATSBY_CART_IV || "your-cart-iv")

export function encrypt(text: string): string {
  const encrypted = CryptoJS.AES.encrypt(text, key, { iv: iv })
  return encrypted.toString()
}

export function decrypt(encryptedText: string): string {
  const decrypted = CryptoJS.AES.decrypt(encryptedText, key, { iv: iv })
  return decrypted.toString(CryptoJS.enc.Utf8)
}

export function splitDescription(description: string): string[] {
  const checkmark = "\u2714\ufe0f"
  const prefix = description.split(checkmark).length > 1 ? checkmark : ""
  return description
    .split(checkmark)
    .filter((line) => line.trim() !== "")
    .map((line) => prefix + line)
}

export function calculateTotalPrice(monthEstimate: any) {
  let recurring = monthEstimate?.invoice_estimate?.line_items?.filter(
    ({ date_from, date_to }: any) => date_from !== date_to
  )
  let recurringSubtotal = sum(map(recurring, "amount")) / 100
  return recurringSubtotal
}

// Convert holiday list to a Set for faster lookup
const holidaySet = new Set(
  holidayList.map((holiday) => {
    const [day, month, year] = holiday.Date.split("/").map(Number)
    return new Date(year, month - 1, day).toDateString() // Normalize to string for comparison
  })
)
// Check if a date is holiday
export function isHoliday(date: Date) {
  const validDate = new Date(date)
  return holidaySet.has(validDate.toDateString())
}

// Check if a date is week-end
export function isWeekEnd(date: Date) {
  const day = date.getDay()
  return day === 0 || day === 6
}

export function scrollToTop() {
  window.scroll({ top: 0, left: 0, behavior: "smooth" })
}
