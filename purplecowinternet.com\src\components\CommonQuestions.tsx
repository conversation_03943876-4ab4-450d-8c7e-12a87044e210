import React, { ReactNode, useState } from "react"
import RoundedContainer from "./RoundedContainer"
import { FibreHeading } from "./FibreHeading"
import { Icon } from "../icons/Icons"
import pcEyes from "../content/Pc_Eyes.png"

interface QuestionItem {
  id: number
  question: ReactNode
  answer: ReactNode
}

interface CommonQuestionsProps {
  questions: QuestionItem[]
  bgColor?: string
  textColor?: string
  title?: string
}
const CommonQuestions: React.FC<CommonQuestionsProps> = ({
  questions,
  bgColor = "black", // default bg color
  textColor = "white",
  title = "COMMON QUESTIONS",
}) => {
  const [open, setOpen] = useState(0)
  const handleOpen = (value: number) => {
    setOpen(open === value ? 0 : value)
  }
  return (
    <RoundedContainer bgColor={bgColor}>
      <div className="flex flex-col lg:flex-row justify-between p-2 sm:p-10 w-full">
        <div className="w-full flex flex-col items-center">
          <FibreHeading
            color={textColor}
            align="text-left"
            className="text-start"
            content={title}
          />

          <div className="mt-10 flex justify-center">
            <img
              src={pcEyes}
              alt="Decorative"
              className="hidden md:block w-20 sm:w-30 md:w-44 lg:w-52 scale-90 hover:scale-95 transition-all duration-150"
            />
          </div>
        </div>

        <div className="w-full font-sans flex flex-col items-start text-left">
          <section className="w-full">
            <div>
              <div>
                {questions.map(({ id, question, answer }) => (
                  <div key={id} className="border-b">
                    <div
                      style={{ color: textColor }}
                      onClick={() => handleOpen(id)}
                      className="w-full cursor-pointer font-semibold text-left py-4 px-2 flex justify-between items-center text-xl"
                    >
                      <p className="text-2xl">{question}</p>
                      <div className="ml-1">
                        <Icon color={textColor} id={id} open={open ?? 0} />
                      </div>
                    </div>
                    {open === id && (
                      <div style={{ color: textColor }} className="p-2 text-sm">
                        {answer}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </section>
        </div>
      </div>
    </RoundedContainer>
  )
}

export default CommonQuestions
