import { createSlice, PayloadAction } from "@reduxjs/toolkit"

export enum PageStep {
  CHECK_PLANS = "checkplans",
  TV_PLAN = "tvplan",
  HOME_PHONE_PLAN = "homephoneplan",
  CONTACT_INFORMATION = "contactinformation",
  CHECKOUT = "checkoout",
}

export function defaultFormData() {
  return {
    isUpdateCart: 1,
    formData: 0,
    shipping_address: {
      Line1: "",
      SubBuilding: "",
      City: "",
      Province: "",
      PostalCode: "",
    },
    mailing_address: {
      Line1: "",
      SubBuilding: "",
      City: "",
      Province: "",
      PostalCode: "",
    },
    plan_id: null,
    addons: [] as any[],
    optional_addons: [] as any[],
    Service_Area: "",
    tv_plan: null as number | null,
    tv_optional_plans: [] as any[],
    tv_optional_single_plans: [] as any[],
    tv_optional_single_plans_name: [] as any[],
    selectedAddonPlanData: [] as any[],
    is_mailing: true,
    is_new_phone: true,
    is_existing_phone: false,
    is_business: false,
    customer: {
      first_name: "",
      last_name: "",
      port_number: "",
      phone: "",
      email: "",
      cf_suggested_turn_on_date: "" as any,
      cf_company_name: "",
    },
    total: 0,
    SA_SFRecordID: "",
    monthly_yearly: "M",
    monthEstimate: null,
    yearEstimate: null,
    optionalPlanTrigger: 0,
    OptionalPagePlan: [] as any[],
    OptionalPagePlansIcons: [] as any[],
    OptionalPagePlansIconsName: [] as any[],
    optionaPlansIcons: [] as any[],
    hideTvPlan: "",
    selectedItems: {
      internet: true,
      tv: false,
      home_phone: false,
      mobile: false,
    },
    // Frontend only field
    page: PageStep.CHECK_PLANS,
    addressType: "" as "" | "PurpleCow" | "Eastlink",
  }
}

type FormState = ReturnType<typeof defaultFormData> & {
  page: PageStep
}
const formSlice = createSlice({
  name: "form",
  initialState: defaultFormData() as FormState,
  reducers: {
    setFormData(state, action: PayloadAction<Partial<FormState>>) {
      return { ...state, ...action.payload }
    },
    resetFormData() {
      return defaultFormData()
    },
  },
})

export const { setFormData, resetFormData } = formSlice.actions
export default formSlice.reducer
