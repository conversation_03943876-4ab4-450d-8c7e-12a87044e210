import React from "react"
// import symmetricalSpeed from "../../content/Symmetrical_Speed.mp4"
import RoundedContainer from "../../components/RoundedContainer"

const SymmetricalSpeeds = () => {
  return (
    <RoundedContainer>
      <div className="relative w-full h-[60vh] md:h-[90vh] flex items-center justify-center overflow-hidden">
        {/* <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute rounded-3xl inset-0 w-full h-full object-cover"
        >
          <source src={symmetricalSpeed} type="video/mp4" />
          Your browser does not support the video.
        </video> */}

        <div className="relative px-4 sm:px-8 md:px-16 lg:px-32 flex flex-col items-center justify-center text-center w-full">
          <h1 className="text-[40px] sm:text-[35px] md:text-[55px] lg:text-[80px] xl:text-[100px] font-anton text-center md:text-right text-white">
            <div className="flex flex-wrap items-center justify-center md:flex-col md:items-end gap-x-2">
              SYMMETRICAL SPEEDS
            </div>
          </h1>
          <h2 className="mt-6 md:mt-12 lg:mt-16 text-white font-sans text-lg sm:text-xl md:text-3xl lg:text-4xl xl:text-5xl w-full max-w-[900px]">
            Only Purple Fibre offers 100 % of its plans with symmetrical speeds,
            ensuring equal upload and download speeds for the ultimate internet
            experience.
          </h2>
        </div>
      </div>
    </RoundedContainer>
  )
}

export default SymmetricalSpeeds
