import React, { useEffect, useState } from "react"
import Layout from "../../components/Layout"
import { Helmet } from "react-helmet"
import "../../styles/custome.css"
import axios from "axios"

export default function Channels() {
  const [channels, setChannels] = useState([
    { channel_number: 0, name: "Loading...", packages: { "Loading...": true } },
  ])
  const [columns, setColumns] = useState(["Loading..."])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios(
          "https://product-catalog-storage.s3.ca-central-1.amazonaws.com/snapshots/latest/services/tv/channel_guide.json"
        )
        setColumns(Object.keys(response.data[0].packages))
        setChannels(response.data)
      } catch (err) {
        console.error("Failed to fetch channels:", err)
      }
    }
    fetchData()
  }, [])

  return (
    <Layout>
      <Helmet>
        <title>Channels Guide – Purple Cow Internet 💜🐄</title>
      </Helmet>
      <section className="px-4 sm:px-6 lg:px-20 xl:px-40 flex flex-col items-center justify-center py-8 mx-auto font-light prose prose-lg w-full">
        <h1 className="mt-7 text-center">TV Channels</h1>
        <p className="font-bold mt-8 leading-relaxed text-xl text-[#374151] text-center">
          This is a list of all the channels we currently offer. If you have any
          questions, don't hesitate to call or text us at 902-800-2660.
        </p>
        <div className="w-full overflow-x-auto mt-6 shadow-md">
          <table className="w-full border-collapse text-sm md:text-base">
            <thead className="bg-[#f2f2f2]">
              <tr>
                <th className="px-4 py-2 text-center text-[#333333]">
                  Channel #
                </th>
                <th className="px-4 py-2 text-center text-[#333333]">
                  Channel Name
                </th>
                {columns.map((header, index) => (
                  <th
                    key={index}
                    className="px-4 py-2 text-center text-[#333333]"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {channels
                .sort((a, b) => a.channel_number - b.channel_number)
                .map((channel: any, index: number) => (
                  <tr className="hover:bg-[#a059eb]" key={index}>
                    <td className="px-4 py-2 text-center">
                      {channel.channel_number}
                    </td>
                    <td className="px-4 py-2 text-center">{channel.name}</td>
                    {columns.map((header, idx) => (
                      <td key={idx} className="px-4 py-2 text-center">
                        {channel.packages[header] ? "✓" : ""}
                      </td>
                    ))}
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      </section>
      <p className="my-8 leading-relaxed text-center text-xl text-[#374151]">
        Last Updated: June 11th 2024
      </p>
    </Layout>
  )
}
