import React from "react"
import RoundedContainer from "../../components/RoundedContainer"
import purplefibrehero from "../../content/PurpleFibreHero.png"
import PurpleFibreLogo from "../../content/purple_fibre_logo.png"
import CommonButtonInternet from "../../components/CommonButton"
import { navigate } from "gatsby"
import { colorTheme } from "../../data/SrtingConstants"

const NovaScotiasFastestPureFibreInternet = () => {
  return (
    <RoundedContainer className="mt-1 sm:mt-1" bgImage={purplefibrehero}>
      <div className="w-full bg-cover sm:h-[90dvh] bg-center flex flex-col items-center justify-center">
        <img className="w-60 sm:w-96 my-10" src={PurpleFibreLogo} alt="" />
        <h1 className="font-sans text-white text-center font-extrabold mb-10">
          Nova Scotia’s Fastest <br /> Pure Fibre Internet
        </h1>
        <div className="flex justify-center mb-5">
          <CommonButtonInternet
            buttonText={<>Check availability &gt; </>}
            textColor="white"
            bgColor={colorTheme.MEDIUM_PURPLE}
            onClick={() => navigate("/join-the-herd")}
            className="py-4"
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default NovaScotiasFastestPureFibreInternet
