import React from "react"
import PurpleCowModem from "../../content/PurpleCowModem.png"
import dancingCow from "../../content/Dancing_Cow.gif"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import CommonButtonInternet from "../../components/CommonButton"
import { navigate } from "gatsby"
const FibreNetwork = () => {
  return (
    <RoundedContainer bgColor="white">
      <div className="flex flex-col md:flex-row md:justify-between sm:px-10">
        <div className="flex flex-col justify-center md:w-[60%] mr-5">
          <FibreHeading
            color="black"
            align="text-left"
            content={
              <span className="!font-anton">
                100% fibre
                <div className="flex justify-start !font-anton">
                  Network
                  <img src={dancingCow} className="h-[1em]" alt="Dancing Cow" />
                </div>
              </span>
            }
            className="my-5 text-start"
          />
          <FibreDescription
            color="black"
            align="text-left"
            content={
              "Powered by the latest technology so you have the most reliable internet."
            }
            className="text-start mb-5"
          />
          <div>
            <CommonButtonInternet
              className="mt-5 hidden md:block"
              buttonText="Get 100% Fibre"
              onClick={() => navigate("/join-the-herd")}
            />
          </div>
        </div>

        <div className="flex justify-center h-full">
          <img
            className="object-contain w-52 sm:w-60 xl:w-80 2xl:w-96"
            src={PurpleCowModem}
            alt="Speed"
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default FibreNetwork
