import React, { ReactNode, useState } from "react"
import { colorTheme } from "../data/SrtingConstants"

interface ButtonProps {
  onClick: (e?: any) => void
  bgColor?: string
  textColor?: string
  className?: string
  buttonText?: ReactNode
  disabled?: boolean
  title?: string
}

const CommonButtonInternet: React.FC<ButtonProps> = ({
  onClick = () => {},
  bgColor = colorTheme.GOLD,
  textColor = "black",
  className = "",
  disabled = false,
  buttonText = "Button",
  title = "",
}) => {
  const [hovered, setHovered] = useState(false)

  const bgHoverColor =
    bgColor === colorTheme.GOLD
      ? colorTheme.GOLD_HOVER
      : bgColor === colorTheme.MEDIUM_PURPLE
        ? colorTheme.DARK_PURPLE
        : bgColor === "transparent"
          ? ""
          : "#ECECEC"

  return (
    <button
      title={title}
      disabled={disabled}
      onClick={onClick}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      style={{
        backgroundColor: hovered ? bgHoverColor : bgColor,
        color: textColor,
        fontVariantLigatures: "none",
      }}
      className={`!font-sans w-max rounded-3xl flex items-center justify-center whitespace-nowrap text-lg sm:text-xl py-3 px-14 md:w-max ${className}`}
    >
      {buttonText}
    </button>
  )
}

export default CommonButtonInternet
