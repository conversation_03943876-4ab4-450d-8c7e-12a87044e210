export const validationRegEx = {
  phone_regex:
    /^(\+?[1-9]\d{0,2}\s?)?(\([0-9]{3}\)|[0-9]{3})[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/,
  email_regex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  postalcode_regex: /^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/,
}

export const validationErrorMessages = {
  ENTER_FIRST_NAME: "Please enter your first name.",
  ENTER_LAST_NAME: "Please enter your last name.",
  ENTER_BUSINESS_NAME: "Please enter your business name.",
  ENTER_CELL_PHONE_NUMBER:
    "Please enter a valid phone number, such as (************* or ****************.",
  ENTER_VALID_EMAIL_ADDRESS: "Please enter a valid email address.",
  ENTER_ADDRESS: "Please enter your address.",
  ENTER_CITY: "Please enter your city.",
  ENTER_PROVINCE: "Please enter your province.",
  ENTER_VALID_POSTAL_CODE: "Please enter a valid postal code, such as A1A 1A1.",
}
