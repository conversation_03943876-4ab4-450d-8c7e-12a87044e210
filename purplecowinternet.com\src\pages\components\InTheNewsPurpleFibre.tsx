import React from "react"
import smilyFace from "../../content/SmilyFace.png"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreHeading } from "../../components/FibreHeading"

const InTheNewsPurpleFibre = () => {
  return (
    <RoundedContainer bgColor="white">
      <div className="flex relative flex-col md:flex-row items-center justify-between p-6 sm:p-10 w-full">
        <FibreHeading
          align="text-left"
          color="black"
          content={
            <>
              In the <br className="hidden sm:block" /> news
            </>
          }
        />

        <div className="w-full mt-3 md:mt-0 font-sans md:w-1/2 flex flex-col justify-center items-start text-left">
          <h2 className="font-bold">"The fastest internet in Nova Scotia"</h2>
          <h3 className="font-bold uppercase my-5">All Nova Scotia</h3>
          <div className="border-b border-black w-full" />

          <h2 className="font-bold mt-7">
            "Unmatched reliability and customer support"
          </h2>
          <h3 className="font-bold uppercase my-5">Global News</h3>
          <div className="border-b border-black w-full" />

          <h2 className="font-bold mt-7">
            "The most desired internet package"
          </h2>
          <h3 className="font-bold uppercase my-5">Haligonia</h3>
        </div>
        <img
          src={smilyFace}
          className="absolute bottom-[2%] sm:bottom-[5%] right-[5%] w-[20%] max-w-[28%] md:max-w-[10%] sm:w-20"
        />
      </div>
    </RoundedContainer>
  )
}

export default InTheNewsPurpleFibre
