import React, {
  useCallback,
  useEffect,
  useState,
  useImperative<PERSON>andle,
  forwardRef,
} from "react"
import { useScript } from "../../hooks"
import { Address } from "../../addressValidator"
import { defaultFormData } from "../../customfunction"
const WaitingListAddressComponent = forwardRef((props, ref) => {
  const [formData, setformData] = useState(defaultFormData())
  const [loaded] = useScript(
    "https://ws1.postescanada-canadapost.ca/js/addresscomplete-2.30.min.js"
  )
  /*
   *Address Mapping
   */
  let onFormStateChanged = useCallback((newFormState: any) => {
    setformData((formData) => ({ ...formData, ...newFormState }))
  }, [])
  let addressChange: any = useCallback(
    (shipping_address: any) => onFormStateChanged({ shipping_address }),
    [onFormStateChanged]
  )

  useImperativeHandle(ref, () => ({
    getFormData: () => formData,
  }))

  useEffect(() => {
    let type = window?.pca?.fieldMode?.DEFAULT
    if (loaded && typeof type !== "undefined" && typeof type !== "undefined") {
      let fields = [
        {
          element: "line1",
          field: "Line1",
          mode: window.pca.fieldMode.DEFAULT,
        },
        {
          element: "line11",
          field: "Line1",
          mode: window.pca.fieldMode.DEFAULT,
        },
        {
          element: "apartment",
          field: "BuildingNumber",
          mode: window.pca.fieldMode.POPULATE,
        },
      ]
      let options = {
        key: "MM49-DD25-FR41-WY59",
        bar: { showCountry: false, showLogo: true, logoLink: false },
      }
      let control = new window.pca.Address(fields, options)
      control.listen("populate", (a: Address) => {
        addressChange(a)
      })
      return () => {
        control.destroy()
      }
    }
  }, [loaded, addressChange])

  const checkAddress = () => {
    let values: any = formData?.shipping_address
    if (values?.Line1) {
      localStorage.setItem("address_data", JSON.stringify(formData))
    }
  }
  useEffect(() => {
    checkAddress()
  }, [formData])
  useEffect(() => {
    localStorage.removeItem("address_data")
  }, [])
  return (
    <>
      <link
        rel="stylesheet"
        type="text/css"
        href="https://ws1.postescanada-canadapost.ca/css/addresscomplete-2.30.min.css?key=bw11-fu43-na37-by38"
      />
    </>
  )
})

export default WaitingListAddressComponent
