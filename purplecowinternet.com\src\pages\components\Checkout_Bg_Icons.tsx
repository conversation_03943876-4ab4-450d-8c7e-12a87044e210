import React from "react"
import PeaceIcon from "../../content/pc_peace.png"
import DancingCow from "../../content/Dancing_Cow.gif"
import HeartIcon from "../../content/pc_heart.png"

interface BgIconProps {
  isCheckoutPage?: boolean
}
const CheckoutBgIcons: React.FC<BgIconProps> = ({ isCheckoutPage }) => {
  return (
    <div
      className={
        "absolute right-2 top-4 sm:right-4 sm:top-6 md:right-6 md:top-10 lg:right-8 lg:top-14 xl:right-10 xl:top-20"
      }
    >
      <div
        className={`flex ${isCheckoutPage ? "" : "flex-col"} items-end gap-2 sm:gap-3 md:gap-4 xl:gap-5`}
      >
        {/* Peace Icon */}
        <img
          src={PeaceIcon}
          className="-rotate-6 w-10 sm:w-12 md:w-14 lg:w-16 xl:w-20 mr-2 sm:mr-4 md:mr-5 lg:mr-6 xl:mr-8"
        />

        {/* Dancing Cow GIF */}
        <img
          src={DancingCow}
          alt="Dancing cow"
          className="-rotate-6 w-20 sm:w-24 md:w-28 lg:w-32 xl:w-40 mr-7 sm:mr-8 md:mr-12 lg:mr-14 xl:mr-16"
        />

        {/* Heart Icon */}
        <img
          src={HeartIcon}
          className="rotate-12 h-10 w-10 sm:h-12 sm:w-12 md:h-14 md:w-14 lg:h-16 lg:w-16 xl:h-20 xl:w-20 mr-2 sm:mr-4 md:mr-5 lg:mr-6 xl:mr-8"
        />
      </div>
    </div>
  )
}

export default CheckoutBgIcons
