import React, { useState, useEffect } from "react"
import Loading from "./Loading"
import { CloseIcon } from "../icons/Icons"

let Modal: React.FunctionComponent<{ close: () => void }> = ({ close }) => {
  let [phone, setPhone] = useState("")
  let [question, setQuestion] = useState("")
  let [submitted, setSubmitted] = useState(false)
  let [once, setOnce] = useState(false)
  let [fetched, setFetched] = useState<{
    error: boolean | unknown
    loading: boolean
    data: any
  }>({
    error: false,
    loading: false,
    data: null,
  })

  useEffect(() => {
    async function fetchIt() {
      setFetched({ loading: true, data: null, error: false })
      try {
        let data = fetch(process.env.GATSBY_LAMBDA + "/text", {
          method: "POST",
          body: JSON.stringify({ phone, question }),
        })
        setFetched({ error: false, loading: false, data })
      } catch (e) {
        setFetched({ error: e, loading: false, data: null })
      }
    }
    if (submitted && !fetched.loading && !fetched.error && !once) {
      fetchIt()
      setOnce(true)
    }
  }, [submitted, close, fetched])

  return (
    <div
      onClick={close}
      className="fixed top-0 bottom-0 left-0 right-0 z-50 flex items-center justify-center w-screen h-screen bg-black bg-opacity-30"
    >
      <div
        onClick={(e) => {
          e.stopPropagation()
        }}
        className="relative w-1/3 max-w-4xl px-12 pb-8 bg-white rounded-lg shadow-lg pt-14"
      >
        <button
          className="absolute text-primary-700 right-12 top-6"
          onClick={close}
        >
          <div className="w-4 h-4">
            <CloseIcon />
          </div>
        </button>
        {fetched.data && (
          <div className="mt-10 mb-8 text-xl font-thin text-center">
            Thanks for sending us a message! We'll shoot you a text back asap!{" "}
            <button
              className="inline-block w-full py-4 mt-8 text-xl font-thin text-white border-0 bg-primary-700"
              onClick={close}
            >
              Close
            </button>
          </div>
        )}
        {fetched.loading && <Loading />}
        {fetched.error && (
          <div>
            There was a problem sending a message, you can try texting us at:
            <a aria-label="Purple cow phone number" href="sms:**************">
              **************
            </a>
          </div>
        )}

        {!submitted && (
          <form
            onSubmit={(e) => {
              e.preventDefault()
              setSubmitted(true)
            }}
          >
            <h2 className="mb-4 text-center">Send Us a Text</h2>
            <input
              className="w-full mb-4"
              placeholder="Cellular/Mobile Number"
              onChange={(e) => setPhone(e.target.value)}
              required
              type="tel"
              value={phone}
            />
            <textarea
              className="w-full mb-4"
              placeholder="Question"
              onChange={(e) => setQuestion(e.target.value)}
              required
              value={question}
              rows={4}
            />
            <input
              className="w-full py-4 text-xl font-thin text-white border-0 bg-primary-700"
              type="submit"
              value="Send"
            />
          </form>
        )}
      </div>
    </div>
  )
}

export default Modal
