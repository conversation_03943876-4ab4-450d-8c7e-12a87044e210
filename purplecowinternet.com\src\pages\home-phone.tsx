import React, { useEffect, useState } from "react"
import Layout from "../components/Layout"
import { Helmet } from "react-helmet"
import axios from "axios"
import { splitDescription } from "../utils"
import { navigate } from "gatsby"
import MessageModal from "./components/common/MessageModal"
import { colorTheme } from "../data/SrtingConstants"
import CommonButtonInternet from "../components/CommonButton"
import RoundedContainer from "../components/RoundedContainer"
import CommonQuestions from "../components/CommonQuestions"
import { PurpleCheckedIcon } from "../icons/Icons"
import { homePhoneQuestions } from "../data/CommonQuestionsData"
import DancingCow from "../content/Dancing_Cow.gif"
import PcPeace from "../content/pc_peace.png"
import { FibreHeading } from "../components/FibreHeading"
const FrontPhonePage = () => {
  const [messagePopup, setMessagePopup] = useState(false)
  const [phones, setPhones] = useState<any>(null)
  const bannerName = "Spicy Deal"

  const fetchHomePhonePlans = async () => {
    try {
      const response = await axios(
        process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
          "/snapshots/latest/services/home-phones/plans.json"
      )
      setPhones(response.data)
    } catch (err) {
      console.error("Failed to fetch phone plans:", err)
    }
  }
  useEffect(() => {
    fetchHomePhonePlans()
  }, [])

  return (
    <Layout>
      <Helmet>
        <title>Phone – Purple Cow Internet 💜🐄</title>
      </Helmet>

      {/* Home Phone, Keep Your Existing Number */}
      <RoundedContainer
        className="mt-5 relative"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <img
          src={DancingCow}
          className="w-32 mt-2 ml-2 md:w-40 lg:w-56 sm:ml-5 sm:mt-5"
          alt="Dancing cow"
        />
        <FibreHeading
          align="text-center"
          color="white"
          content={
            <>
              Home Phone, Keep Your <br className="hidden md:block" /> Existing
              Number
            </>
          }
        />
        <h3 className="!font-sans text-white text-center mt-10 mb-6 sm:mb-1">
          Home phone price can't be beat!
        </h3>
        <div className="flex justify-end">
          <img
            className="w-20 md:w-24 mt-2 mb-1 sm:mt-0 mr-5"
            src={PcPeace}
            alt=""
          />
        </div>
      </RoundedContainer>

      {/* Home phone Spicy Deal */}
      <RoundedContainer className="flex flex-col items-center">
        <div className="flex flex-wrap justify-center gap-6 mt-10">
          {phones
            ?.filter((p: any) => p?.status === "ACTIVE")
            .slice(0, 1)
            .map((homePhone: any, i: number) => (
              <div
                key={i}
                className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
              >
                <div>
                  <h2 className="!font-anton mb-2">
                    {homePhone?.name || "Home Phone"}
                  </h2>
                  <div className="flex my-5">
                    <h1 className="!font-anton">
                      ${homePhone?.billing_period[0]?.monthly?.price || 0}
                    </h1>
                    <p className="text-lg flex items-end ml-2">/month</p>
                  </div>
                  <ul className="plan-list text-[#666666] my-5 mb-10">
                    {splitDescription(homePhone.description).map((line, i) => (
                      <li
                        className="my-2 flex items-center gap-3 !font-sans"
                        key={i}
                      >
                        <PurpleCheckedIcon />
                        {/* remove "✔️" sign and add svg*/}
                        {line?.replace("✔️", "").trim()}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
        </div>

        <h4 className="!font-sans text-black text-center mt-10">
          One time phone number port or new number charge of $55. Shipping
          included free.
        </h4>

        <CommonButtonInternet
          buttonText={"Check My Eligibility"}
          onClick={() => navigate("/join-the-herd")}
          className="px-4 my-5"
        />
      </RoundedContainer>

      {/* Common Questions */}
      <CommonQuestions
        textColor="black"
        bgColor="white"
        questions={homePhoneQuestions}
      />

      {/* Or If You're Ready... */}
      <RoundedContainer
        className="flex flex-col justify-center items-center gap-5 sm:gap-10 p-5 sm:p-10 md:p-20"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <h1 className="!font-anton text-center text-white uppercase">
          Or If You're Ready...
        </h1>
        <h3 className="!font-sans text-white text-center mt-5 lg:w-[60%]">
          If you still have a question, shoot us a message and our team will
          respond within mins
        </h3>

        <div className="flex justify-between gap-6">
          <CommonButtonInternet
            buttonText="Message Us"
            onClick={(e) => {
              const isMobile = /iPhone|iPad|iPod|Android/i.test(
                navigator.userAgent
              )
              if (isMobile) {
                e.preventDefault() // Prevent the default anchor click behavior
                window.location.href =
                  "sms:**************?body=Hey%20Purple%20Cow%20I%20have%20a%20question.%20"
              } else {
                // Open the existing popup on other devices
                setMessagePopup(true)
              }
            }}
            className="px-5 !text-white border border-white"
            bgColor="transparent"
          />
          <CommonButtonInternet
            buttonText="Join the Herd"
            onClick={() => navigate("/join-the-herd")}
            className="px-5"
          />
        </div>
      </RoundedContainer>

      {messagePopup ? (
        <MessageModal closepopup={setMessagePopup}></MessageModal>
      ) : null}
    </Layout>
  )
}

export default FrontPhonePage
