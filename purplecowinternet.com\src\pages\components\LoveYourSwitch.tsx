import React from "react"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreHeading } from "../../components/FibreHeading"
import guaranteedImg from "../../content/Capture.jpg"

const LoveYourSwitch: React.FC = () => {
  return (
    <RoundedContainer className="px-3 sm:px-10" bgColor="white">
      <div className="flex flex-col-reverse md:flex-row">
        <div className="w-full md:w-[50%]">
          <FibreHeading
            color="black"
            align="text-left"
            content={"Love Your Switch, Or It’s Free!"}
          />
          <h3 className="font-sans mt-5 text-center sm:text-start">
            We stand by the quality of our service. If you’re not absolutely in
            love with your Purple Cow Internet experience after the first month
            and you want to leave the herd, we’ll refund your money 100%. No
            hassle, no fine print—just great service, guaranteed.
          </h3>
        </div>
        <div className="w-full flex items-center justify-center md:w-[50%]">
          <img src={guaranteedImg} alt="guaranteed" className="center" />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default LoveYourSwitch
