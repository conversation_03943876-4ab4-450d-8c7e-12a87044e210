import React, { useEffect } from "react"
import { setFormData } from "../../redux/formSlice"
import { useDispatch } from "react-redux"
import { validationRegEx } from "../../data/Regex"

interface GoogleAddressProps {
  inputRef: any
  unitRef: any
  googleAddressType: any
  setShowInputCustomPostalCode: (value: boolean) => void
}
const GoogleAddress: React.FC<GoogleAddressProps> = ({
  inputRef,
  unitRef,
  googleAddressType,
  setShowInputCustomPostalCode,
}) => {
  const GOOGLE_API_KEY = process.env.GATSBY_GOOGLE_API_KEY
  const dispatch = useDispatch()

  useEffect(() => {
    // Avoid adding the script multiple times
    const existingScript = document.querySelector(
      'script[src^="https://maps.googleapis.com/maps/api/js"]'
    )

    if (!existingScript) {
      const script = document.createElement("script")
      script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_API_KEY}&libraries=maps,places&v=beta`
      script.async = true
      script.defer = true
      script.onload = () => {
        setupAutocomplete()
      }
      document.body.appendChild(script)

      return () => {
        // Don't remove the script globally if used elsewhere
        // Instead, just clean up listeners if needed
      }
    } else {
      // If already loaded, just run the autocomplete setup
      if ((window as any).google?.maps?.places) {
        setupAutocomplete()
      } else {
        // Wait for script to load if it's there but not yet initialized
        existingScript.addEventListener("load", setupAutocomplete)
      }
    }

    function setupAutocomplete() {
      if (!inputRef.current) return
      const autocomplete = new (window as any).google.maps.places.Autocomplete(
        inputRef.current,
        {
          types: ["address"],
          componentRestrictions: { country: "ca" },
        }
      )

      autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace()

        const getLong = (type: string) =>
          place.address_components?.find((c: any) => c.types.includes(type))
            ?.long_name || ""

        const getShort = (type: string) =>
          place.address_components?.find((c: any) => c.types.includes(type))
            ?.short_name || ""

        const rawStreet = getShort("route")
        let normalizedStreet = ""
        if (rawStreet) {
          if (rawStreet.endsWith(" Ct")) {
            normalizedStreet = rawStreet.replace(/ Ct$/, " Crt")
          } else if (rawStreet.endsWith(" Crescent")) {
            normalizedStreet = rawStreet.replace(/ Crescent$/, " Cres")
          } else {
            normalizedStreet = rawStreet
          }
        }
        const address = {
          Line1: `${getLong("street_number")} ${normalizedStreet}`.trim(),
          Street: normalizedStreet,
          City: getLong("locality"),
          Province: getShort("administrative_area_level_1"),
          PostalCode: getLong("postal_code"),
          Country: getLong("country"),
          SubBuilding: getLong("subpremise")?.toUpperCase(),
          BuildingNumber: getLong("street_number"),
        }

        // in case of getting bad postal code from Google API
        if (
          address?.PostalCode &&
          !validationRegEx.postalcode_regex.test(address?.PostalCode)
        ) {
          setShowInputCustomPostalCode && setShowInputCustomPostalCode(true)
        } else {
          setShowInputCustomPostalCode && setShowInputCustomPostalCode(false)
        }

        if (unitRef.current && address.SubBuilding) {
          unitRef.current.value = address.SubBuilding
        }

        dispatch(setFormData({ [googleAddressType]: address }))
      })
    }
  }, [])

  return <></>
}

export default GoogleAddress
