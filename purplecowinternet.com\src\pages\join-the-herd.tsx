import React, { useState, useEffect } from "react"
import { useUpdateReferralState, setReferralState } from "../ReferralWrapper"
import { resetMainUrl } from "../customfunction"
import { getSelectedPlanData } from "../customfunction"
import { decrypt, scrollToTop } from "../utils"
import { crudSessionStorage, sessionStorageKeys } from "../sessionStorage"
import customers from "raw-loader!../content/new-pictou-customers.txt"
import fibercustomers from "raw-loader!../content/new-purple-cow-fiber.txt"
import Cookies from "universal-cookie"
import { Address } from "../addressValidator"
import CheckPlans from "./components/CheckPlans"
import PurpleCowLogo from "../content/cow.png"
import TvPlans from "./components/TvPlans"
import HomePhonePlans from "./components/HomePhonePlans"
import { useDispatch, useSelector } from "react-redux"
import { PageStep, setFormData } from "../redux/formSlice"
import ContactInformation from "./components/ContactInformation"
import CheckoutComponent from "./components/CheckoutComponent"
import LoaderComponent from "./components/LoaderComponent"
import { navigate } from "gatsby"
import { Helmet } from "react-helmet"
import axios from "axios"

const joinTheHerd = () => {
  const cookies = new Cookies()
  const useUpdateReferral = useUpdateReferralState()
  const [plans, setPlans]: any = useState({})
  const [addons, setAddons]: any = useState({})
  const formData = useSelector((state: any) => state.form)
  const dispatch = useDispatch()
  const [loadCartDetail, setLoadCartDetail] = useState(false)
  const [exclusions, setExclusions] = useState<null | []>(null)
  const [fibreAddresses, setFibreAddresses] = useState<null | []>(null)

  // check address for pictout and fiber customer
  const pictoutCustomer: any = new Promise((resolve, reject) => {
    try {
      const parsedCustomers = JSON.parse(customers)
      resolve(parsedCustomers)
    } catch (error) {
      reject(error)
    }
  })

  const fetchExcludedAddress = async () => {
    try {
      const response = await axios(
        "https://product-catalog-storage.s3.ca-central-1.amazonaws.com/excludedAddresses.json"
      )
      setExclusions(response?.data || [])
    } catch (err) {
      console.error("Failed to fetch channels:", err)
    }
  }

  const fetchFibreAddress = async () => {
    try {
      const response = await axios(
        "https://product-catalog-storage.s3.ca-central-1.amazonaws.com/purpleFibreAddresses.json"
      )
      setFibreAddresses(response?.data || [])
    } catch (err) {
      console.error("Failed to fetch channels:", err)
    }
  }

  const fiberCustomer: any = new Promise((resolve, reject) => {
    try {
      const fibercustomerss = JSON.parse(fibercustomers)
      resolve(fibercustomerss)
    } catch (error) {
      reject(error)
    }
  })

  const checkpictoutCustomerAddress = async (caddress: any) => {
    let customer_address_list: any = await pictoutCustomer
    if (customer_address_list) {
      return customer_address_list.some((exclusion: any) =>
        Object.keys(exclusion).every(
          (key: string) =>
            exclusion[key as keyof Address] === caddress[key as keyof Address]
        )
      )
    }
    return false
  }

  const checkFibercustomerAddress = async (caddress: any) => {
    let customer_address_list: any = await fiberCustomer
    if (customer_address_list) {
      return customer_address_list.some((exclusion: any) =>
        Object.keys(exclusion).every(
          (key: string) =>
            exclusion[key as keyof Address] === caddress[key as keyof Address]
        )
      )
    }
    return false
  }

  const areAddressesSame = (address1: any, address2: any) => {
    return (
      address1?.Line1 === address2?.Line1 &&
      address1?.SubBuilding === address2?.SubBuilding &&
      address1?.City === address2?.City &&
      address1?.Province === address2?.Province &&
      address1?.PostalCode === address2?.PostalCode
    )
  }

  const calculateInternetTvTotal = (data: any) => {
    let total = 0

    // internet
    const selectedInternetPlan =
      plans?.internet?.length > 0 &&
      plans?.internet?.filter((plan: any) => plan?.id === data?.plan_id)[0]
    if (selectedInternetPlan) {
      total = total + (selectedInternetPlan?.billing?.[0]?.monthly?.price ?? 0)
    }

    // Tv
    const selectedTvPlan = data?.tv_plan
      ? plans?.tv?.filter((plan: any) => plan?.id === data?.tv_plan)[0]
      : null

    if (selectedTvPlan) {
      total = total + (selectedTvPlan?.billing_period?.[0]?.monthly?.price ?? 0)
      total =
        total +
        [
          ...selectedTvPlan?.optional_extra_packages,
          ...selectedTvPlan?.optional_iptv_products,
        ]
          ?.filter((a) => data.tv_optional_plans.includes(a.id))
          ?.map((a) => a.billing_period[0]?.monthly?.price)
          ?.reduce((a, b) => a + b, 0)

      const numberOfSingleChannels = data?.tv_optional_single_plans?.length
      const numberOfBundles = Math.floor(numberOfSingleChannels / 5)
      const remainingItems = numberOfSingleChannels % 5
      total +=
        numberOfBundles *
          addons?.tv?.filter((obj: any) => obj?.api_name === "X_PICK_5")[0]
            .billing_period[0]?.monthly.price +
        remainingItems *
          addons?.tv?.filter(
            (obj: any) => obj?.api_name === "X_SNGL_CHANNEL"
          )[0].billing_period[0]?.monthly?.price
    }
    return total
  }

  //  Check cartUrl if it is valid
  const checkCartUrl = async () => {
    if (typeof window == "undefined") return
    const urlParams = new URLSearchParams(window.location.search)
    const id = urlParams?.get("id")

    if (id) {
      setLoadCartDetail(true)
      try {
        const rep = urlParams?.get("rep")
        const response = await fetch(
          `${process.env.GATSBY_LAMBDA}/query-cart?Cart_ID__c=${id}&Rep=${rep}`
        )

        if (!response?.ok || response?.status != 200) {
          resetMainUrl()
          crudSessionStorage("delete", sessionStorageKeys?.CART_ID)
          dispatch(setFormData({ page: PageStep.CHECK_PLANS }))
          return ""
        }
        const clone = response?.clone() // Clone the response to avoid consuming the original stream
        const responseText = await clone?.text() // Read the response from the clone
        let cartDetails = JSON.parse(decrypt(responseText)) // Decrypt and parse

        if (cartDetails?.Id && !cartDetails?.Checkout_Complete__c) {
          let expires = new Date()
          expires.setTime(expires.getTime() + 2 * 60 * 60 * 1000)
          cartDetails.Coupon_List__c = cartDetails?.Coupon_List__c
            ? cartDetails?.Coupon_List__c
            : ""
          let referrals = cartDetails?.Coupon_List__c?.split(",")?.map(
            (referral: string) => referral?.trim()
          )
          cookies.set("referral", referrals, { expires })
          setReferralState(referrals)
          const baseInternetPlan = plans?.internet?.filter(
            (plan: any) =>
              plan.billing[0]?.monthly?.api_name === cartDetails?.Plan__c
          )

          if (baseInternetPlan?.length < 1) {
            resetMainUrl()
            crudSessionStorage("delete", sessionStorageKeys?.CART_ID)

            dispatch(setFormData({ page: PageStep.CHECK_PLANS }))
            return ""
          }
          const baseInternetPlanId = baseInternetPlan[0]?.id
          let isHomePlan: boolean = false
          if (cartDetails?.Plan_HomePhone_Base_Package__c) {
            let addonPlans: any =
              cartDetails?.Plan_HomePhone_Base_Package__c?.split(",")
            if (
              addonPlans.indexOf("monthly_voip_alianza_res_home_phone") !== -1
            ) {
              isHomePlan = true
            }
          }
          const baseTVPlan = cartDetails?.Plan_TV_Base_Package__c
            ? plans?.tv?.filter(
                (plan: any) =>
                  plan?.billing_period[0]?.monthly?.api_name ===
                  cartDetails?.Plan_TV_Base_Package__c
              )[0]
            : null
          const is_mailing_address = areAddressesSame(
            {
              Line1: cartDetails?.Service_Street__c,
              SubBuilding: cartDetails?.Service_Suite_Unit__c,
              City: cartDetails?.Service_City__c,
              Province: cartDetails?.Service_State_Province__c ?? "NS",
              BuildingNumber: cartDetails?.Service_Street_Number__c,
              PostalCode: cartDetails?.Service_Zip_Postal_Code__c,
            },
            {
              Line1: cartDetails?.Mailing_Street__c,
              SubBuilding: cartDetails?.Mailing_Suite_Unit__c,
              City: cartDetails?.Mailing_City__c,
              Province: cartDetails?.Mailing_State_Province__c ?? "NS",
              BuildingNumber: cartDetails?.Mailing_Street_Number__c,
              PostalCode: cartDetails?.Mailing_Zip_Postal_Code__c,
            }
          )

          const ApiformData = {
            ...formData,
            isUpdateCart: 0,
            customer: {
              first_name: cartDetails?.FirstName,
              last_name: cartDetails?.LastName,
              port_number: cartDetails?.Porting_Phone_Number__c ?? "",
              phone: cartDetails?.Primary_Phone__c,
              email: cartDetails?.Email,
              cf_suggested_turn_on_date: cartDetails?.Requested_Install_Date__c,
              cf_company_name: cartDetails?.Business_Name__c,
            },
            shipping_address: {
              Line1: cartDetails?.Service_Street__c,
              SubBuilding: cartDetails?.Service_Suite_Unit__c,
              City: cartDetails?.Service_City__c,
              Province: cartDetails?.Service_State_Province__c ?? "NS",
              BuildingNumber: cartDetails?.Service_Street_Number__c,
              PostalCode: cartDetails?.Service_Zip_Postal_Code__c,
            },
            SA_SFRecordID: cartDetails?.SA_SFRecordID__c,
            mailing_address: {
              Line1: cartDetails?.Mailing_Street__c,
              SubBuilding: cartDetails?.Mailing_Suite_Unit__c,
              City: cartDetails?.Mailing_City__c,
              Province: cartDetails?.Mailing_State_Province__c ?? "NS",
              BuildingNumber: cartDetails?.Mailing_Street_Number__c,
              PostalCode: cartDetails?.Mailing_Zip_Postal_Code__c,
            },
            is_business: !!cartDetails?.Business_Name__c,
            is_new_phone: isHomePlan
              ? cartDetails?.Porting_Phone_Number__c
                ? false
                : true
              : false,
            is_existing_phone: isHomePlan
              ? cartDetails?.Porting_Phone_Number__c
                ? true
                : false
              : false,
            is_mailing: is_mailing_address,
            plan_id: baseInternetPlanId,
            addressType:
              plans?.internet?.filter(
                (internet: any) => internet?.id === baseInternetPlanId
              )[0]?.provider || "Eastlink",
            page: PageStep.CHECKOUT,
            tv_plan: baseTVPlan?.id ?? null,
            selectedItems: {
              internet: true,
              tv: !!baseTVPlan,
              home_phone: cartDetails?.Plan_HomePhone_Base_Package__c
                ? true
                : false,
              mobile: false,
            },
            tv_optional_plans:
              addons?.tv
                ?.filter((addon: any) =>
                  [
                    ...(cartDetails?.Plan_TV_Extra_Packages__c?.split(",") ||
                      []),
                    ...(cartDetails?.Plan_TV_IPTV_Products__c
                      ? (
                          "monthly_" + cartDetails?.Plan_TV_IPTV_Products__c
                        )?.split(",")
                      : []), // add  monthly_ to match with json data
                  ]?.includes(addon?.billing_period[0]?.monthly?.api_name)
                )
                ?.map((a: any) => a.id) || [],
            addons: cartDetails?.Plan_HomePhone_Base_Package__c
              ? cartDetails?.Plan_HomePhone_Base_Package__c?.split(",")
              : [],
            optional_addons: cartDetails?.Plan_Optional_Addons__c
              ? cartDetails?.Plan_Optional_Addons__c?.split(",")
              : [],
            tv_optional_single_plans_name:
              cartDetails?.Plan_TV_Single_Channels__c
                ? cartDetails?.Plan_TV_Single_Channels__c?.split(",")
                : [],
            tv_optional_single_plans: cartDetails?.Plan_TV_Single_Channels__c
              ? baseTVPlan?.optional_single_channels
                  .filter((ch: any) =>
                    cartDetails?.Plan_TV_Single_Channels__c?.split(
                      ","
                    )?.includes(ch?.api_name)
                  )
                  .map((ch: any) => ch.id)
              : [],
            OptionalPagePlan: baseTVPlan
              ? [
                  ...(baseTVPlan?.optional_extra_packages || []),
                  ...(baseTVPlan?.optional_iptv_products || []),
                ]
              : [],
            OptionalPagePlansIcons: baseTVPlan
              ? baseTVPlan?.optional_single_channels
              : [],
          }
          if (baseTVPlan) {
            const iptvAddons = ApiformData?.addons
              .filter((a: any) =>
                baseTVPlan?.optional_iptv_products
                  .map((p: any) => p?.billing_period[0]?.monthly?.api_name)
                  .includes(a)
              )
              .map(
                (a: any) =>
                  baseTVPlan.optional_iptv_products.filter(
                    (p: any) => p?.billing_period[0]?.monthly?.api_name === a
                  )[0]?.id
              )
            ApiformData!.tv_optional_plans = [
              ...ApiformData?.tv_optional_plans,
              ...iptvAddons,
            ]
            ApiformData!.addons = ApiformData?.addons?.filter(
              (a: any) =>
                !baseTVPlan.optional_iptv_products
                  .map((p: any) => p.billing_period[0]?.monthly?.api_name)
                  .includes(a)
            )
          }
          ApiformData.internet_tv_total = calculateInternetTvTotal(ApiformData)
          let totals = 0

          ;[...(plans?.homePhone || []), ...(addons?.misc || [])]
            ?.filter((item) =>
              ApiformData.addons.includes(
                item?.billing_period[0]?.monthly
                  ? item?.billing_period[0]?.monthly?.api_name
                  : item?.billing_period[0]?.non_recurring?.api_name
              )
            )
            .map((item: any) => {
              totals += item?.billing_period[0]?.monthly
                ? item?.billing_period[0]?.monthly?.price
                : 0
            })
          ApiformData.total = ApiformData?.internet_tv_total + totals
          if (
            await checkpictoutCustomerAddress(ApiformData?.shipping_address)
          ) {
            ApiformData.Service_Area = "Pictou_Customers"
          } else if (
            await checkFibercustomerAddress(formData?.shipping_address)
          ) {
            ApiformData.Service_Area = "Purple_Cow_Fiber_Customer"
          } else {
            dispatch(
              setFormData({
                Service_Area: "Eastlink_Customer",
              })
            )
            ApiformData.Service_Area = "Eastlink_Customer"
          }
          dispatch(setFormData(ApiformData))
          crudSessionStorage("add", sessionStorageKeys?.CART_ID, id, "single")
          useUpdateReferral(referrals)
          dispatch(setFormData({ page: PageStep.CHECKOUT }))
          return true
        }
        resetMainUrl()
        crudSessionStorage("delete", sessionStorageKeys?.CART_ID)

        dispatch(setFormData({ page: PageStep.CHECK_PLANS }))
      } catch (e: any) {
        console.error(e)
        dispatch(setFormData({ page: PageStep.CHECK_PLANS }))
      } finally {
        setLoadCartDetail(false)
      }
    }
  }

  const getAddonsData = async () => {
    try {
      const response = await fetch(process.env.GATSBY_LAMBDA + "/list-addons")
      setAddons(await response.json())
    } catch (error) {
      console.error(error)
    }
  }

  const getPlansData = async () => {
    try {
      const response = await fetch(process.env.GATSBY_LAMBDA + "/list-plans")
      setPlans(await response.json())
    } catch (error) {
      console.error(error)
    }
  }

  const monthEstimatePrice = async () => {
    try {
      let planData: any = getSelectedPlanData(formData, plans, addons, "M")
      dispatch(setFormData({ monthEstimate: null }))

      const addonsSelectedData = [
        ...planData?.all_addons,
        ...planData?.additional_plans,
        ...planData?.optional_plans,
      ]
      let shipping_address = formData?.shipping_address

      // Await API call
      const res = await fetch(process.env.GATSBY_LAMBDA + "/estimate", {
        method: "POST",
        body: JSON.stringify({
          plan_id: planData?.internet_plan_id,
          addons: addonsSelectedData,
          referral: cookies.get("referral")?.join(","),
          billing_address: {
            ...shipping_address,
            country: "CA",
          },
        }),
      })

      const estimate = await res.json()

      if (estimate.status == 200) {
        dispatch(
          setFormData({
            monthEstimate: estimate?.data?.estimate,
          })
        )
      }
    } catch (e) {
      console.error(e)
    }
  }

  useEffect(() => {
    formData?.plan_id && plans?.internet && monthEstimatePrice()
  }, [
    formData?.plan_id,
    formData?.tv_plan,
    formData?.tv_optional_plans,
    formData?.tv_optional_single_plans,
    formData?.addons,
    formData?.optional_addons,
    plans,
  ])

  useEffect(() => {
    if (typeof window == "undefined") return
    const urlParams = new URLSearchParams(window.location.search)
    const id = urlParams?.get("id")
    if (typeof window !== "undefined" && id && plans?.internet?.length > 0) {
      checkCartUrl()
    }
  }, [plans?.internet])

  // if flag gets true then copy shipping address into mailing address
  useEffect(() => {
    if (formData?.is_mailing) {
      dispatch(setFormData({ mailing_address: formData?.shipping_address }))
    }
  }, [formData?.is_mailing, formData?.shipping_address])

  useEffect(() => {
    fetchExcludedAddress()
    getPlansData()
    getAddonsData()
    scrollToTop()
    fetchFibreAddress()
  }, [])

  return (
    <>
      <Helmet>
        <title>Join the herd 💜🐄 </title>
      </Helmet>
      {plans?.internet?.length > 0 && !loadCartDetail && exclusions !== null && fibreAddresses !== null ? (
        <div className="m-1 sm:m-5">
          <img
            onClick={() => {
              navigate("/")
              dispatch(setFormData({ page: PageStep.CHECK_PLANS }))
            }}
            className="w-32"
            src={PurpleCowLogo}
            alt="Purple cow"
          />
          {formData?.page === PageStep.CHECK_PLANS && (
            <CheckPlans exclusions={exclusions} fibreAddresses={fibreAddresses} plans={plans?.internet} />
          )}
          {formData?.page === PageStep.TV_PLAN && (
            <TvPlans addons={addons} tvPlans={plans?.tv} />
          )}
          {formData?.page === PageStep.HOME_PHONE_PLAN && (
            <HomePhonePlans plans={plans} addons={addons} />
          )}
          {formData?.page === PageStep.CONTACT_INFORMATION && (
            <ContactInformation />
          )}
          {formData?.page === PageStep.CHECKOUT && (
            <CheckoutComponent
              plansData={plans}
              addonsData={addons}
              monthEstimatePrice={monthEstimatePrice}
            />
          )}
        </div>
      ) : (
        <LoaderComponent />
      )}
    </>
  )
}

export default joinTheHerd
