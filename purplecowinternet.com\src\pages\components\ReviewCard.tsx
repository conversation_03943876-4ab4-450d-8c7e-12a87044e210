import React from "react"
import Star from "../../../static/images/ReviewStar.png"
interface Testimonial {
  name: string
  rating: 1 | 2 | 3 | 4 | 5 // from 1 to 5
  description: string
}

interface TestimonialListProps {
  testimonials: Testimonial[]
}

const TestimonialList: React.FC<TestimonialListProps> = ({ testimonials }) => {
  if (!testimonials) return
  return (
    <div className="flex justify-around gap-4 p-2 overflow-x-auto custom-scrollbar">
      {testimonials?.length > 0 &&
        testimonials?.map((t, index) => (
          <div
            key={index}
            className="rounded-2xl bg-white p-6 shadow-md max-w-xs flex-shrink-0 scale-95 hover:scale-100 transition-all duration-200"
          >
            <div className="flex justify-center gap-1 mb-2">
              {Array.from({ length: t?.rating }, (_, i) => (
                <img
                  key={i}
                  src={Star} // Update this to your actual star image path
                  alt="Star"
                  className="w-10 h-10"
                />
              ))}
            </div>
            <h3 className="font-bold text-center text-lg mb-2">{t.name}</h3>
            <p className="font-semibold text-center text-black">
              "{t.description}"
            </p>
          </div>
        ))}
    </div>
  )
}

export default TestimonialList
