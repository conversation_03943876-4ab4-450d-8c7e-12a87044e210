import { <PERSON>, navigate } from "gatsby"
import React, { useState, useEffect } from "react"
import { useChargebee, useSiteMetadata } from "../hooks"
import { Helmet } from "react-helmet"
import { BarIcon, CloseIcon } from "../icons/Icons"
import <PERSON><PERSON>owLogo from "../content/cow.png"
import CommonButtonInternet from "./CommonButton"
const Header: React.FunctionComponent = () => {
  let [menuOpen, setMenuOpen] = useState(false)
  useChargebee()

  let isWaitingListMode,
    isServiceOutageMode = useSiteMetadata()
  const [scroll, setScroll] = useState(false)
  useEffect(() => {
    window.addEventListener("scroll", () => {
      setScroll(window.scrollY > 50)
    })
  }, [])

  return (
    <>
      <Helmet>
        <meta property="og:url" content="https://www.purplecowinternet.com/" />
        <meta property="og:type" content="website" />
        <meta
          property="og:title"
          content="Nova Scotia's Fastest Growing Internet Service Provider!"
        />
        <meta
          property="og:description"
          content="Purple Cow is a Nova Scotia based business offering Internet TV and Phone. We offer amazing AMAZING customer service, internet, and are the best deal in town!"
        />
        <meta
          name="description"
          content="Purple Cow is a Nova Scotia based business offering Internet TV and Phone. We offer amazing AMAZING customer service, internet, and are the best deal in town!"
        />

        <meta
          property="og:image"
          content="https://purplecowinternet.com/images/linksharingphoto.png"
        />

        <meta
          property="og:image:secure_url"
          content="https://purplecowinternet.com/images/linksharingphoto.png"
        />

        <meta property="fb:app_id" content="2815092408601788" />

        <meta
          name="facebook-domain-verification"
          content="y51i42mib2v5mror4hyb4pvmpfyzff"
        />
      </Helmet>
      <header
        className={`flex items-center !z-30 bg-white ${scroll ? "shadow-lg" : ""}`}
      >
        <div className="new-header-row px-4 md:mx-10">
          <img
            onClick={() => navigate("/")}
            src={PurpleCowLogo}
            alt=""
            className="w-32 cursor-pointer"
          />

          <div className="flex items-center justify-end sm:hidden">
            <button
              className="self-start block justify-self-end sm:hidden"
              onClick={() => setMenuOpen(true)}
            >
              <div className="w-6 h-6 text-black-700 ml-4">
                <BarIcon />
              </div>
            </button>
          </div>

          {/* Nav show on long display */}
          <nav className="hidden sm:block">
            <ul className="flex items-center justify-end">
              <li>
                <Link to="/internet/">Internet </Link>
              </li>
              <li>
                <Link to="/tv/">TV </Link>
              </li>
              <li>
                <Link to="/home-phone/">Phone </Link>
              </li>
              <li>
                <a
                  href={`https://${process.env.GATSBY_CHARGEBEE_SITE}.chargebeeportal.com`}
                >
                  My Account
                </a>
              </li>
            </ul>
          </nav>

          <span className="hidden sm:block">
            {isWaitingListMode ? (
              <a
                className="inline-block px-4 py-2 text-white cursor-pointer bg-primary-700"
                href="https://purplecowinternet.squarespace.com/waiting-list"
              >
                Join the Waiting List
              </a>
            ) : (
              <CommonButtonInternet
                buttonText="Join the Herd"
                onClick={() => navigate("/join-the-herd/")}
                className="px-5"
              />
            )}
          </span>

          {/* mobile */}
          {menuOpen && (
            <nav className="fixed mobile-menu tracking-wider sm:hidden">
              <button
                className="close-menu"
                onClick={() => setMenuOpen(false)}
                onKeyDown={(e) => e.key === "Enter" && setMenuOpen(false)}
              >
                <CloseIcon />
              </button>
              <ul className="flex flex-col gap-4 text-2xl font-bold text-white">
                <li>
                  <Link to="/internet/">Internet </Link>
                </li>
                <li>
                  <Link to="/tv/">TV </Link>
                </li>
                <li>
                  <Link to="/home-phone/">Phone </Link>
                </li>
                <li>
                  <Link to="/thoughts/">Thoughts</Link>
                </li>
                <li>
                  <a
                    href={`https://${process.env.GATSBY_CHARGEBEE_SITE}.chargebeeportal.com`}
                  >
                    My Account
                  </a>
                </li>
                <li>
                  <Link to="/join-the-herd/">Join the herd</Link>
                </li>
              </ul>
            </nav>
          )}
        </div>
      </header>
    </>
  )
}

export default Header
