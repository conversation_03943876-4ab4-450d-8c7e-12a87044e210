import React from "react"
import Layout from "../../components/Layout"
import { Helmet } from "react-helmet"
import leaderImage from "../../pages/thoughts/images/Leader-purple-cow.png"

export default function FinalistForTheBusiness() {
  return (
    <Layout>
      <Helmet>
        <title>
          Our CEO is a finalist for the Business Leader of the Year 💜🐄
        </title>
      </Helmet>

      <div className="prose flex flex-col prose-lg lg:prose-xl mx-5 sm:mx-10 md:mx-40 xl:mx-80 2xl:mx-96 thought-wrapper">
        <h1>Our CEO is a finalist for the Business Leader of the Year</h1>

        <img
          src={leaderImage}
          alt="Our CEO is a finalist for the Business Leader of the Year"
        />

        <p>
          I'm thrilled to share that alongside four other outstanding
          individuals I've been named a finalist for the "Business Leader of the
          Year Award" by the Halifax Chamber of Commerce. It's truly an honor
          for Purple Cow and a testament to the impact everyone on our team is
          making in our community. But here's the kicker: with utmost humility,
          "We are just getting started."
        </p>

        <p>
          At this very moment, I find myself in Austin on a personal development
          trip, absorbing invaluable lessons from accomplished business leaders.
          The insights are pouring in – from refining our quarterly meetings to
          mastering hiring techniques and magnifying our brand story. The wisdom
          is coming in so thick and fast that I'm scrambling to write it all
          down.
        </p>

        <p>
          My point is: win or lose, being down here in <PERSON> underscores the
          realization that there's always more to learn. So when I say "I'm just
          getting started," it's because I am only a fraction of what I want to
          become. In saying that, I want to say thank you to all the herd
          members for your support — and let's keep pushing the boundaries
          together!
        </p>

        <p>
          <PERSON> Farquhar
          <br />
          CEO
        </p>
      </div>
    </Layout>
  )
}
