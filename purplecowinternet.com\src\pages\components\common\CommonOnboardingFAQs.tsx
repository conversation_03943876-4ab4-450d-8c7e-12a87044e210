import React, { useState } from "react"
import Setup2 from "../../../../static/images/setup2.svg"
import { StaticImage } from "gatsby-plugin-image"
import { Icon } from "../../../icons/Icons"

const CommonQuestion = () => {
  const [open, setOpen] = useState(0)

  const handleOpen = (value: number) => {
    setOpen(open === value ? 0 : value)
  }

  const questions = [
    {
      id: 1,
      question: "How long will it take to get setup?",
      answer:
        "Typical set up time is 4-7 business days. There are some things that may delay setup time like if your home doesn't currently have a coax connection or if a technician is required. However, in any circumstance we strive to resolve these situations quickly and get you connected ASAP.",
    },
    {
      id: 2,
      question: "Do I need a coax/cable port to get installed?",
      answer: (
        <>
          You do, yes. Please take a look around your home for anything that
          looks like the photos below. If you do not have them, we'd be happy to
          get one installed for you. Please text or call us as soon as possible
          to make this change to your order!
          <StaticImage
            className="max-w-lg mx-auto"
            src="../../../content/PurpleCowCoaxLine.png"
            alt="A coax cable"
            width={432}
            height={382}
          />
          <div className="text-xl">Or</div>
          <StaticImage
            className="max-w-lg mx-auto"
            layout="constrained"
            src="../../../content/PurpleCowCoaxPort.png"
            alt="A coax port in an outlet"
            width={432}
            height={800}
          />
        </>
      ),
    },
    {
      id: 3,
      question: "When will I get my modem in the mail?",
      answer:
        "Your modem should arrive the day you requested your internet to start.",
    },
    {
      id: 4,
      question: "What do I do when I get my modem in the mail?",
      answer: (
        <>
          These instructions will be included with your modem. You can also
          watch{" "}
          <a
            href="https://www.youtube.com/watch?v=m8-2Lq1MlgU"
            target="_blank"
            rel="noopener noreferrer"
          >
            this helpful video
          </a>
          .
          <div
            className="max-w-full h-auto mx-auto"
            style={{ marginTop: "16px" }}
          >
            <Setup2 className="w-full h-auto" />
          </div>
        </>
      ),
    },
    {
      id: 5,
      question:
        "Should I schedule the cancellation of my other internet provider now?",
      answer:
        "Not yet. It is best to wait until you are fully hooked up before canceling your current provider. This will ensure you will not go without internet.",
    },
    {
      id: 6,
      question: "Can I keep my existing Phone Number?",
      answer:
        "Absolutely! To ensure we can take it over from your current provider, please do not cancel your service, we will do that on your behalf when we take the number over. To complete the takeover, we do need a copy of your bill from your current provider. It should be the most recent bill, and it should show your name, your phone number, and your account number. You will receive an email from us with instructions on how to submit your bill.",
    },
  ]

  return (
    <>
      <section className="onboarding-section">
        <div className="container mx-auto px-4">
          <div className="max-width1000">
            {questions.map(({ id, question, answer }) => (
              <div key={id} className="border-b">
                <div
                  onClick={() => handleOpen(id)}
                  className="w-full cursor-pointer font-semibold text-left py-4 px-2 flex justify-between items-center text-xl"
                >
                  {question}
                  <Icon id={id} open={open ?? 0} />
                </div>
                {open === id && (
                  <div className="p-2 text-sm text-gray-700">{answer}</div>
                )}{" "}
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  )
}

export default CommonQuestion
