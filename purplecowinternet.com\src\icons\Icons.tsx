import React from "react"
import { colorTheme } from "../data/SrtingConstants"

// Facebook Logo Component
export const FacebookLogo: React.FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="30"
      height="31"
      viewBox="0 0 30 31"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3 15.7279C3 21.7734 7.39077 26.8006 13.1333 27.82V19.0374H10.0933V15.66H13.1333V12.9574C13.1333 9.91744 15.0921 8.22923 17.8626 8.22923C18.7401 8.22923 19.6866 8.364 20.5641 8.49877V11.6067H19.0107C17.5241 11.6067 17.1867 12.3494 17.1867 13.2959V15.66H20.4293L19.8892 19.0374H17.1867V27.82C22.9292 26.8006 27.32 21.7745 27.32 15.7279C27.32 9.0024 21.848 3.5 15.16 3.5C8.472 3.5 3 9.0024 3 15.7279Z"
        fill="white"
      />
    </svg>
  )
}

// Instagram Logo Component
export const InstagramLogo = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
    >
      <path
        d="M12.0009 8.59462C9.85048 8.59462 8.0956 10.3495 8.0956 12.4999C8.0956 14.6503 9.85048 16.4052 12.0009 16.4052C14.1513 16.4052 15.9061 14.6503 15.9061 12.4999C15.9061 10.3495 14.1513 8.59462 12.0009 8.59462ZM23.7138 12.4999C23.7138 10.8827 23.7284 9.28017 23.6376 7.66591C23.5468 5.79091 23.119 4.12685 21.7479 2.75575C20.3739 1.38173 18.7128 0.956926 16.8378 0.866106C15.2206 0.775285 13.6181 0.789934 12.0038 0.789934C10.3866 0.789934 8.78407 0.775285 7.16982 0.866106C5.29482 0.956926 3.63075 1.38466 2.25966 2.75575C0.885637 4.12978 0.460832 5.79091 0.370012 7.66591C0.279192 9.2831 0.29384 10.8856 0.29384 12.4999C0.29384 14.1142 0.279192 15.7196 0.370012 17.3339C0.460832 19.2089 0.888567 20.8729 2.25966 22.244C3.63368 23.6181 5.29482 24.0429 7.16982 24.1337C8.787 24.2245 10.3895 24.2099 12.0038 24.2099C13.621 24.2099 15.2235 24.2245 16.8378 24.1337C18.7128 24.0429 20.3768 23.6151 21.7479 22.244C23.122 20.87 23.5468 19.2089 23.6376 17.3339C23.7313 15.7196 23.7138 14.1171 23.7138 12.4999ZM12.0009 18.5087C8.67568 18.5087 5.99208 15.8251 5.99208 12.4999C5.99208 9.1747 8.67568 6.49111 12.0009 6.49111C15.3261 6.49111 18.0097 9.1747 18.0097 12.4999C18.0097 15.8251 15.3261 18.5087 12.0009 18.5087ZM18.2558 7.64833C17.4794 7.64833 16.8524 7.02138 16.8524 6.24501C16.8524 5.46865 17.4794 4.84169 18.2558 4.84169C19.0321 4.84169 19.6591 5.46865 19.6591 6.24501C19.6593 6.42936 19.6232 6.61195 19.5527 6.78231C19.4823 6.95268 19.3789 7.10747 19.2486 7.23783C19.1182 7.36818 18.9634 7.47154 18.7931 7.54198C18.6227 7.61242 18.4401 7.64856 18.2558 7.64833Z"
        fill="white"
      />
    </svg>
  )
}

export default InstagramLogo

// YouTube Logo Component
export const YouTubeLogo: React.FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="30"
      height="31"
      viewBox="0 0 30 31"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 5.5C16.0688 5.5 17.165 5.5275 18.2275 5.5725L19.4825 5.6325L20.6838 5.70375L21.8088 5.78L22.8362 5.86C23.9515 5.94486 25.001 6.42054 25.7999 7.20325C26.5989 7.98596 27.096 9.02549 27.2038 10.1388L27.2537 10.67L27.3475 11.8075C27.435 12.9862 27.5 14.2712 27.5 15.5C27.5 16.7288 27.435 18.0138 27.3475 19.1925L27.2537 20.33L27.2038 20.8612C27.096 21.9747 26.5987 23.0144 25.7995 23.7971C25.0003 24.5799 23.9505 25.0554 22.835 25.14L21.81 25.2188L20.685 25.2962L19.4825 25.3675L18.2275 25.4275C17.1523 25.4741 16.0762 25.4982 15 25.5C13.9238 25.4982 12.8477 25.4741 11.7725 25.4275L10.5175 25.3675L9.31625 25.2962L8.19125 25.2188L7.16375 25.14C6.04852 25.0551 4.999 24.5795 4.20006 23.7967C3.40111 23.014 2.90399 21.9745 2.79625 20.8612L2.74625 20.33L2.6525 19.1925C2.55729 17.9639 2.50642 16.7323 2.5 15.5C2.5 14.2712 2.565 12.9862 2.6525 11.8075L2.74625 10.67L2.79625 10.1388C2.90395 9.02569 3.40089 7.98632 4.19958 7.20363C4.99827 6.42095 6.04749 5.94515 7.1625 5.86L8.18875 5.78L9.31375 5.70375L10.5163 5.6325L11.7712 5.5725C12.8469 5.52591 13.9234 5.50174 15 5.5ZM12.5 12.4688V18.5312C12.5 19.1088 13.125 19.4688 13.625 19.1812L18.875 16.15C18.9892 16.0842 19.0841 15.9895 19.1501 15.8754C19.2161 15.7613 19.2508 15.6318 19.2508 15.5C19.2508 15.3682 19.2161 15.2387 19.1501 15.1246C19.0841 15.0105 18.9892 14.9158 18.875 14.85L13.625 11.82C13.511 11.7542 13.3816 11.7195 13.2499 11.7195C13.1182 11.7195 12.9888 11.7542 12.8748 11.8201C12.7608 11.886 12.6661 11.9808 12.6003 12.0948C12.5345 12.2089 12.4999 12.3383 12.5 12.47V12.4688Z"
        fill="white"
      />
    </svg>
  )
}

export const CopyIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 5C7 3.34315 8.34315 2 10 2H19C20.6569 2 22 3.34315 22 5V14C22 15.6569 20.6569 17 19 17H17V19C17 20.6569 15.6569 22 14 22H5C3.34315 22 2 20.6569 2 19V10C2 8.34315 3.34315 7 5 7H7V5ZM9 7H14C15.6569 7 17 8.34315 17 10V15H19C19.5523 15 20 14.5523 20 14V5C20 4.44772 19.5523 4 19 4H10C9.44772 4 9 4.44772 9 5V7ZM5 9C4.44772 9 4 9.44772 4 10V19C4 19.5523 4.44772 20 5 20H14C14.5523 20 15 19.5523 15 19V10C15 9.44772 14.5523 9 14 9H5Z"
        fill="gray"
      ></path>
    </svg>
  )
}

export const BarIcon = ({ color = "ccurrentColor" }) => {
  return (
    <svg
      fill="currentColor"
      aria-hidden="true"
      data-icon="bars"
      data-prefix="fas"
      viewBox="0 0 448 512"
    >
      <path
        fill={color}
        d="M0 96c0-17.7 14.3-32 32-32h384c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zm0 160c0-17.7 14.3-32 32-32h384c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zm448 160c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32s14.3-32 32-32h384c17.7 0 32 14.3 32 32z"
      ></path>
    </svg>
  )
}

export const ChevronIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="gray"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M9 18l6-6-6-6"></path>
    </svg>
  )
}

export const LockIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect
        x="5"
        y="11"
        width="14"
        height="10"
        rx="2"
        ry="2"
        fill="currentColor"
      ></rect>
      <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
      <line x1="12" y1="16" x2="12" y2="16"></line>
    </svg>
  )
}

interface IconProps {
  id: number
  open: number
  color?: string
}

export const Icon = ({ id, open, color = "#fbbd69" }: IconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className={`${
        id === open ? "rotate-180" : ""
      } h-7 w-7 transition-transform`}
      fill="none"
      viewBox="0 0 24 24"
      stroke={color}
      strokeWidth={2}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  )
}
interface InternetIconProps {
  selected: boolean
}

export const InternetIcon: React.FC<InternetIconProps> = ({ selected }) => {
  return (
    <svg
      width="120"
      height="138"
      viewBox="0 0 120 138"
      fill="none"
      className="transition-all duration-200"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="120"
        height="120"
        rx="60"
        className="transition-all duration-200"
        fill={selected ? "#d4a1fe" : "#f7f7f7"}
      />
      <path
        d="M27.5144 46.4627C26.2611 47.5893 26.1545 49.5231 27.2762 50.7819C28.3979 52.0407 30.3233 52.1478 31.5765 51.0211L27.5144 46.4627ZM88.4235 51.0211C89.6767 52.1478 91.6021 52.0407 92.7237 50.7819C93.8456 49.5231 93.7387 47.5893 92.4855 46.4627L88.4235 51.0211ZM60 79.8824C58.318 79.8824 56.9546 81.2518 56.9546 82.9412C56.9546 84.6306 58.318 86 60 86V79.8824ZM60.0305 86C61.7125 86 63.0759 84.6306 63.0759 82.9412C63.0759 81.2518 61.7125 79.8824 60.0305 79.8824V86ZM68.1938 73.8871C69.4379 75.0237 71.3641 74.9323 72.4958 73.6827C73.6278 72.4329 73.5364 70.4985 72.2924 69.3615L68.1938 73.8871ZM78.4369 62.5703C79.6809 63.707 81.6072 63.6155 82.7389 62.3657C83.8706 61.1162 83.7795 59.1815 82.5354 58.0448L78.4369 62.5703ZM47.7077 69.3615C46.4635 70.4985 46.3723 72.4329 47.5041 73.6827C48.6359 74.9323 50.562 75.0237 51.8062 73.8871L47.7077 69.3615ZM37.4647 58.0448C36.2205 59.1815 36.1293 61.1162 37.2611 62.3657C38.3929 63.6155 40.3189 63.707 41.5632 62.5703L37.4647 58.0448ZM31.5765 51.0211C39.1217 44.2384 49.0781 40.1176 60 40.1176V34C47.522 34 36.1332 38.7148 27.5144 46.4627L31.5765 51.0211ZM60 40.1176C70.9219 40.1176 80.8784 44.2384 88.4235 51.0211L92.4855 46.4627C83.8669 38.7148 72.4778 34 60 34V40.1176ZM60 86H60.0305V79.8824H60V86ZM60 70.7059C63.1572 70.7059 66.0279 71.9083 68.1938 73.8871L72.2924 69.3615C69.049 66.3984 64.7314 64.5882 60 64.5882V70.7059ZM60 55.4118C67.1017 55.4118 73.5663 58.1204 78.4369 62.5703L82.5354 58.0448C76.5874 52.6105 68.6756 49.2941 60 49.2941V55.4118ZM51.8062 73.8871C53.9721 71.9083 56.8428 70.7059 60 70.7059V64.5882C55.2686 64.5882 50.9511 66.3984 47.7077 69.3615L51.8062 73.8871ZM41.5632 62.5703C46.4339 58.1204 52.8985 55.4118 60 55.4118V49.2941C51.3243 49.2941 43.4128 52.6105 37.4647 58.0448L41.5632 62.5703Z"
        fill={selected ? "#ffffff" : "#d4a1fe"}
        className="transition-all duration-200"
      />
      <path
        d="M34.1956 125.304V137H32.1476V125.304H34.1956ZM38.4522 137H36.5002V129.096H38.3082L38.4682 130.12C38.9642 129.32 39.9242 128.856 40.9962 128.856C42.9802 128.856 44.0042 130.088 44.0042 132.136V137H42.0522V132.6C42.0522 131.272 41.3962 130.632 40.3882 130.632C39.1882 130.632 38.4522 131.464 38.4522 132.744V137ZM48.6101 137H46.6581V130.728H45.1381V129.096H46.6581V126.632H48.6101V129.096H50.1461V130.728H48.6101V137ZM54.8174 137.208C52.4654 137.208 50.8174 135.496 50.8174 133.048C50.8174 130.568 52.4334 128.856 54.7534 128.856C57.1214 128.856 58.6254 130.44 58.6254 132.904V133.496L52.6734 133.512C52.8174 134.904 53.5534 135.608 54.8494 135.608C55.9214 135.608 56.6254 135.192 56.8494 134.44H58.6574C58.3214 136.168 56.8814 137.208 54.8174 137.208ZM54.7694 130.456C53.6174 130.456 52.9134 131.08 52.7214 132.264H56.6894C56.6894 131.176 55.9374 130.456 54.7694 130.456ZM65.1477 129.064V130.872H64.4277C63.0197 130.872 62.1397 131.624 62.1397 133.16V137H60.1877V129.112H62.0277L62.1397 130.264C62.4757 129.48 63.2277 128.952 64.2837 128.952C64.5557 128.952 64.8277 128.984 65.1477 129.064ZM68.4209 137H66.4689V129.096H68.2769L68.4369 130.12C68.9329 129.32 69.8929 128.856 70.9649 128.856C72.9489 128.856 73.9729 130.088 73.9729 132.136V137H72.0209V132.6C72.0209 131.272 71.3649 130.632 70.3569 130.632C69.1569 130.632 68.4209 131.464 68.4209 132.744V137ZM79.4268 137.208C77.0748 137.208 75.4268 135.496 75.4268 133.048C75.4268 130.568 77.0428 128.856 79.3628 128.856C81.7308 128.856 83.2348 130.44 83.2348 132.904V133.496L77.2828 133.512C77.4268 134.904 78.1628 135.608 79.4588 135.608C80.5308 135.608 81.2348 135.192 81.4588 134.44H83.2668C82.9308 136.168 81.4908 137.208 79.4268 137.208ZM79.3788 130.456C78.2268 130.456 77.5228 131.08 77.3308 132.264H81.2988C81.2988 131.176 80.5468 130.456 79.3788 130.456ZM87.4069 137H85.4549V130.728H83.9349V129.096H85.4549V126.632H87.4069V129.096H88.9429V130.728H87.4069V137Z"
        fill="black"
      />
    </svg>
  )
}

export const TvIcon: React.FC<InternetIconProps> = ({ selected }) => {
  return (
    <svg
      width="120"
      height="137"
      viewBox="0 0 120 137"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="120"
        height="120"
        rx="60"
        className="transition-all duration-200"
        fill={selected ? "#d4a1fe" : "#f7f7f7"}
      />
      <path
        d="M78.75 80.1562H41.25C39.9563 80.1562 38.9062 81.2062 38.9062 82.5C38.9062 83.7938 39.9563 84.8438 41.25 84.8438H78.75C80.0438 84.8438 81.0938 83.7938 81.0938 82.5C81.0938 81.2062 80.0438 80.1562 78.75 80.1562ZM82.5 35.1562H37.5C34.1363 35.16 31.41 37.8863 31.4062 41.25V71.25C31.41 74.6138 34.1363 77.34 37.5 77.3438H82.5C85.8638 77.3419 88.5919 74.6138 88.5938 71.25V41.25C88.59 37.8863 85.8638 35.16 82.5 35.1562ZM83.9062 71.25C83.9062 72.0262 83.2762 72.6562 82.5 72.6562H37.5C36.7238 72.6562 36.0938 72.0262 36.0938 71.25V41.25C36.0956 40.4738 36.7238 39.8456 37.5 39.8438H82.5C83.2762 39.8438 83.9062 40.4738 83.9062 41.25V71.25Z"
        fill={selected ? "#ffffff" : "#d4a1fe"}
        className="transition-all duration-200"
      />
      <path
        d="M38.9299 137H36.8819V125.304H38.9299V130.136H44.1139V125.304H46.1619V137H44.1139V132.04H38.9299V137ZM52.9649 137H48.7569V125.304H52.8529C56.3089 125.304 58.6929 127.688 58.6929 131.176C58.6929 134.616 56.3569 137 52.9649 137ZM52.6609 127.192H50.8049V135.112H52.7729C55.1089 135.112 56.5329 133.608 56.5329 131.176C56.5329 128.696 55.0769 127.192 52.6609 127.192ZM63.8892 127.192V125.304H72.7852V127.192H69.3612V137H67.3132V127.192H63.8892ZM77.7869 137L73.3709 125.304H75.5469L78.1069 132.04C78.3629 132.744 78.5869 133.432 78.8429 134.376C79.1309 133.352 79.3709 132.632 79.5949 132.04L82.1229 125.304H84.2509L79.8989 137H77.7869Z"
        fill="black"
      />
    </svg>
  )
}

export const HomePhoneIcon: React.FC<InternetIconProps> = ({ selected }) => {
  return (
    <svg
      width="120"
      height="138"
      viewBox="0 0 120 138"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="transition-all duration-300"
    >
      <rect
        width="120"
        height="120"
        rx="60"
        className="transition-all duration-200"
        fill={selected ? "#d4a1fe" : "#f7f7f7"}
      />
      <path
        d="M72.4092 70.0818L73.9041 68.7354L70.4329 65.5731L68.9384 66.9195L72.4092 70.0818ZM78.9239 68.1714L85.1939 71.2591L87.5449 67.3414L81.275 64.2537L78.9239 68.1714ZM86.4009 77.3958L81.7388 81.5951L85.2096 84.7573L89.8717 80.5581L86.4009 77.3958ZM78.898 82.9471C74.1397 83.3488 61.829 82.9908 48.5001 70.985L45.0292 74.1473C59.5744 87.2486 73.418 87.8884 79.3548 87.3872L78.898 82.9471ZM48.5001 70.985C35.795 59.5411 33.6904 49.9158 33.4284 45.74L28.5136 45.9931C28.8433 51.2487 31.4493 61.9155 45.0292 74.1473L48.5001 70.985ZM53.0137 52.6115L53.9549 51.7637L50.4841 48.6015L49.5428 49.4493L53.0137 52.6115ZM54.7041 40.9565L50.5659 35.9489L46.6252 38.6214L50.7633 43.629L54.7041 40.9565ZM36.6477 34.859L31.4966 39.4988L34.9675 42.661L40.1187 38.0212L36.6477 34.859ZM51.2783 51.0304C49.5428 49.4493 49.5404 49.4514 49.5381 49.4535C49.5373 49.4543 49.5349 49.4564 49.5333 49.4579C49.5301 49.4608 49.5268 49.4638 49.5235 49.4669C49.5168 49.473 49.5099 49.4794 49.5029 49.4861C49.4886 49.4994 49.4736 49.5139 49.4578 49.5294C49.4261 49.5606 49.3914 49.5962 49.3542 49.6364C49.2798 49.7168 49.1954 49.8159 49.1068 49.9343C48.9292 50.1718 48.7356 50.4859 48.5714 50.8808C48.2362 51.6866 48.0565 52.7488 48.2818 54.0642C48.7223 56.6366 50.678 60.0346 55.6877 64.5469L59.1586 61.3846C54.4705 57.1618 53.3537 54.592 53.1463 53.3806C53.0475 52.804 53.1499 52.5203 53.1781 52.4531C53.1952 52.4115 53.2034 52.4073 53.1824 52.4356C53.1719 52.4495 53.1545 52.4713 53.1272 52.5005C53.1138 52.5152 53.0977 52.5318 53.079 52.5503C53.0695 52.5595 53.0596 52.5692 53.0485 52.5794C53.0432 52.5846 53.0376 52.5898 53.0317 52.5951C53.0288 52.5978 53.0258 52.6005 53.0229 52.6032C53.0216 52.6046 53.0193 52.6067 53.0183 52.6074C53.016 52.6094 53.0137 52.6115 51.2783 51.0304ZM55.6877 64.5469C60.699 69.0606 64.4675 70.8173 67.3119 71.2127C68.7654 71.4146 69.9373 71.2535 70.8264 70.9535C71.2622 70.8063 71.6094 70.633 71.872 70.4737C72.0032 70.394 72.1129 70.3182 72.2018 70.2513C72.2464 70.218 72.2858 70.1868 72.3203 70.1582C72.3377 70.144 72.3537 70.1306 72.3685 70.1178C72.3761 70.1113 72.383 70.105 72.3898 70.0991C72.3931 70.0961 72.3964 70.0931 72.3997 70.0902C72.4013 70.0887 72.404 70.0866 72.4046 70.086C72.4069 70.0839 72.4092 70.0818 70.6738 68.5005C68.9384 66.9195 68.9407 66.9175 68.943 66.9154C68.9436 66.9148 68.9459 66.9127 68.9475 66.9112C68.9505 66.9085 68.9535 66.9059 68.9564 66.9032C68.9626 66.8981 68.9682 66.8931 68.9738 66.888C68.9853 66.8782 68.9961 66.8693 69.0063 66.8607C69.0266 66.844 69.045 66.8295 69.0614 66.8176C69.0936 66.7932 69.1175 66.7777 69.1323 66.7685C69.1628 66.7501 69.1566 66.7584 69.108 66.7748C69.0276 66.8018 68.7057 66.8949 68.0585 66.8048C66.7021 66.6166 63.8453 65.6058 59.1586 61.3846L55.6877 64.5469ZM50.5659 35.9489C47.2207 31.901 40.6427 31.2605 36.6477 34.859L40.1187 38.0212C41.8635 36.4495 44.9625 36.6095 46.6252 38.6214L50.5659 35.9489ZM33.4284 45.74C33.3638 44.7111 33.8848 43.6363 34.9675 42.661L31.4966 39.4988C29.7332 41.0871 28.3469 43.3355 28.5136 45.9931L33.4284 45.74ZM81.7388 81.5951C80.8369 82.4075 79.8701 82.8651 78.898 82.9471L79.3548 87.3872C81.7657 87.1835 83.741 86.0803 85.2096 84.7573L81.7388 81.5951ZM53.9549 51.7637C57.1859 48.8534 57.4261 44.2502 54.7041 40.9565L50.7633 43.629C52.0876 45.2313 51.8906 47.3345 50.4841 48.6015L53.9549 51.7637ZM85.1939 71.2591C87.8777 72.5809 88.2958 75.6888 86.4009 77.3958L89.8717 80.5581C94.2704 76.5961 92.914 69.9852 87.5449 67.3414L85.1939 71.2591ZM73.9041 68.7354C75.1643 67.6003 77.1928 67.3191 78.9239 68.1714L81.275 64.2537C77.7189 62.5024 73.3055 62.9861 70.4329 65.5731L73.9041 68.7354Z"
        fill={selected ? "#ffffff" : "#d4a1fe"}
        className="transition-all duration-200"
      />
      <path
        d="M15.2034 137H13.1554V125.304H15.2034V130.136H20.3874V125.304H22.4354V137H20.3874V132.04H15.2034V137ZM24.2784 133.032C24.2784 130.568 26.0544 128.872 28.5024 128.872C30.9504 128.872 32.7264 130.568 32.7264 133.032C32.7264 135.496 30.9504 137.192 28.5024 137.192C26.0544 137.192 24.2784 135.496 24.2784 133.032ZM26.2304 133.032C26.2304 134.472 27.1584 135.448 28.5024 135.448C29.8464 135.448 30.7744 134.472 30.7744 133.032C30.7744 131.592 29.8464 130.616 28.5024 130.616C27.1584 130.616 26.2304 131.592 26.2304 133.032ZM36.2256 137H34.2736V129.096H36.0656L36.2256 130.024C36.6256 129.368 37.4256 128.856 38.5616 128.856C39.7616 128.856 40.5936 129.448 41.0096 130.36C41.4096 129.448 42.3376 128.856 43.5376 128.856C45.4576 128.856 46.5136 130.008 46.5136 131.832V137H44.5776V132.344C44.5776 131.208 43.9696 130.616 43.0416 130.616C42.0976 130.616 41.3776 131.224 41.3776 132.52V137H39.4256V132.328C39.4256 131.224 38.8336 130.632 37.9056 130.632C36.9776 130.632 36.2256 131.24 36.2256 132.52V137ZM51.9659 137.208C49.6139 137.208 47.9659 135.496 47.9659 133.048C47.9659 130.568 49.5819 128.856 51.9019 128.856C54.2699 128.856 55.7739 130.44 55.7739 132.904V133.496L49.8219 133.512C49.9659 134.904 50.7019 135.608 51.9979 135.608C53.0699 135.608 53.7739 135.192 53.9979 134.44H55.8059C55.4699 136.168 54.0299 137.208 51.9659 137.208ZM51.9179 130.456C50.7659 130.456 50.0619 131.08 49.8699 132.264H53.8379C53.8379 131.176 53.0859 130.456 51.9179 130.456ZM66.5606 132.968H63.9846V137H61.9366V125.304H66.5606C68.9126 125.304 70.4326 126.84 70.4326 129.128C70.4326 131.384 68.8966 132.968 66.5606 132.968ZM66.1606 127.128H63.9846V131.144H66.1286C67.5366 131.144 68.2886 130.392 68.2886 129.112C68.2886 127.832 67.5206 127.128 66.1606 127.128ZM73.8194 137H71.8674V125.096H73.8354V130.12C74.3314 129.352 75.2594 128.856 76.3954 128.856C78.3474 128.856 79.3714 130.088 79.3714 132.136V137H77.4194V132.6C77.4194 131.272 76.7634 130.632 75.7714 130.632C74.5394 130.632 73.8194 131.496 73.8194 132.68V137ZM80.8253 133.032C80.8253 130.568 82.6013 128.872 85.0493 128.872C87.4973 128.872 89.2733 130.568 89.2733 133.032C89.2733 135.496 87.4973 137.192 85.0493 137.192C82.6013 137.192 80.8253 135.496 80.8253 133.032ZM82.7773 133.032C82.7773 134.472 83.7053 135.448 85.0493 135.448C86.3933 135.448 87.3213 134.472 87.3213 133.032C87.3213 131.592 86.3933 130.616 85.0493 130.616C83.7053 130.616 82.7773 131.592 82.7773 133.032ZM92.7725 137H90.8205V129.096H92.6285L92.7885 130.12C93.2845 129.32 94.2445 128.856 95.3165 128.856C97.3005 128.856 98.3245 130.088 98.3245 132.136V137H96.3725V132.6C96.3725 131.272 95.7165 130.632 94.7085 130.632C93.5085 130.632 92.7725 131.464 92.7725 132.744V137ZM103.778 137.208C101.426 137.208 99.7784 135.496 99.7784 133.048C99.7784 130.568 101.394 128.856 103.714 128.856C106.082 128.856 107.586 130.44 107.586 132.904V133.496L101.634 133.512C101.778 134.904 102.514 135.608 103.81 135.608C104.882 135.608 105.586 135.192 105.81 134.44H107.618C107.282 136.168 105.842 137.208 103.778 137.208ZM103.73 130.456C102.578 130.456 101.874 131.08 101.682 132.264H105.65C105.65 131.176 104.898 130.456 103.73 130.456Z"
        fill="black"
      />
    </svg>
  )
}

export const Mobile: React.FC<InternetIconProps> = ({ selected }) => {
  return (
    <svg
      width="120"
      height="138"
      viewBox="0 0 120 138"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="120"
        height="120"
        rx="60"
        className="transition-all duration-200"
        fill={selected ? "#d4a1fe" : "#f7f7f7"}
      />
      <g clipPath="url(#clip0_10668_5109)">
        <path
          d="M72.2188 29.5H49.7188C48.4755 29.5 47.2833 29.9939 46.4042 30.8729C45.5251 31.752 45.0312 32.9443 45.0312 34.1875V37.9375C44.7826 37.9375 44.5442 38.0363 44.3683 38.2121C44.1925 38.3879 44.0938 38.6264 44.0938 38.875V42.625C44.0938 42.8736 44.1925 43.1121 44.3683 43.2879C44.5442 43.4637 44.7826 43.5625 45.0312 43.5625V45.4375C44.7826 45.4375 44.5442 45.5363 44.3683 45.7121C44.1925 45.8879 44.0938 46.1264 44.0938 46.375V50.125C44.0938 50.3736 44.1925 50.6121 44.3683 50.7879C44.5442 50.9637 44.7826 51.0625 45.0312 51.0625V84.8125C45.0312 86.0557 45.5251 87.248 46.4042 88.1271C47.2833 89.0061 48.4755 89.5 49.7188 89.5H72.2188C73.462 89.5 74.6542 89.0061 75.5333 88.1271C76.4124 87.248 76.9062 86.0557 76.9062 84.8125V34.1875C76.9062 32.9443 76.4124 31.752 75.5333 30.8729C74.6542 29.9939 73.462 29.5 72.2188 29.5ZM49.7188 31.375H72.2188C72.8901 31.3656 73.5427 31.5967 74.0586 32.0266C74.5744 32.4565 74.9194 33.0567 75.0312 33.7188H72.2188C71.6131 33.7266 71.0169 33.8695 70.4736 34.1371C69.9302 34.4047 69.4535 34.7903 69.0781 35.2656C68.7846 35.6485 68.4092 35.9609 67.9793 36.1799C67.5494 36.3989 67.076 36.5189 66.5938 36.5312H55.3438C54.8615 36.5189 54.3881 36.3989 53.9582 36.1799C53.5283 35.9609 53.1529 35.6485 52.8594 35.2656C52.484 34.7903 52.0073 34.4047 51.4639 34.1371C50.9206 33.8695 50.3244 33.7266 49.7188 33.7188H46.9531C47.0637 33.0647 47.4019 32.4707 47.9079 32.0418C48.414 31.613 49.0554 31.3768 49.7188 31.375ZM72.2188 87.625H49.7188C48.9728 87.625 48.2575 87.3287 47.73 86.8012C47.2026 86.2738 46.9062 85.5584 46.9062 84.8125V34.6562H49.7188C50.201 34.6686 50.6744 34.7886 51.1043 35.0076C51.5342 35.2266 51.9096 35.539 52.2031 35.9219C52.5785 36.3972 53.0552 36.7828 53.5986 37.0504C54.1419 37.318 54.7381 37.4609 55.3438 37.4688H66.5938C67.1994 37.4609 67.7956 37.318 68.3389 37.0504C68.8823 36.7828 69.359 36.3972 69.7344 35.9219C70.0279 35.539 70.4033 35.2266 70.8332 35.0076C71.2631 34.7886 71.7365 34.6686 72.2188 34.6562H75.0312V84.8125C75.0312 85.5584 74.7349 86.2738 74.2075 86.8012C73.68 87.3287 72.9647 87.625 72.2188 87.625Z"
          fill={selected ? "#ffffff" : "#d4a1fe"}
          className="transition-all duration-200"
        />
      </g>
      <path
        d="M38.0456 137H36.0616V125.304H38.0456L41.8216 134.616L45.5976 125.304H47.6136V137H45.6296V133.4C45.6296 131.048 45.6296 130.36 45.7416 129.528L42.7656 137H40.8776L37.9176 129.544C38.0296 130.248 38.0456 131.352 38.0456 132.808V137ZM49.4503 133.032C49.4503 130.568 51.2263 128.872 53.6743 128.872C56.1223 128.872 57.8983 130.568 57.8983 133.032C57.8983 135.496 56.1223 137.192 53.6743 137.192C51.2263 137.192 49.4503 135.496 49.4503 133.032ZM51.4023 133.032C51.4023 134.472 52.3303 135.448 53.6743 135.448C55.0183 135.448 55.9463 134.472 55.9463 133.032C55.9463 131.592 55.0183 130.616 53.6743 130.616C52.3303 130.616 51.4023 131.592 51.4023 133.032ZM61.2535 137H59.4455V125.096H61.3975V130.248C61.9095 129.368 62.9655 128.84 64.1655 128.84C66.4215 128.84 67.7975 130.6 67.7975 133.096C67.7975 135.528 66.3095 137.208 64.0375 137.208C62.8535 137.208 61.8455 136.68 61.3815 135.768L61.2535 137ZM61.4135 133.016C61.4135 134.44 62.2935 135.416 63.6375 135.416C65.0135 135.416 65.8295 134.424 65.8295 133.016C65.8295 131.608 65.0135 130.6 63.6375 130.6C62.2935 130.6 61.4135 131.592 61.4135 133.016ZM70.3278 127.512C69.6558 127.512 69.1278 126.984 69.1278 126.328C69.1278 125.672 69.6558 125.16 70.3278 125.16C70.9678 125.16 71.4958 125.672 71.4958 126.328C71.4958 126.984 70.9678 127.512 70.3278 127.512ZM69.3518 137V129.096H71.3038V137H69.3518ZM75.2885 137H73.3525V125.096H75.2885V137ZM80.8721 137.208C78.5201 137.208 76.8721 135.496 76.8721 133.048C76.8721 130.568 78.4881 128.856 80.8081 128.856C83.1761 128.856 84.6801 130.44 84.6801 132.904V133.496L78.7281 133.512C78.8721 134.904 79.6081 135.608 80.9041 135.608C81.9761 135.608 82.6801 135.192 82.9041 134.44H84.7121C84.3761 136.168 82.9361 137.208 80.8721 137.208ZM80.8241 130.456C79.6721 130.456 78.9681 131.08 78.7761 132.264H82.7441C82.7441 131.176 81.9921 130.456 80.8241 130.456Z"
        fill="black"
      />
      <defs>
        <clipPath id="clip0_10668_5109">
          <rect
            width="60"
            height="60"
            fill="white"
            transform="translate(30.5 29.5)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ArrowDownIcon = () => {
  return (
    <>
      <svg
        width="13"
        height="8"
        viewBox="0 0 13 8"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.63602 5.06111L11.5858 0.111328L13 1.52555L6.63602 7.88951L0.272121 1.52555L1.68632 0.111328L6.63602 5.06111Z"
          fill="#A9A9A9"
        />
      </svg>
    </>
  )
}

export const DownlodSpeedIcon = () => {
  return (
    <>
      <svg
        width="27"
        height="27"
        viewBox="0 0 27 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.69238 15.459L13.5001 20.2667L18.3078 15.459"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.5 20.2662V6.80469"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.5 26.0352C20.4037 26.0352 26 20.4388 26 13.5352C26 6.6316 20.4037 1.03516 13.5 1.03516C6.59644 1.03516 1 6.6316 1 13.5352C1 20.4388 6.59644 26.0352 13.5 26.0352Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </>
  )
}

export const UploadSpeedIcon = () => {
  return (
    <>
      <svg
        width="27"
        height="27"
        viewBox="0 0 27 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.3076 11.6113L13.4999 6.80364L8.69223 11.6113"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.5 6.80409L13.5 20.2656"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.5 1.03516C6.59635 1.03516 1 6.6315 1 13.5352C1 20.4387 6.59635 26.0352 13.5 26.0352C20.4036 26.0352 26 20.4387 26 13.5352C26 6.6315 20.4036 1.03516 13.5 1.03516Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </>
  )
}

export const PriceLockIcon = () => {
  return (
    <>
      <svg
        width="27"
        height="32"
        viewBox="0 0 27 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M23.5 12.5742H3.5C2.1193 12.5742 1 13.6074 1 14.8819V28.7281C1 30.0026 2.1193 31.0358 3.5 31.0358H23.5C24.8807 31.0358 26 30.0026 26 28.7281V14.8819C26 13.6074 24.8807 12.5742 23.5 12.5742Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M22.25 12.5736V9.11208C22.25 6.96994 21.3282 4.91554 19.6872 3.40084C18.0462 1.88612 15.8207 1.03516 13.5 1.03516C11.1793 1.03516 8.95375 1.88612 7.31282 3.40084C5.67187 4.91554 4.75 6.96994 4.75 9.11208V12.5736"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.6943 18.726C16.5846 18.4393 16.4152 18.1787 16.2 17.9567C15.7423 17.4846 15.077 17.1875 14.3366 17.1875H12.4015C11.169 17.1875 10.1699 18.1098 10.1699 19.2474C10.1699 20.2155 10.9001 21.0529 11.9246 21.2598L14.8708 21.8547C16.0185 22.0864 16.8366 23.0253 16.8366 24.1098C16.8366 25.3842 15.7173 26.4183 14.3366 26.4183H12.6699C11.5814 26.4183 10.6554 25.776 10.3122 24.8798"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.5 17.1905V14.8828"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.5 28.7276V26.4199"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </>
  )
}

export const GigaHeartIcon = () => {
  return (
    <>
      <svg
        width="25"
        height="26"
        viewBox="0 0 25 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22.3212 1.82143L2.67836 1.82143C1.69211 1.82143 0.892648 2.62089 0.892648 3.60714L0.892648 21.4643C0.892648 21.9379 1.08086 22.3921 1.41568 22.727C1.75051 23.0619 2.20479 23.25 2.67836 23.25L22.3212 23.25C22.7948 23.25 23.249 23.0619 23.5839 22.727C23.9188 22.3921 24.1069 21.9379 24.1069 21.4643L24.1069 3.60714C24.1069 3.13357 23.9188 2.67929 23.5839 2.34446C23.249 2.00964 22.7948 1.82143 22.3212 1.82143Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M24.1069 17.8926L0.892648 17.8926"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.94043 7.74581C10.3333 4.35296 15.869 4.35296 19.2619 7.74581"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.1631 10.4839C11.6964 8.95067 14.3248 8.95067 15.8581 10.4839"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.0128 14.6585C12.7663 14.6585 12.5664 14.4585 12.5664 14.2121C12.5664 13.9654 12.7663 13.7656 13.0128 13.7656"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.0127 14.6585C13.2592 14.6585 13.4591 14.4585 13.4591 14.2121C13.4591 13.9654 13.2592 13.7656 13.0127 13.7656"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M20.3874 20.4329C20.5618 20.2585 20.5618 19.9759 20.3874 19.8015C20.2131 19.6272 19.9304 19.6272 19.7561 19.8015C19.5818 19.9759 19.5818 20.2585 19.7561 20.4329C19.9304 20.6072 20.2131 20.6072 20.3874 20.4329Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </>
  )
}

export const CheckedPurpleIcon = () => {
  return (
    <>
      <svg
        width="20"
        height="21"
        viewBox="0 0 20 21"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.0013 18.3678C5.39893 18.3678 1.66797 14.6368 1.66797 10.0345C1.66797 5.43213 5.39893 1.70117 10.0013 1.70117C14.6036 1.70117 18.3346 5.43213 18.3346 10.0345C18.3346 14.6368 14.6036 18.3678 10.0013 18.3678ZM9.17013 13.3678L15.0627 7.47528L13.8842 6.29677L9.17013 11.0108L6.81314 8.65375L5.63463 9.83234L9.17013 13.3678Z"
          fill="#6D24AE"
        />
      </svg>
    </>
  )
}

export const InfinityIcon = () => {
  return (
    <>
      <svg
        width="25"
        height="26"
        viewBox="0 0 25 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.75 17.893C19.4707 17.9621 20.1976 17.871 20.8791 17.6262C21.5605 17.3815 22.1793 16.989 22.6912 16.4771C23.2032 15.9651 23.5955 15.3463 23.8403 14.665C24.0851 13.9836 24.1071 12.5358 24.1071 12.5358C24.1071 12.5358 24.0851 11.0881 23.8403 10.4067C23.5955 9.72533 23.2032 9.10652 22.6912 8.59458C22.1793 8.08263 21.5605 7.6902 20.8791 7.44543C20.1976 7.20065 19.4707 7.10956 18.75 7.17868C13.8393 7.17868 11.1607 17.893 6.24997 17.893C5.52927 17.9621 4.8022 17.871 4.12084 17.6262C3.43946 17.3815 2.82064 16.989 2.3087 16.4771C1.79675 15.9651 1.40434 15.3463 1.15956 14.665C0.914785 13.9836 0.892822 12.5358 0.892822 12.5358C0.892822 12.5358 0.914785 11.0881 1.15956 10.4067C1.40434 9.72533 1.79675 9.10652 2.3087 8.59458C2.82064 8.08263 3.43946 7.6902 4.12084 7.44543C4.8022 7.20065 5.52927 7.10956 6.24997 7.17868C11.1607 7.17868 13.8393 17.893 18.75 17.893Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </>
  )
}

export const CallIcon = () => {
  return (
    <>
      <svg
        width="25"
        height="26"
        viewBox="0 0 25 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.9689 22.7053C10.0168 23.3196 8.8822 23.5873 7.75593 23.4633C6.62968 23.3394 5.58041 22.8316 4.78457 22.0251L4.08884 21.3448C3.78382 21.0328 3.61304 20.6139 3.61304 20.1775C3.61304 19.7412 3.78382 19.3223 4.08884 19.0101L7.04186 16.0881C7.35123 15.7841 7.76764 15.6137 8.20141 15.6137C8.63518 15.6137 9.05161 15.7841 9.36098 16.0881C9.67297 16.3932 10.0919 16.5639 10.5283 16.5639C10.9646 16.5639 11.3836 16.3932 11.6956 16.0881L16.3338 11.4499C16.4885 11.2974 16.6114 11.1157 16.6953 10.9153C16.7791 10.7149 16.8223 10.4998 16.8223 10.2826C16.8223 10.0653 16.7791 9.85028 16.6953 9.64988C16.6114 9.44949 16.4885 9.26778 16.3338 9.11529C16.0298 8.80592 15.8594 8.38951 15.8594 7.95574C15.8594 7.52196 16.0298 7.10554 16.3338 6.79617L19.2714 3.85862C19.5833 3.5536 20.0023 3.38281 20.4387 3.38281C20.8749 3.38281 21.294 3.5536 21.606 3.85862L22.2862 4.55435C23.0928 5.35019 23.6006 6.39946 23.7246 7.52572C23.8485 8.65199 23.5808 9.78656 22.9665 10.7387C19.766 15.4554 15.6938 19.5171 10.9689 22.7053Z"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.3819 2.10156L6.55277 7.93071C5.85539 8.62806 4.72475 8.62806 4.02739 7.93071L1.45703 5.36033"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.25024 2.10156H12.3823V6.23367"
          stroke="black"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </>
  )
}

export const CheckoutGuaranteedIcon = () => {
  return (
    <>
      <svg
        width="66"
        height="65"
        viewBox="0 0 66 65"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M28.2822 30.4464L31.0826 33.645C31.3399 33.9402 31.8031 33.9158 32.0306 33.6016L37.7181 25.7285"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M31.5563 56.388C31.9713 56.7039 32.4784 56.8749 32.9999 56.8749C33.5214 56.8749 34.0285 56.7039 34.4434 56.388C38.8715 53.0649 51.9582 42.2126 51.9582 29.8355V16.4835C51.961 16.139 51.836 15.8058 51.6074 15.5482C51.3788 15.2906 51.0628 15.1269 50.7205 15.0887C45.2974 14.5334 40.0999 12.6284 35.6026 9.54742L33.9505 8.41805C33.6707 8.2259 33.3393 8.12305 32.9999 8.12305C32.6605 8.12305 32.329 8.2259 32.0493 8.41805L30.3972 9.54742C25.8999 12.6284 20.7023 14.5334 15.2793 15.0887C14.937 15.1269 14.621 15.2906 14.3924 15.5482C14.1637 15.8058 14.0388 16.139 14.0415 16.4835V29.8355C14.0415 42.2126 27.1282 53.0649 31.5563 56.3826"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </>
  )
}

export const EditIcon = () => {
  return (
    <svg
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="44"
        width="44"
        height="44"
        rx="12"
        transform="rotate(90 44 0)"
        fill={colorTheme.GOLD}
      />
      <path
        d="M25.1066 19.979L23.9281 18.8005L16.1667 26.562V27.7405H17.3452L25.1066 19.979ZM26.2851 18.8005L27.4636 17.622L26.2851 16.4435L25.1066 17.622L26.2851 18.8005ZM18.0355 29.4071H14.5V25.8715L25.6958 14.6757C26.0213 14.3503 26.5489 14.3503 26.8743 14.6757L29.2314 17.0327C29.5568 17.3582 29.5568 17.8858 29.2314 18.2112L18.0355 29.4071Z"
        fill="black"
      />
    </svg>
  )
}

export const PlusIcon = () => {
  return (
    <svg
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="44"
        width="44"
        height="44"
        rx="12"
        transform="rotate(90 44 0)"
        fill={colorTheme.GOLD}
      />
      <path
        d="M22 14V30M14 22H30"
        stroke="black"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  )
}

export const InternetIconCheckout = () => {
  return (
    <svg
      width="82"
      height="82"
      viewBox="0 0 82 82"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="82" height="82" rx="41" fill={colorTheme.MEDIUM_PURPLE} />
      <path
        d="M8.51443 27.4627C7.26114 28.5893 7.15448 30.5231 8.27622 31.7819C9.39792 33.0407 11.3233 33.1478 12.5765 32.0211L8.51443 27.4627ZM69.4235 32.0211C70.6767 33.1478 72.6021 33.0407 73.7237 31.7819C74.8456 30.5231 74.7387 28.5893 73.4855 27.4627L69.4235 32.0211ZM41 60.8824C39.318 60.8824 37.9546 62.2518 37.9546 63.9412C37.9546 65.6306 39.318 67 41 67V60.8824ZM41.0305 67C42.7125 67 44.0759 65.6306 44.0759 63.9412C44.0759 62.2518 42.7125 60.8824 41.0305 60.8824V67ZM49.1938 54.8871C50.4379 56.0237 52.3641 55.9323 53.4958 54.6827C54.6278 53.4329 54.5364 51.4985 53.2924 50.3615L49.1938 54.8871ZM59.4369 43.5703C60.6809 44.707 62.6072 44.6155 63.7389 43.3657C64.8706 42.1162 64.7795 40.1815 63.5354 39.0448L59.4369 43.5703ZM28.7077 50.3615C27.4635 51.4985 27.3723 53.4329 28.5041 54.6827C29.6359 55.9323 31.562 56.0237 32.8062 54.8871L28.7077 50.3615ZM18.4647 39.0448C17.2205 40.1815 17.1293 42.1162 18.2611 43.3657C19.3929 44.6155 21.3189 44.707 22.5632 43.5703L18.4647 39.0448ZM12.5765 32.0211C20.1217 25.2384 30.0781 21.1176 41 21.1176V15C28.522 15 17.1332 19.7148 8.51443 27.4627L12.5765 32.0211ZM41 21.1176C51.9219 21.1176 61.8784 25.2384 69.4235 32.0211L73.4855 27.4627C64.8669 19.7148 53.4778 15 41 15V21.1176ZM41 67H41.0305V60.8824H41V67ZM41 51.7059C44.1572 51.7059 47.0279 52.9083 49.1938 54.8871L53.2924 50.3615C50.049 47.3984 45.7314 45.5882 41 45.5882V51.7059ZM41 36.4118C48.1017 36.4118 54.5663 39.1204 59.4369 43.5703L63.5354 39.0448C57.5874 33.6105 49.6756 30.2941 41 30.2941V36.4118ZM32.8062 54.8871C34.9721 52.9083 37.8428 51.7059 41 51.7059V45.5882C36.2686 45.5882 31.9511 47.3984 28.7077 50.3615L32.8062 54.8871ZM22.5632 43.5703C27.4339 39.1204 33.8985 36.4118 41 36.4118V30.2941C32.3243 30.2941 24.4128 33.6105 18.4647 39.0448L22.5632 43.5703Z"
        fill="white"
      />
    </svg>
  )
}

export const HomePhoneIconCheckout = () => {
  return (
    <svg
      width="82"
      height="82"
      viewBox="0 0 82 82"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="82" height="82" rx="41" fill={colorTheme.MEDIUM_PURPLE} />
      <path
        d="M52.9092 51.0818L54.4041 49.7354L50.9329 46.5731L49.4384 47.9195L52.9092 51.0818ZM59.4239 49.1714L65.6939 52.2591L68.0449 48.3414L61.775 45.2537L59.4239 49.1714ZM66.9009 58.3958L62.2388 62.5951L65.7096 65.7573L70.3717 61.5581L66.9009 58.3958ZM59.398 63.9471C54.6397 64.3488 42.329 63.9908 29.0001 51.985L25.5292 55.1473C40.0744 68.2486 53.918 68.8884 59.8548 68.3872L59.398 63.9471ZM29.0001 51.985C16.295 40.5411 14.1904 30.9158 13.9284 26.74L9.01362 26.9931C9.34331 32.2487 11.9493 42.9155 25.5292 55.1473L29.0001 51.985ZM33.5137 33.6115L34.4549 32.7637L30.9841 29.6015L30.0428 30.4493L33.5137 33.6115ZM35.2041 21.9565L31.0659 16.9489L27.1252 19.6214L31.2633 24.629L35.2041 21.9565ZM17.1477 15.859L11.9966 20.4988L15.4675 23.661L20.6187 19.0212L17.1477 15.859ZM31.7783 32.0304C30.0428 30.4493 30.0404 30.4514 30.0381 30.4535C30.0373 30.4543 30.0349 30.4564 30.0333 30.4579C30.0301 30.4608 30.0268 30.4638 30.0235 30.4669C30.0168 30.473 30.0099 30.4794 30.0029 30.4861C29.9886 30.4994 29.9736 30.5139 29.9578 30.5294C29.9261 30.5606 29.8914 30.5962 29.8542 30.6364C29.7798 30.7168 29.6954 30.8159 29.6068 30.9343C29.4292 31.1718 29.2356 31.4859 29.0714 31.8808C28.7362 32.6866 28.5565 33.7488 28.7818 35.0642C29.2223 37.6366 31.178 41.0346 36.1877 45.5469L39.6586 42.3846C34.9705 38.1618 33.8537 35.592 33.6463 34.3806C33.5475 33.804 33.6499 33.5203 33.6781 33.4531C33.6952 33.4115 33.7034 33.4073 33.6824 33.4356C33.6719 33.4495 33.6545 33.4713 33.6272 33.5005C33.6138 33.5152 33.5977 33.5318 33.579 33.5503C33.5695 33.5595 33.5596 33.5692 33.5485 33.5794C33.5432 33.5846 33.5376 33.5898 33.5317 33.5951C33.5288 33.5978 33.5258 33.6005 33.5229 33.6032C33.5216 33.6046 33.5193 33.6067 33.5183 33.6074C33.516 33.6094 33.5137 33.6115 31.7783 32.0304ZM36.1877 45.5469C41.199 50.0606 44.9675 51.8173 47.8119 52.2127C49.2654 52.4146 50.4373 52.2535 51.3264 51.9535C51.7622 51.8063 52.1094 51.633 52.372 51.4737C52.5032 51.394 52.6129 51.3182 52.7018 51.2513C52.7464 51.218 52.7858 51.1868 52.8203 51.1582C52.8377 51.144 52.8537 51.1306 52.8685 51.1178C52.8761 51.1113 52.883 51.105 52.8898 51.0991C52.8931 51.0961 52.8964 51.0931 52.8997 51.0902C52.9013 51.0887 52.904 51.0866 52.9046 51.086C52.9069 51.0839 52.9092 51.0818 51.1738 49.5005C49.4384 47.9195 49.4407 47.9175 49.443 47.9154C49.4436 47.9148 49.4459 47.9127 49.4475 47.9112C49.4505 47.9085 49.4535 47.9059 49.4564 47.9032C49.4626 47.8981 49.4682 47.8931 49.4738 47.888C49.4853 47.8782 49.4961 47.8693 49.5063 47.8607C49.5266 47.844 49.545 47.8295 49.5614 47.8176C49.5936 47.7932 49.6175 47.7777 49.6323 47.7685C49.6628 47.7501 49.6566 47.7584 49.608 47.7748C49.5276 47.8018 49.2057 47.8949 48.5585 47.8048C47.2021 47.6166 44.3453 46.6058 39.6586 42.3846L36.1877 45.5469ZM31.0659 16.9489C27.7207 12.901 21.1427 12.2605 17.1477 15.859L20.6187 19.0212C22.3635 17.4495 25.4625 17.6095 27.1252 19.6214L31.0659 16.9489ZM13.9284 26.74C13.8638 25.7111 14.3848 24.6363 15.4675 23.661L11.9966 20.4988C10.2332 22.0871 8.84687 24.3355 9.01362 26.9931L13.9284 26.74ZM62.2388 62.5951C61.3369 63.4075 60.3701 63.8651 59.398 63.9471L59.8548 68.3872C62.2657 68.1835 64.241 67.0803 65.7096 65.7573L62.2388 62.5951ZM34.4549 32.7637C37.6859 29.8534 37.9261 25.2502 35.2041 21.9565L31.2633 24.629C32.5876 26.2313 32.3906 28.3345 30.9841 29.6015L34.4549 32.7637ZM65.6939 52.2591C68.3777 53.5809 68.7958 56.6888 66.9009 58.3958L70.3717 61.5581C74.7704 57.5961 73.414 50.9852 68.0449 48.3414L65.6939 52.2591ZM54.4041 49.7354C55.6643 48.6003 57.6928 48.3191 59.4239 49.1714L61.775 45.2537C58.2189 43.5024 53.8055 43.9861 50.9329 46.5731L54.4041 49.7354Z"
        fill="white"
      />
    </svg>
  )
}

export const TvIconCheckout = () => {
  return (
    <svg
      width="82"
      height="82"
      viewBox="0 0 82 82"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="82" height="82" rx="41" fill={colorTheme.MEDIUM_PURPLE} />
      <path
        d="M59.75 61.1562H22.25C20.9563 61.1562 19.9062 62.2062 19.9062 63.5C19.9062 64.7938 20.9563 65.8438 22.25 65.8438H59.75C61.0438 65.8438 62.0938 64.7938 62.0938 63.5C62.0938 62.2062 61.0438 61.1562 59.75 61.1562ZM63.5 16.1562H18.5C15.1363 16.16 12.41 18.8863 12.4062 22.25V52.25C12.41 55.6138 15.1363 58.34 18.5 58.3438H63.5C66.8638 58.3419 69.5919 55.6138 69.5938 52.25V22.25C69.59 18.8863 66.8638 16.16 63.5 16.1562ZM64.9062 52.25C64.9062 53.0262 64.2762 53.6562 63.5 53.6562H18.5C17.7238 53.6562 17.0938 53.0262 17.0938 52.25V22.25C17.0956 21.4738 17.7238 20.8456 18.5 20.8438H63.5C64.2762 20.8438 64.9062 21.4738 64.9062 22.25V52.25Z"
        fill="white"
      />
    </svg>
  )
}

export const RightArrowIcon = () => {
  return (
    <svg
      width="9"
      height="14"
      viewBox="0 0 9 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.56068 7.0007L0.610901 2.05093L2.02512 0.636719L8.38908 7.0007L2.02512 13.3646L0.610901 11.9504L5.56068 7.0007Z"
        fill="black"
      />
    </svg>
  )
}

export const CardIcon = () => {
  return (
    <svg
      width="32"
      height="26"
      viewBox="0 0 32 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M28.5714 4.51758H3.42854C2.16617 4.51758 1.14282 5.31708 1.14282 6.30329V19.6961C1.14282 20.6824 2.16617 21.4819 3.42854 21.4819H28.5714C29.8338 21.4819 30.8571 20.6824 30.8571 19.6961V6.30329C30.8571 5.31708 29.8338 4.51758 28.5714 4.51758Z"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.14282 10.7676H30.8571"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.7142 17.0176H25.1428"
        stroke="black"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const CreditCardsIcons = () => {
  return (
    <div className="flex gap-2">
      <svg
        width="34"
        height="31"
        viewBox="0 0 34 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.13957 27.0422V25.4972C6.14977 25.3672 6.12868 25.2369 6.07771 25.1147C6.02674 24.9926 5.94706 24.8815 5.84399 24.789C5.74092 24.6964 5.61683 24.6244 5.47998 24.5778C5.34313 24.5312 5.19669 24.511 5.05041 24.5187C4.85896 24.5076 4.66766 24.5424 4.49647 24.6194C4.32528 24.6964 4.18045 24.8128 4.07709 24.9565C3.98289 24.8171 3.84815 24.7029 3.68705 24.6258C3.52595 24.5488 3.34447 24.5118 3.1617 24.5187C3.00239 24.5116 2.84375 24.5416 2.70149 24.6057C2.55922 24.6699 2.43827 24.7659 2.3506 24.8844V24.5806H1.74805V27.0422H2.35637V25.6774C2.34321 25.5932 2.35121 25.5075 2.37977 25.4263C2.40833 25.3452 2.45676 25.2707 2.52156 25.2081C2.58637 25.1456 2.66595 25.0966 2.75459 25.0647C2.84323 25.0327 2.93873 25.0187 3.03423 25.0234C3.43398 25.0234 3.63678 25.2552 3.63678 25.6723V27.0422H4.24511V25.6774C4.23294 25.5934 4.24162 25.508 4.27055 25.4272C4.29948 25.3465 4.34795 25.2723 4.41255 25.21C4.47715 25.1477 4.55632 25.0987 4.64449 25.0664C4.73267 25.0342 4.82772 25.0195 4.92296 25.0234C5.33427 25.0234 5.53125 25.2552 5.53125 25.6723V27.0422H6.13957ZM15.1428 24.5806H14.1579V23.8338H13.5496V24.5806H12.9876V25.0698H13.5496V26.1924C13.5496 26.764 13.7987 27.104 14.5113 27.104C14.7775 27.1047 15.0386 27.0386 15.2644 26.9134L15.0907 26.4551C14.9291 26.5408 14.7457 26.5886 14.5576 26.5941C14.2564 26.5941 14.1579 26.4293 14.1579 26.1822V25.0698H15.1428V24.5806ZM20.2817 24.5187C20.1348 24.5149 19.9894 24.5463 19.861 24.6098C19.7325 24.6732 19.6257 24.7663 19.5517 24.8792V24.5806H18.955V27.0422H19.5575V25.662C19.5575 25.2552 19.7545 25.0286 20.1485 25.0286C20.2773 25.027 20.4053 25.048 20.525 25.0904L20.7104 24.5857C20.5728 24.5419 20.4278 24.5193 20.2817 24.5187ZM12.5125 24.7762C12.1715 24.596 11.7793 24.5066 11.3828 24.5187C10.6817 24.5187 10.2299 24.8174 10.2299 25.3066C10.2299 25.7084 10.5659 25.9556 11.1858 26.0328L11.4697 26.0688C11.7999 26.11 11.9563 26.1873 11.9563 26.3263C11.9563 26.5168 11.7361 26.625 11.3248 26.625C10.994 26.6335 10.6695 26.5428 10.4036 26.3675L10.1198 26.7846C10.4707 27.0025 10.8911 27.1145 11.319 27.104C12.1185 27.104 12.582 26.7692 12.582 26.3006C12.582 25.868 12.217 25.6414 11.6145 25.5641L11.3307 25.5281C11.0699 25.4972 10.8614 25.4509 10.8614 25.2861C10.8614 25.1058 11.0583 24.9977 11.3886 24.9977C11.6918 25.0017 11.9889 25.0744 12.2518 25.2088L12.5125 24.7762ZM28.6534 24.5187C28.5065 24.5149 28.3611 24.5463 28.2327 24.6098C28.1042 24.6732 27.9973 24.7663 27.9234 24.8792V24.5806H27.3267V27.0422H27.9292V25.662C27.9292 25.2552 28.1262 25.0286 28.5201 25.0286C28.649 25.027 28.777 25.048 28.8968 25.0904L29.0821 24.5857C28.9445 24.5419 28.7995 24.5193 28.6534 24.5187ZM20.89 25.8113C20.882 25.9847 20.9149 26.1577 20.9866 26.3191C21.0584 26.4806 21.1673 26.6269 21.3065 26.7486C21.4456 26.8704 21.6119 26.9649 21.7946 27.0261C21.9773 27.0873 22.1725 27.1138 22.3674 27.104C22.7291 27.1201 23.0846 27.0154 23.3639 26.8104L23.0742 26.3778C22.8659 26.519 22.6119 26.5966 22.35 26.5992C22.1242 26.5871 21.9122 26.4988 21.7571 26.3523C21.6021 26.2059 21.5158 26.0124 21.5158 25.8113C21.5158 25.6103 21.6021 25.4168 21.7571 25.2703C21.9122 25.1239 22.1242 25.0356 22.35 25.0234C22.6119 25.0261 22.8659 25.1037 23.0742 25.2449L23.3639 24.8123C23.0846 24.6073 22.7291 24.5026 22.3674 24.5187C22.1725 24.5089 21.9774 24.5354 21.7947 24.5966C21.6119 24.6578 21.4457 24.7523 21.3065 24.874C21.1673 24.9958 21.0584 25.1421 20.9867 25.3035C20.9149 25.4649 20.882 25.6379 20.89 25.8113ZM26.5329 25.8113V24.5806H25.9304V24.8792C25.8276 24.7614 25.6945 24.6672 25.5423 24.6045C25.3901 24.5418 25.2232 24.5124 25.0556 24.5187C24.6822 24.535 24.3302 24.6783 24.0725 24.9189C23.8147 25.1596 23.6709 25.4791 23.6709 25.8114C23.6709 26.1436 23.8147 26.4632 24.0725 26.7038C24.3302 26.9444 24.6822 27.0877 25.0556 27.104C25.2232 27.1103 25.3901 27.0809 25.5423 27.0182C25.6945 26.9555 25.8276 26.8613 25.9304 26.7434V27.0422H26.5329V25.8113ZM24.2908 25.8113C24.3002 25.666 24.3571 25.5264 24.4547 25.4096C24.5523 25.2927 24.6862 25.2039 24.8399 25.1539C24.9936 25.1039 25.1604 25.095 25.3198 25.1283C25.4791 25.1615 25.624 25.2355 25.7366 25.3411C25.8492 25.4467 25.9246 25.5793 25.9534 25.7226C25.9823 25.8658 25.9633 26.0134 25.8989 26.1471C25.8345 26.2809 25.7275 26.395 25.591 26.4753C25.4546 26.5557 25.2946 26.5987 25.1309 26.5992C25.0162 26.6006 24.9024 26.5809 24.7967 26.5412C24.691 26.5016 24.5956 26.4429 24.5167 26.3688C24.4378 26.2948 24.377 26.2071 24.3381 26.1111C24.2992 26.0151 24.2831 25.9131 24.2908 25.8113ZM17.0199 24.5187C16.6442 24.5363 16.2907 24.6826 16.0336 24.9268C15.7766 25.171 15.6359 25.4943 15.6412 25.8287C15.6464 26.1631 15.7972 26.4828 16.0618 26.7205C16.3264 26.9583 16.6843 27.0957 17.0605 27.104C17.4738 27.1169 17.8781 26.994 18.196 26.7589L17.9005 26.3624C17.6717 26.5254 17.3884 26.616 17.0952 26.6199C16.8961 26.6347 16.6983 26.5802 16.5437 26.4677C16.3891 26.3552 16.2898 26.1937 16.2668 26.0173H18.3235C18.3293 25.9504 18.3351 25.8834 18.3351 25.8113C18.3293 25.044 17.7963 24.5187 17.0199 24.5187ZM17.0083 24.9977C17.1903 24.9945 17.3663 25.0556 17.4977 25.1676C17.6291 25.2796 17.7052 25.4333 17.7093 25.5951H16.2725C16.2831 25.4298 16.366 25.2748 16.5035 25.1631C16.641 25.0515 16.8222 24.9921 17.0083 24.9977ZM32.1237 25.8113V23.5918H31.5212V24.8792C31.4184 24.7614 31.2853 24.6672 31.1331 24.6045C30.9809 24.5418 30.814 24.5124 30.6464 24.5187C30.273 24.535 29.921 24.6783 29.6632 24.9189C29.4055 25.1595 29.2617 25.4791 29.2617 25.8114C29.2617 26.1436 29.4055 26.4632 29.6632 26.7038C29.921 26.9444 30.273 27.0877 30.6464 27.104C30.814 27.1103 30.9809 27.0809 31.1331 27.0182C31.2853 26.9555 31.4184 26.8613 31.5212 26.7434V27.0422H32.1237V25.8113ZM33.1281 26.6854C33.1656 26.6852 33.2026 26.6917 33.2371 26.7046C33.2703 26.7169 33.3005 26.7347 33.3262 26.7571C33.3517 26.7794 33.372 26.8059 33.3862 26.835C33.4007 26.865 33.4082 26.8973 33.4082 26.93C33.4082 26.9626 33.4007 26.9949 33.3862 27.0249C33.3719 27.0539 33.3516 27.0803 33.3262 27.1025C33.3005 27.1249 33.2703 27.1427 33.2371 27.1552C33.2027 27.1684 33.1656 27.175 33.1281 27.1748C33.073 27.1753 33.019 27.1613 32.9727 27.1347C32.9265 27.108 32.8901 27.0698 32.8681 27.0249C32.8536 26.9949 32.8462 26.9626 32.8462 26.93C32.8462 26.8973 32.8536 26.865 32.8681 26.835C32.8822 26.8059 32.9025 26.7794 32.9278 26.7571C32.9535 26.7346 32.9838 26.7167 33.0172 26.7046C33.0524 26.6917 33.0901 26.6852 33.1281 26.6854ZM33.1281 27.1208C33.1567 27.121 33.185 27.1159 33.2112 27.1058C33.2363 27.0961 33.2592 27.0823 33.2786 27.065C33.3186 27.0291 33.341 26.9806 33.341 26.9301C33.341 26.8795 33.3186 26.831 33.2786 26.7951C33.2592 26.7779 33.2364 26.7642 33.2112 26.7547C33.1849 26.7448 33.1567 26.7398 33.1281 26.74C33.0991 26.7398 33.0703 26.7448 33.0435 26.7547C33.0179 26.7641 32.9947 26.7778 32.975 26.7951C32.935 26.831 32.9125 26.8795 32.9125 26.9301C32.9125 26.9806 32.935 27.0291 32.975 27.065C32.9947 27.0824 33.0179 27.0962 33.0435 27.1058C33.0703 27.1159 33.0991 27.121 33.1281 27.1208ZM33.1443 26.8155C33.1714 26.814 33.1983 26.8209 33.2203 26.835C33.229 26.8415 33.236 26.8496 33.2406 26.8588C33.2452 26.868 33.2474 26.878 33.2469 26.8881C33.2472 26.8967 33.2455 26.9053 33.2418 26.9133C33.2382 26.9213 33.2327 26.9286 33.2257 26.9345C33.2089 26.9478 33.1876 26.9557 33.1653 26.9571L33.2489 27.0428H33.1835L33.1059 26.9577H33.0809V27.0428H33.0263V26.8155H33.1443ZM33.0809 26.8581V26.9187H33.1437C33.1558 26.9192 33.1677 26.9166 33.1781 26.9112C33.1823 26.9087 33.1857 26.9052 33.188 26.9012C33.1902 26.8971 33.1912 26.8926 33.1909 26.8881C33.1912 26.8837 33.1901 26.8793 33.1879 26.8753C33.1857 26.8714 33.1823 26.868 33.1781 26.8656C33.1677 26.8602 33.1557 26.8576 33.1437 26.8581H33.0809ZM29.8816 25.8113C29.891 25.666 29.948 25.5264 30.0456 25.4096C30.1431 25.2928 30.277 25.2039 30.4307 25.1539C30.5845 25.1039 30.7513 25.0951 30.9106 25.1283C31.0699 25.1616 31.2148 25.2356 31.3274 25.3412C31.44 25.4468 31.5154 25.5794 31.5442 25.7226C31.573 25.8658 31.5541 26.0134 31.4897 26.1472C31.4253 26.2809 31.3182 26.395 31.1818 26.4753C31.0453 26.5557 30.8854 26.5987 30.7217 26.5992C30.607 26.6006 30.4932 26.5809 30.3875 26.5412C30.2818 26.5015 30.1864 26.4428 30.1075 26.3688C30.0286 26.2948 29.9678 26.207 29.9289 26.1111C29.89 26.0151 29.8739 25.9131 29.8816 25.8113ZM9.53463 25.8113V24.5806H8.93207V24.8792C8.82922 24.7614 8.69614 24.6672 8.54394 24.6045C8.39174 24.5418 8.22485 24.5124 8.05724 24.5187C7.68389 24.535 7.33189 24.6783 7.07413 24.9189C6.81637 25.1596 6.6726 25.4791 6.6726 25.8114C6.6726 26.1436 6.81637 26.4632 7.07413 26.7038C7.33189 26.9444 7.68389 27.0877 8.05724 27.104C8.22485 27.1103 8.39174 27.0809 8.54394 27.0182C8.69614 26.9555 8.82923 26.8613 8.93207 26.7434V27.0422H9.53463V25.8113ZM7.29253 25.8113C7.30189 25.666 7.35887 25.5264 7.45644 25.4096C7.55402 25.2928 7.68792 25.2039 7.84163 25.1539C7.99535 25.1039 8.16216 25.0951 8.32149 25.1283C8.48083 25.1616 8.62572 25.2356 8.73831 25.3412C8.85089 25.4468 8.92625 25.5794 8.95508 25.7226C8.9839 25.8659 8.96494 26.0134 8.90054 26.1472C8.83614 26.2809 8.7291 26.395 8.59264 26.4753C8.45617 26.5557 8.29624 26.5987 8.13257 26.5992C8.01784 26.6006 7.90404 26.5809 7.79833 26.5412C7.69263 26.5015 7.59732 26.4428 7.5184 26.3688C7.43947 26.2948 7.37866 26.207 7.33979 26.1111C7.30091 26.0151 7.28482 25.9131 7.29253 25.8113Z"
          fill="#231F20"
        />
        <path
          d="M21.4384 5.82227H12.3123V20.4H21.4384V5.82227Z"
          fill="#FF5F00"
        />
        <path
          d="M12.8916 13.1108C12.8902 11.7069 13.2481 10.3211 13.9383 9.05826C14.6284 7.79545 15.6327 6.68876 16.8752 5.82198C15.3366 4.74697 13.4888 4.07844 11.5429 3.89279C9.59705 3.70715 7.6317 4.01188 5.87148 4.77217C4.11125 5.53246 2.62718 6.71762 1.5889 8.19219C0.550611 9.66676 0 11.3712 0 13.1108C0 14.8504 0.550611 16.5549 1.5889 18.0295C2.62718 19.504 4.11125 20.6892 5.87148 21.4495C7.6317 22.2098 9.59705 22.5145 11.5429 22.3289C13.4888 22.1432 15.3366 21.4747 16.8752 20.3997C15.6328 19.5329 14.6284 18.4262 13.9383 17.1634C13.2481 15.9006 12.8902 14.5148 12.8916 13.1108Z"
          fill="#EB001B"
        />
        <path
          d="M33.75 13.1108C33.7501 14.8504 33.1996 16.5549 32.1614 18.0294C31.1231 19.504 29.6391 20.6892 27.8789 21.4495C26.1187 22.2098 24.1534 22.5145 22.2076 22.3289C20.2617 22.1432 18.4139 21.4747 16.8754 20.3997C18.1167 19.532 19.1203 18.4251 19.8103 17.1625C20.5004 15.8999 20.8589 14.5145 20.8589 13.1108C20.8589 11.7071 20.5004 10.3217 19.8103 9.05912C19.1203 7.79651 18.1167 6.68964 16.8754 5.82198C18.4139 4.74697 20.2617 4.07843 22.2076 3.89279C24.1534 3.70715 26.1187 4.01189 27.8789 4.77219C29.6391 5.53248 31.1231 6.71764 32.1614 8.19222C33.1996 9.66679 33.7501 11.3713 33.75 13.1108Z"
          fill="#F79E1B"
        />
        <path
          d="M32.7549 18.8559V18.5575H32.8903V18.4967H32.5455V18.5575H32.681V18.8559H32.7549ZM33.4243 18.8559V18.4961H33.3186L33.1971 18.7436L33.0755 18.4961H32.9697V18.8559H33.0444V18.5845L33.1584 18.8185H33.2358L33.3498 18.5839V18.8559H33.4243Z"
          fill="#F79E1B"
        />
      </svg>
      <svg
        width="37"
        height="31"
        viewBox="0 0 37 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.3048 10.8078L15.9361 20.1781H13.0711L15.44 10.8078H18.3048ZM30.3578 16.8583L31.8657 13.3396L32.7335 16.8583H30.3578ZM33.5554 20.1781H36.2045L33.8902 10.8078H31.4465C30.8958 10.8078 30.4316 11.078 30.2261 11.4947L25.9274 20.1781H28.9361L29.5334 18.7787H33.2085L33.5554 20.1781ZM26.0766 17.119C26.089 14.6461 22.0364 14.5091 22.0635 13.4041C22.0722 13.0684 22.4508 12.7107 23.2782 12.6193C23.6885 12.5746 24.8205 12.5383 26.1037 13.0387L26.6056 11.0508C25.9164 10.8399 25.0295 10.6367 23.9258 10.6367C21.0936 10.6367 19.1013 11.9097 19.0854 13.7339C19.0672 15.083 20.5086 15.8351 21.5923 16.2845C22.7096 16.7437 23.084 17.0381 23.0785 17.4485C23.0708 18.0771 22.1876 18.3555 21.3651 18.3661C19.924 18.385 19.0886 18.0365 18.4228 17.7739L17.9028 19.8281C18.5733 20.0877 19.8087 20.3136 21.0879 20.3252C24.0989 20.3252 26.0676 19.0672 26.0766 17.119ZM14.2111 10.8078L9.56911 20.1781H6.54116L4.25667 12.7C4.11819 12.2401 3.99739 12.0711 3.57621 11.8768C2.88728 11.5602 1.75002 11.2641 0.75 11.0799L0.817748 10.8078H5.69257C6.3135 10.8078 6.87217 11.1575 7.01439 11.7628L8.22114 17.1854L11.2013 10.8078H14.2111Z"
          fill="#1434CB"
        />
      </svg>
      <svg
        width="41"
        height="31"
        viewBox="0 0 41 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M3.04074 11.4157L3.81025 12.7753H2.27563L3.04074 11.4157Z"
          fill="#006FCF"
        />
        <path
          d="M20.7624 12.2339C20.6079 12.3019 20.4252 12.3042 20.2063 12.3042H18.8405V11.5466H20.2249C20.4208 11.5466 20.6252 11.553 20.758 11.6082C20.9039 11.6578 20.9941 11.7636 20.9941 11.9097C20.9941 12.0587 20.9083 12.1787 20.7624 12.2339Z"
          fill="#006FCF"
        />
        <path
          d="M29.7278 11.4157L30.5059 12.7753H28.9541L29.7278 11.4157Z"
          fill="#006FCF"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.20459 14.9168H2.12567L2.5588 14.1593H3.52849L3.96049 14.9168H7.74008V14.3377L8.07744 14.9193H10.0395L10.3769 14.329V14.9168H19.7699L19.7655 13.6733H19.9472C20.0745 13.6765 20.1117 13.6851 20.1117 13.8374V14.9168H24.9698V14.6273C25.3616 14.7796 25.9711 14.9168 26.7731 14.9168H28.8169L29.2543 14.1593H30.224L30.6517 14.9168H34.5902V14.1972L35.1866 14.9168H38.3427V10.1602H35.2192V10.7219L34.7818 10.1602H31.5768V10.7219L31.1752 10.1602H26.846C26.1213 10.1602 25.4843 10.2335 24.9698 10.4379V10.1602H21.9822V10.4379C21.6548 10.2272 21.2086 10.1602 20.7125 10.1602H9.79792L9.06558 11.3887L8.31352 10.1602H4.8757V10.7219L4.49804 10.1602H1.56613L0.20459 12.4217V14.9168ZM16.9372 14.2469H13.1814V10.8364H16.9372V11.5466H14.3057V12.1614H16.874V12.8605H14.3057V13.5415H16.9372V14.2469ZM11.1909 14.2469H12.3434V10.8364H10.4715L9.11991 13.15L7.64856 10.8364H5.8136V14.0655L3.90146 10.8364H2.23107L0.217779 14.2469H1.43886L1.87526 13.4862H4.21613L4.64813 14.2469H6.93481V11.5735L8.56931 14.2469H9.55645L11.1867 11.5758L11.1909 14.2469ZM22.2326 11.7549C22.2326 12.2987 21.7321 12.5796 21.4404 12.664C21.6864 12.7319 21.8965 12.8518 21.9966 12.9512C22.1553 13.1208 22.1827 13.2723 22.1827 13.5769V14.2469H21.0487L21.0445 13.8168C21.0445 13.796 21.0447 13.7743 21.045 13.7519C21.0476 13.5531 21.0508 13.2998 20.867 13.1524C20.7027 13.0324 20.4523 13.0064 20.0475 13.0064H18.8406V14.2469H17.7164V10.8364H20.3023C20.8769 10.8364 21.3002 10.8474 21.6637 10.9998C22.0194 11.1521 22.2326 11.3745 22.2326 11.7549ZM24.0318 14.2469H22.8847V10.8364H24.0318V14.2469ZM37.3405 14.2469H35.7473L33.6162 11.6871V14.2469H31.3266L30.889 13.4862H28.5536L28.1291 14.2469H26.8135C26.267 14.2469 25.5751 14.1593 25.1833 13.8697C24.7882 13.5801 24.5826 13.1879 24.5826 12.5677C24.5826 12.0619 24.7055 11.5995 25.1887 11.2341C25.5521 10.9619 26.1213 10.8364 26.8961 10.8364H27.9845V11.5672H26.9189C26.5086 11.5672 26.277 11.6114 26.0538 11.7692C25.8621 11.9128 25.7306 12.1843 25.7306 12.5418C25.7306 12.9072 25.8308 13.1706 26.0398 13.3427C26.2128 13.4777 26.5274 13.5186 26.8233 13.5186H27.3282L28.9128 10.8365H30.5974L32.5008 14.0624V10.8365H34.2127L36.1889 13.2117V10.8365H37.3405V14.2469Z"
          fill="#006FCF"
        />
        <path
          d="M25.0198 18.1783C25.2386 18.1783 25.4246 18.1727 25.575 18.108C25.7207 18.0441 25.8078 17.9266 25.8078 17.7806C25.8078 17.6346 25.7207 17.529 25.575 17.4737C25.4389 17.4152 25.2386 17.4121 25.0384 17.4121H23.654V18.1783H25.0198Z"
          fill="#006FCF"
        />
        <path
          d="M15.79 18.4181L17.5161 19.8076V17.0726L15.79 18.4181Z"
          fill="#006FCF"
        />
        <path
          d="M10.2942 19.4069H13.0584L14.3426 18.4061L13.1129 17.412H10.2942V18.0322H12.7624V18.7282H10.2942V19.4069Z"
          fill="#006FCF"
        />
        <path
          d="M18.6359 18.2808H20.0888C20.5208 18.2808 20.7895 18.1254 20.7895 17.8304C20.7895 17.532 20.5078 17.4121 20.1019 17.4121H18.6359V18.2808Z"
          fill="#006FCF"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M40.2046 19.6815C39.9315 19.9711 39.3993 20.1178 38.6789 20.1178H36.5077V19.3863H38.6701C38.8846 19.3863 39.0347 19.3659 39.1251 19.3019C39.2034 19.2491 39.258 19.1725 39.258 19.0794C39.258 18.98 39.2034 18.9011 39.1207 18.8538C39.0391 18.8017 38.9204 18.7781 38.7246 18.7781C38.6555 18.7764 38.5854 18.775 38.5145 18.7736C37.5023 18.7537 36.3519 18.7311 36.3519 17.7222C36.3519 17.2274 36.7852 16.7066 37.965 16.7066H40.2044V16.0279H38.1238C37.4958 16.0279 37.0397 16.1369 36.7167 16.3065V16.0279H33.6391C33.1469 16.0279 32.5692 16.1164 32.296 16.3065V16.0279H26.8002V16.3065C26.3628 16.0777 25.6248 16.0279 25.2841 16.0279H21.659V16.3065C21.313 16.0635 20.5435 16.0279 20.0745 16.0279H16.0174L15.089 16.7564L14.2195 16.0279H8.15902V20.7878H14.1054L15.062 20.0478L15.9632 20.7878L19.6286 20.7902V19.6705H19.9889C20.4753 19.6759 21.0489 19.6617 21.5549 19.5031V20.7877H24.5782V19.5471H24.724C24.9102 19.5471 24.9285 19.5527 24.9285 19.6875V20.7876H34.1126C34.6957 20.7876 35.3052 20.6794 35.6427 20.483V20.7876H38.5559C39.1621 20.7876 39.7541 20.726 40.2046 20.5682V19.6815ZM35.7199 18.3187C35.9388 18.4829 36.0561 18.6903 36.0561 19.0414C36.0561 19.7753 35.4238 20.1178 34.29 20.1178H32.1002V19.3863H34.2812C34.4944 19.3863 34.6457 19.3659 34.7404 19.3019C34.8177 19.2491 34.8732 19.1725 34.8732 19.0794C34.8732 18.98 34.8132 18.9011 34.736 18.8538C34.6501 18.8017 34.5314 18.7781 34.3357 18.7781C34.2668 18.7764 34.1968 18.775 34.1262 18.7736C33.1175 18.7537 31.9676 18.731 31.9676 17.7222C31.9676 17.2274 32.3963 16.7066 33.575 16.7066H35.8289V17.4327H33.7665C33.562 17.4327 33.4291 17.4382 33.316 17.4943C33.1929 17.5495 33.1472 17.6315 33.1472 17.7397C33.1472 17.8684 33.2518 17.9559 33.3932 17.9938C33.5118 18.0237 33.6392 18.0325 33.8307 18.0325L34.436 18.0443C35.0463 18.0551 35.4652 18.1316 35.7199 18.3187ZM26.254 18.5325C26.5042 18.5995 26.7088 18.7195 26.8047 18.8189C26.9635 18.9855 26.9865 19.1409 26.991 19.4416V20.1178H25.8623V19.691C25.8623 19.6705 25.8625 19.6491 25.8628 19.6269C25.8653 19.4266 25.8686 19.1661 25.6805 19.0233C25.5162 18.9011 25.2658 18.8719 24.8557 18.8719H23.6542V20.1178H22.5245V16.7065H25.1201C25.6893 16.7065 26.1039 16.7247 26.4729 16.8675C26.8277 17.0229 27.0509 17.2359 27.0509 17.625C27.0507 18.1695 26.5499 18.4474 26.254 18.5325ZM27.6742 16.7065H31.4266V17.412H28.7939V18.0322H31.3623V18.7282H28.7939V19.4069L31.4266 19.41V20.1178H27.6742V16.7065ZM21.9966 17.8303C21.9966 18.778 21.0215 18.9736 20.0387 18.9736H18.6359V20.1178H16.4507L15.0663 18.9885L13.6276 20.1178H9.17424V16.7065H13.6961L15.0793 17.8247L16.5094 16.7065H20.1019C20.9941 16.7065 21.9966 16.8856 21.9966 17.8303Z"
          fill="#006FCF"
        />
        <path
          d="M38.1555 17.4324H40.2046V18.4031C40.2027 18.4014 40.2009 18.3997 40.1991 18.398L40.1933 18.3924L40.1902 18.3896L40.1901 18.3895C40.1643 18.3649 40.1387 18.3406 40.1003 18.3187C39.85 18.1317 39.4396 18.0551 38.8247 18.0441L38.2154 18.0323C38.0281 18.0323 37.9007 18.0235 37.7821 17.9936C37.6364 17.9557 37.5363 17.8682 37.5363 17.7395C37.5363 17.6313 37.582 17.5493 37.7006 17.4941C37.815 17.438 37.951 17.4324 38.1555 17.4324Z"
          fill="#006FCF"
        />
      </svg>
      <svg
        width="30"
        height="31"
        viewBox="0 0 30 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.5975 16.6404C2.30919 16.8942 1.94218 17.0052 1.35528 17.0052H1.11174V13.9769H1.35528C1.94218 13.9769 2.29511 14.0804 2.5975 14.3468C2.90964 14.6221 3.09721 15.0467 3.09721 15.486C3.09721 15.9273 2.90964 16.3671 2.5975 16.6404ZM1.53708 13.2012H0.20459V17.7812H1.52895C2.23374 17.7812 2.74247 17.616 3.18838 17.2515C3.71805 16.8197 4.03235 16.1677 4.03235 15.4923C4.03235 14.1403 3.00695 13.2012 1.53708 13.2012Z"
          fill="#201D1C"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.45068 13.2012H5.35423V17.7812H4.45068V13.2012Z"
          fill="#201D1C"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.57305 14.9567C7.02767 14.7596 6.86826 14.6284 6.86826 14.3808C6.86826 14.0931 7.15313 13.8749 7.54488 13.8749C7.8164 13.8749 8.04007 13.982 8.27837 14.2436L8.7501 13.634C8.36033 13.2961 7.89385 13.125 7.38529 13.125C6.56244 13.125 5.93528 13.6889 5.93528 14.4345C5.93528 15.0676 6.22684 15.3888 7.07767 15.6938C7.43332 15.8156 7.61403 15.8988 7.70555 15.9552C7.88681 16.0706 7.97707 16.236 7.97707 16.4271C7.97707 16.7997 7.67739 17.0736 7.27228 17.0736C6.841 17.0736 6.49204 16.8609 6.28316 16.4615L5.69861 17.0191C6.11653 17.6223 6.6175 17.8919 7.30767 17.8919C8.24895 17.8919 8.91221 17.2711 8.91221 16.387C8.91221 15.6584 8.60459 15.3281 7.57305 14.9567Z"
          fill="#201D1C"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.19714 15.4923C9.19714 16.8392 10.2704 17.8829 11.652 17.8829C12.0416 17.8829 12.3755 17.8072 12.7868 17.616V16.5634C12.4234 16.9232 12.1044 17.0651 11.6917 17.0651C10.7788 17.0651 10.1321 16.4144 10.1321 15.4859C10.1321 14.6087 10.7997 13.9137 11.652 13.9137C12.082 13.9137 12.4106 14.0638 12.7868 14.4289V13.3776C12.3894 13.1796 12.0634 13.0977 11.6706 13.0977C10.2969 13.0977 9.19714 14.1618 9.19714 15.4923Z"
          fill="#201D1C"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M20.1329 16.276L18.893 13.2012H17.9044L19.8754 17.8968H20.3616L22.3709 13.2012H21.3901L20.1329 16.276Z"
          fill="#201D1C"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M22.7823 17.7812H25.3536V17.0052H23.6895V15.7689H25.2904V14.993H23.6895V13.9769H25.3536V13.2012H22.7823V17.7812Z"
          fill="#201D1C"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M27.1246 15.3073H26.8614V13.922H27.1394C27.7059 13.922 28.0094 14.1562 28.0094 14.6023C28.0094 15.0608 27.7059 15.3073 27.1246 15.3073ZM28.9431 14.5525C28.9431 13.6948 28.3459 13.2012 27.299 13.2012H25.9523V17.7812H26.8614V15.9398H26.9793L28.232 17.7812H29.3475L27.8847 15.8509C28.5683 15.7132 28.9431 15.2533 28.9431 14.5525Z"
          fill="#201D1C"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.0322 15.503C18.0322 16.8216 16.9476 17.8904 15.6084 17.8904C14.2696 17.8904 13.1848 16.8216 13.1848 15.503C13.1848 14.1842 14.2696 13.1152 15.6084 13.1152C16.9476 13.1152 18.0322 14.1842 18.0322 15.503Z"
          fill="url(#paint0_linear_15099_10860)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.0322 15.503C18.0322 16.8216 16.9476 17.8904 15.6084 17.8904C14.2696 17.8904 13.1848 16.8216 13.1848 15.503C13.1848 14.1842 14.2696 13.1152 15.6084 13.1152C16.9476 13.1152 18.0322 14.1842 18.0322 15.503Z"
          fill="url(#paint1_radial_15099_10860)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_15099_10860"
            x1="14.1214"
            y1="13.5664"
            x2="17.2425"
            y2="17.2275"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E6772F" />
            <stop offset="1" stopColor="#EA9D2C" />
          </linearGradient>
          <radialGradient
            id="paint1_radial_15099_10860"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(16.1421 16.4961) rotate(-127.296) scale(3.24083 3.36198)"
          >
            <stop stopColor="#EA9D2C" stopOpacity="0" />
            <stop offset="0.328125" stopColor="#DF7624" stopOpacity="0" />
            <stop offset="0.760188" stopColor="#BF4B23" stopOpacity="0.75" />
            <stop offset="1" stopColor="#7D3017" />
          </radialGradient>
        </defs>
      </svg>
    </div>
  )
}

export const AppleLogo = ({ color = "black" }) => {
  return (
    <>
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity="0.9" clipPath="url(#clip0_15099_10899)">
          <path
            d="M21.792 18.7033C21.429 19.5418 20.9994 20.3136 20.5016 21.0232C19.8231 21.9906 19.2676 22.6602 18.8395 23.0321C18.1758 23.6424 17.4647 23.955 16.7032 23.9728C16.1566 23.9728 15.4973 23.8172 14.73 23.5017C13.9601 23.1876 13.2525 23.0321 12.6056 23.0321C11.9271 23.0321 11.1994 23.1876 10.4211 23.5017C9.64153 23.8172 9.01355 23.9817 8.53342 23.9979C7.80322 24.0291 7.07539 23.7076 6.3489 23.0321C5.88521 22.6276 5.30523 21.9343 4.61043 20.9521C3.86498 19.9033 3.25211 18.687 2.77198 17.3004C2.25777 15.8026 2 14.3523 2 12.9482C2 11.3398 2.34754 9.95259 3.04367 8.79011C3.59076 7.85636 4.31859 7.11979 5.22953 6.57906C6.14046 6.03834 7.12473 5.76279 8.18469 5.74516C8.76467 5.74516 9.52524 5.92457 10.4704 6.27715C11.4129 6.63091 12.0181 6.81032 12.2834 6.81032C12.4817 6.81032 13.154 6.60054 14.2937 6.18234C15.3714 5.7945 16.281 5.63391 17.0262 5.69717C19.0454 5.86012 20.5624 6.6561 21.5712 8.09013C19.7654 9.18432 18.8721 10.7169 18.8898 12.6829C18.9061 14.2142 19.4617 15.4886 20.5535 16.5004C21.0483 16.97 21.6009 17.333 22.2156 17.5907C22.0823 17.9774 21.9416 18.3477 21.792 18.7033ZM17.161 0.480137C17.161 1.68041 16.7225 2.8011 15.8484 3.83841C14.7937 5.07155 13.5179 5.78413 12.1343 5.67168C12.1167 5.52769 12.1065 5.37614 12.1065 5.21688C12.1065 4.06462 12.6081 2.83147 13.4989 1.82321C13.9436 1.3127 14.5092 0.888228 15.1951 0.549615C15.8796 0.216055 16.5269 0.031589 17.1358 0C17.1536 0.160458 17.161 0.320926 17.161 0.480121V0.480137Z"
            fill={color}
          />
        </g>
        <defs>
          <clipPath id="clip0_15099_10899">
            <rect width="24" height="24" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </>
  )
}

interface BulletProps {
  size?: number // Size of the circle (default: 8px)
  color?: string // Color of the circle (default: gray)
}

export const Bullet: React.FC<BulletProps> = ({ size = 8, color = "gray" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill={color}
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="12" cy="12" r="12" />
    </svg>
  )
}
interface CloseIconProps {
  color?: string
}

export const CloseIcon: React.FC<CloseIconProps> = ({ color = "black" }) => {
  return (
    <>
      <svg
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.00002 5.4445L12.4445 0L14.0001 1.55556L8.55557 7.00004L14.0001 12.4445L12.4445 14L7.00002 8.55559L1.55557 14L0 12.4445L5.44448 7.00004L0 1.55556L1.55557 0L7.00002 5.4445Z"
          fill={color}
        />
      </svg>
    </>
  )
}
export const LoaderIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="animate-spin text-white"
    >
      <circle
        className="opacity-30"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <circle
        className="opacity-100"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
        strokeDasharray="60"
        strokeDashoffset="30"
      />
    </svg>
  )
}

interface CheckIconProps {
  color: string
}
export const CheckIcon: React.FC<CheckIconProps> = ({ color = "#a78bfa" }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 13L9 17L19 7"
        stroke={color}
        strokeWidth="5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const PurpleCheckedIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="20" cy="20" r="20" fill={colorTheme.MEDIUM_PURPLE} />
      <path
        d="M28 14L17.25 24.75L12 19.5"
        stroke="white"
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ArrowRightIcon = () => {
  return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
      >
        <path
          d="M7.4248 16.6L12.8581 11.1667C13.4998 10.525 13.4998 9.47499 12.8581 8.83333L7.4248 3.39999"
          stroke="#292D32"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
  )
}
