import React from "react"

interface RoundedContainerProps {
  bgColor?: string
  borderRadius?: string
  bgImage?: string // new prop for background image
  children: React.ReactNode
  className?: string
}

const RoundedContainer: React.FC<RoundedContainerProps> = ({
  // Default values
  bgColor = "white",
  borderRadius = "rounded-3xl",
  bgImage,
  children = <span>Enter you content</span>,
  className = "",
}) => {
  return (
    <div
      className={`${borderRadius} m-2 sm:m-5 sm:mx-4 p-2 bg-cover bg-center ${className}`}
      style={
        bgImage
          ? { backgroundImage: `url(${bgImage})` }
          : { backgroundColor: bgColor }
      }
    >
      {children}
    </div>
  )
}

export default RoundedContainer
