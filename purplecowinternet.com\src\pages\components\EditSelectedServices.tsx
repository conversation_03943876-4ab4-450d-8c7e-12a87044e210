import React, { useEffect, useState } from "react"
import {
  CloseIcon,
  EditIcon,
  HomePhoneIconCheckout,
  InternetIconCheckout,
  PlusIcon,
  TvIconCheckout,
} from "../../icons/Icons"
import { formatMoney, priceFormat } from "../../utils"
import RoundedContainer from "../../components/RoundedContainer"
import { useSelector } from "react-redux"
import SelectInternetPlan from "./SelectInternetPlan"
import SelectTvPlan from "./SelectTvPlan"
import { colorTheme } from "../../data/SrtingConstants"
import SelectHomePhonePlan from "./SelectHomePhonePlan"
import CommonButtonInternet from "../../components/CommonButton"
import { FibreHeading } from "../../components/FibreHeading"
import { validationErrorMessages } from "../../data/Regex"

interface EditServiceProps {
  addonsData: any
  editServices: any
  setEditServices: any
  plansData: any
  editServiceAddress: any
  editContactInformation: any
  postCartData: any
}
const EditSelectedServices: React.FC<EditServiceProps> = ({
  addonsData,
  editServices,
  setEditServices,
  plansData,
  editServiceAddress,
  editContactInformation,
  postCartData,
}: any) => {
  const formData = useSelector((state: any) => state.form)
  const filteredPlans = plansData?.homePhone?.flatMap((plan: any) =>
    plan.billing_period?.flatMap((period: any) =>
      Object.values(period)?.filter((option: any) =>
        formData?.addons?.includes(option?.api_name)
      )
    )
  )

  // TV Plan

  const TvPlansList = plansData ? plansData?.tv : []
  const numberOfSingleChannels = formData?.tv_optional_single_plans?.length
  const numberOfBundles = Math.floor(numberOfSingleChannels / 5)
  const remainingItems = numberOfSingleChannels % 5
  const [phonePortError, setPhonePortError]: any = useState("")

  let seletedTvPlan =
    formData?.tv_plan != null
      ? TvPlansList?.filter((obj: any) => obj?.id === formData?.tv_plan)[0]
      : null
  let optionalPlans = formData?.OptionalPagePlan

  let singleTvPlansTotalPrice =
    numberOfBundles *
      addonsData?.tv?.filter((obj: any) => obj?.api_name === "X_PICK_5")[0]
        .billing_period[0]?.monthly?.price +
    remainingItems *
      addonsData?.tv?.filter(
        (obj: any) => obj?.api_name === "X_SNGL_CHANNEL"
      )[0].billing_period[0]?.monthly?.price

  const totalTvOptionalPlansPrice = optionalPlans?.filter((obj: any) =>
    formData?.tv_optional_plans.includes(obj.id)
  )

  const loadTotalPrice = () => {
    let totals = 0
    if (formData?.tv_plan != null) {
      totals += Number(
        priceFormat(seletedTvPlan?.billing_period[0]?.monthly?.price)
      )

      formData?.tv_optional_plans.map((item: any) => {
        totals += Number(
          priceFormat(
            [
              ...seletedTvPlan?.optional_extra_packages,
              ...seletedTvPlan?.optional_iptv_products,
            ]?.filter((obj) => obj.id == item)[0].billing_period[0]?.monthly
              ?.price
          )
        )
      })

      const numberOfSingleChannels = formData?.tv_optional_single_plans?.length

      // Calculate the number of bundles and remaining items
      const numberOfBundles = Math.floor(numberOfSingleChannels / 5)
      const remainingItems = numberOfSingleChannels % 5

      // Calculate the total cost
      totals +=
        numberOfBundles *
          addonsData?.tv?.filter((obj: any) => obj?.api_name === "X_PICK_5")[0]
            .billing_period[0]?.monthly?.price +
        remainingItems *
          addonsData?.tv?.filter(
            (obj: any) => obj?.api_name === "X_SNGL_CHANNEL"
          )[0].billing_period[0]?.monthly?.price
    }
  }

  // calculate total tv billing
  useEffect(() => {
    loadTotalPrice()
  }, [formData])

  return (
    <div>
      <RoundedContainer className="!mx-0 p-3">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Selected products</h3>
          {!editServiceAddress &&
            !editContactInformation &&
            !editServices?.editServicePopup && (
              <div
                onClick={() =>
                  setEditServices({
                    editServicePopup: true,
                    internet: false,
                    tv: false,
                    homePhone: false,
                  })
                }
                className="cursor-pointer"
              >
                <EditIcon />
              </div>
            )}
        </div>
        <div className="flex mt-3">
          {formData?.plan_id && (
            <div className="flex items-center text-center flex-col">
              <InternetIconCheckout />
              <p>Internet</p>
              <b>
                {formatMoney(
                  plansData?.internet?.filter(
                    (internet: any) => internet.id == formData?.plan_id
                  )[0]?.billing[0]?.monthly?.price
                ) || 0}
              </b>
            </div>
          )}
          {formData?.tv_plan && (
            <div className="flex ml-3 text-center items-center flex-col">
              <TvIconCheckout />
              <p>HD TV</p>

              <b>
                {formatMoney(
                  plansData?.tv?.filter(
                    (tvplan: any) => tvplan?.id === formData?.tv_plan
                  )[0]?.billing_period[0]?.monthly?.price +
                    totalTvOptionalPlansPrice?.reduce(
                      (acc: number, item: any) =>
                        acc + item.billing_period[0]?.monthly?.price,
                      0
                    ) +
                    singleTvPlansTotalPrice
                )}
              </b>
            </div>
          )}
          {formData?.addons?.length > 0 && (
            <div className="flex ml-3 text-center items-center flex-col">
              <HomePhoneIconCheckout />
              <p>Home Phone</p>
              <b>
                {formatMoney(
                  filteredPlans?.map(
                    ({ display_name, price }: any) =>
                      ({
                        display_name,
                        price,
                      })?.price
                  ) || 0
                )}
              </b>
            </div>
          )}
        </div>
      </RoundedContainer>

      {/* Drawer */}
      <div
        className={`fixed inset-0 flex items-end justify-center bg-black bg-opacity-70 transition-all duration-500 ${
          editServices?.editServicePopup
            ? "translate-y-0 opacity-100 z-20"
            : "translate-y-full opacity-0"
        }`}
      >
        <div
          style={{ backgroundColor: colorTheme.MEDIUM_PURPLE }}
          className="h-[80dvh] sm:h-[96dvh] pb-40 w-screen md:w-[80%] lg:w-[66%] overflow-auto p-4 sm:p-10 rounded-t-2xl shadow-lg"
        >
          <div className="flex justify-between">
            <FibreHeading
              align="text-left"
              color="white"
              content="Product Details"
            />
            <div
              className="cursor-pointer"
              onClick={() => {
                setEditServices({
                  editServicePopup: false,
                  internet: false,
                  tv: false,
                  homePhone: false,
                })
              }}
            >
              <CloseIcon color="white" />
            </div>
          </div>

          {/* Internet */}
          {formData?.plan_id && (
            <div className="mt-8">
              <div className="flex justify-between items-center my-2">
                <h3 className="text-white">Internet plan</h3>
                {!editServices?.internet &&
                  !editServices?.tv &&
                  !editServices?.homePhone && (
                    <div
                      onClick={() => {
                        setEditServices({
                          ...editServices,
                          internet: true,
                        })
                      }}
                      className="cursor-pointer"
                    >
                      <EditIcon />
                    </div>
                  )}
              </div>
              {editServices?.internet ? (
                <>
                  <SelectInternetPlan
                    onSelectInternetPlan={() => {}}
                    plans={plansData?.internet}
                  />
                  <div className="flex justify-center">
                    <CommonButtonInternet
                      className="mt-5"
                      onClick={() => {
                        setEditServices({
                          ...editServices,
                          internet: false,
                        })
                      }}
                      buttonText="Update Internet"
                    />
                  </div>
                </>
              ) : (
                <div>
                  <div className="flex justify-between p-2 rounded-lg bg-white">
                    <p>
                      {plansData?.internet?.filter(
                        (internet: any) => internet.id == formData?.plan_id
                      )[0]?.name || ""}
                    </p>
                    <p>
                      {formatMoney(
                        plansData?.internet?.filter(
                          (internet: any) => internet.id == formData?.plan_id
                        )[0]?.billing[0]?.monthly?.price
                      )}
                      /month
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* TV */}
          <div className="mt-8">
            <div className="flex justify-between items-center my-2">
              <h3 className="text-white">TV plan</h3>
              {!editServices?.internet &&
                !editServices?.tv &&
                !editServices?.homePhone && (
                  <div
                    onClick={() =>
                      setEditServices({
                        ...editServices,
                        tv: true,
                      })
                    }
                    className="cursor-pointer"
                  >
                    {formData?.tv_plan ? <EditIcon /> : <PlusIcon />}
                  </div>
                )}
            </div>

            {editServices?.tv ? (
              <>
                <SelectTvPlan addons={addonsData} tvPlans={plansData?.tv} />
                <div className="flex justify-center">
                  <CommonButtonInternet
                    className="mt-5"
                    onClick={() => {
                      setEditServices({
                        ...editServices,
                        tv: false,
                      })
                    }}
                    buttonText="Update TV"
                  />
                </div>
              </>
            ) : (
              <>
                {formData?.tv_plan && (
                  <div>
                    <div className="flex justify-between p-2 rounded-lg bg-white">
                      <p>
                        {
                          plansData?.tv?.filter(
                            (tv: any) => tv?.id == formData?.tv_plan
                          )[0]?.name
                        }
                      </p>
                      <p>
                        {formatMoney(
                          plansData?.tv?.filter(
                            (tv: any) => tv?.id == formData?.tv_plan
                          )[0]?.billing_period[0]?.monthly?.price || 0
                        )}
                        /month
                      </p>
                    </div>

                    {/* Tv Additional plan */}
                    {formData?.tv_optional_plans?.length > 0 && (
                      <div className=" p-2 rounded-lg my-2 bg-[#f7f7f7]">
                        {formData?.tv_optional_plans?.map(
                          (ele: number, index: number) => {
                            return (
                              <div key={index} className="flex justify-between">
                                <p>
                                  {
                                    optionalPlans?.filter(
                                      (plan: any) => plan?.id === ele
                                    )[0]?.name
                                  }
                                </p>
                                <p>
                                  {formatMoney(
                                    optionalPlans?.filter(
                                      (plan: any) => plan?.id === ele
                                    )[0].billing_period[0]?.monthly?.price
                                  ) || 0}
                                  /month
                                </p>
                              </div>
                            )
                          }
                        )}
                      </div>
                    )}

                    {/* Tv additional single plans */}
                    {formData?.tv_optional_single_plans?.length > 0 && (
                      <div className="flex justify-between p-2 rounded-lg my-2 bg-white">
                        <p>
                          {formData?.tv_optional_single_plans?.length} Single
                          channels
                        </p>
                        <p>{formatMoney(singleTvPlansTotalPrice)}/month</p>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>

          {/* Home Phone */}
          <div className="mt-8">
            <div className="flex justify-between items-center my-2">
              <h3 className="text-white">Home phone plan</h3>
              {!editServices?.internet &&
                !editServices?.tv &&
                !editServices?.homePhone && (
                  <div
                    onClick={() =>
                      setEditServices({
                        ...editServices,
                        homePhone: true,
                      })
                    }
                    className="cursor-pointer"
                  >
                    {formData?.addons?.length > 0 ? <EditIcon /> : <PlusIcon />}
                  </div>
                )}
            </div>
            {editServices?.homePhone ? (
              <>
                <SelectHomePhonePlan
                  addons={addonsData}
                  plans={plansData}
                  phonePortError={phonePortError}
                  setPhonePortError={setPhonePortError}
                />
                <div className="flex justify-center">
                  <CommonButtonInternet
                    className="mt-5"
                    onClick={() => {
                      const isExistingPhone = formData?.is_existing_phone
                      const portNumber = formData?.customer?.port_number || ""
                      const cleanedNumber = portNumber.replace(/\D/g, "") // Remove non-numeric characters

                      if (isExistingPhone && portNumber.trim() === "") {
                        setPhonePortError("Enter mobile number")
                        return
                      }

                      if (isExistingPhone) {
                        let phoneValid = cleanedNumber.match(
                          /^(\+1 ?)?\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
                        )

                        if (!phoneValid) {
                          phoneValid = cleanedNumber.match(
                            /^(\+?1 ?)?\(?([0-9]{1})\)?[-. ]?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
                          )
                        }

                        if (!phoneValid) {
                          setPhonePortError(
                            validationErrorMessages.ENTER_CELL_PHONE_NUMBER
                          )
                          return
                        }
                      }
                      setEditServices({
                        ...editServices,
                        homePhone: false,
                      })

                      postCartData()
                    }}
                    buttonText="Update Home Phone"
                  />
                </div>
              </>
            ) : (
              <>
                {formData?.addons?.length > 0 && (
                  <div className="flex justify-between p-2 rounded-lg bg-white">
                    <p>
                      {filteredPlans?.map(
                        ({ display_name, price }: any) =>
                          ({
                            display_name,
                            price,
                          })?.display_name
                      )}
                    </p>
                    {formatMoney(
                      filteredPlans?.map(
                        ({ display_name, price }: any) =>
                          ({
                            display_name,
                            price,
                          })?.price
                      ) || 0
                    )}
                    /month
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default EditSelectedServices
