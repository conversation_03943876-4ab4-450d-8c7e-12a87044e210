{"name": "gatsby-starter-default", "private": true, "description": "A simple starter to get up and developing quickly with Gatsby", "version": "0.1.0", "author": "<PERSON> <<EMAIL>>", "dependencies": {"@chargebee/chargebee-js-react-wrapper": "^0.6.6", "@reduxjs/toolkit": "^2.7.0", "@svg-icons/fa-regular": "^1.87.0", "autoprefixer": "^10.4.20", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "fs": "0.0.1-security", "gatsby": "^5.14.1", "gatsby-plugin-facebook-pixel": "^1.0.8", "gatsby-plugin-google-gtag": "^5.14.0", "gatsby-plugin-google-tagmanager": "^5.14.0", "gatsby-plugin-image": "^3.14.0", "gatsby-plugin-manifest": "^5.14.0", "gatsby-plugin-postcss": "^6.14.0", "gatsby-plugin-react-svg": "^3.3.0", "gatsby-plugin-sharp": "^5.14.0", "gatsby-source-filesystem": "^5.14.0", "gatsby-transformer-sharp": "^5.14.0", "lodash-es": "^4.17.21", "postcss": "^8.4.24", "react": "^18.3.1", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-redux": "^9.2.0", "react-textarea-autosize": "^8.5.6", "sharp": "^0.33.5", "tailwindcss": "^3.4.17", "universal-cookie": "^7.2.2"}, "devDependencies": {"@testing-library/cypress": "^10.0.2", "@testing-library/react": "^16.1.0", "@types/crypto-js": "^4.2.2", "@types/facebook-pixel": "^0.0.31", "@types/gtag.js": "^0.0.20", "@types/lodash": "^4.17.14", "@types/lodash-es": "^4.17.12", "@types/react-helmet": "^6.1.11", "@types/webpack-env": "^1.18.5", "chargebee-typescript": "^2.44.0", "cypress": "^13.17.0", "cypress-iframe": "^1.0.1", "prettier": "^3.4.2", "start-server-and-test": "^2.0.9", "typescript": "^5.7.2"}, "keywords": ["gatsby"], "license": "MIT", "scripts": {"build": "gatsby build", "develop": "GATSBY_EXPERIMENTAL_DEV_SSR=true gatsby develop", "format": "prettier --write \"**/*.{js,jsx,json,md}\"", "start": "npm run develop", "serve": "gatsby serve", "clean": "gatsby clean", "deploy": "aws s3 sync --exclude=*.md public/ s3://purplecowinternet.ca", "deploy-staging": "STAGING=true npm run build && aws --profile purple-cow-internet s3 sync public/ s3://staging-purplecow-dwgarrdrscnn7al2a9recdksyrrxmqvz", "deployProduction": "aws s3 sync --exclude=*.md public/ s3://purplecowinternet.com; sleep 5; aws cloudfront create-invalidation --distribution-id EVATRU5V233F7 --paths '/*'", "deploy-development": "aws s3 sync --exclude=*.m public/ s3://dev-website-purplecow; sleep 5; aws cloudfront create-invalidation --distribution-id E38OSFATP4PF58 --paths '/*'", "predeployProduction": "npm run build", "cy:open": "cypress open", "cy:run": "cypress run", "test:e2e": "start-server-and-test develop http://localhost:8000 cy:open", "test:e2e:run": "start-server-and-test develop http://localhost:8000 cy:run", "test:e2e:staging": "cypress run", "test:e2e:staging:open": "cypress open"}, "repository": {"type": "git", "url": "https://github.com/gatsbyjs/gatsby-starter-default"}, "bugs": {"url": "https://github.com/gatsbyjs/gatsby/issues"}, "browser": {"fs": false, "os": false, "path": false}}