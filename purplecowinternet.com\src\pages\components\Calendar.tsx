import React, { useEffect, useState } from "react"
import { greaterLessDate, isHoliday, isWeekEnd } from "../../utils"
import DatePicker from "react-datepicker"
import { addDays } from "date-fns"
import { toZonedTime } from "date-fns-tz"
import { useDispatch, useSelector } from "react-redux"
import { setFormData } from "../../redux/formSlice"
import { CheckIcon } from "../../icons/Icons"
import { colorTheme } from "../../data/SrtingConstants"

interface CalendarProps {
  isCheckoutPage: boolean
}
const Calendar: React.FC<CalendarProps> = ({ isCheckoutPage }) => {
  const formData = useSelector((state: any) => state.form)
  const [asapInstallDate, setAsapInstallDate] = useState(true)
  const dispatch = useDispatch()
  const numberOfBusinessDays = 5
  const atlanticZone = "America/Halifax"
  const isFibreAddress = formData.addressType === "PurpleCow"
  // Date Change Handle
  const handleInputDateChange = (selectedDate: any) => {
    let date = new Date(selectedDate)
    let dateweek = date.getWeeks()
    let ccateweek = date.getWeeks()
    if (greaterLessDate(new Date(), date) != 1) {
      if (dateweek[0].toDateString() == ccateweek[0].toDateString()) {
        dispatch(
          setFormData({
            customer: {
              ...formData?.customer,
              cf_suggested_turn_on_date: selectedDate?.toISOString(),
            },
          })
        )
      }
    }
  }
  // Disable dates logic
  const isDateBlocked = (date: Date) => {
    if (isFibreAddress) return false // Only disable weekends

    let tempDate = new Date()
    let blockedDays = 0
    // Block next 3 business days
    while (blockedDays < numberOfBusinessDays) {
      tempDate = addDays(tempDate, 1)
      if (!isHoliday(tempDate)) {
        blockedDays++
      }
    }
    return isHoliday(date) || date < tempDate // Block holidays, and first 3 business days
  }

  // Get the 4th business day as minDate
  const getMinDate = () => {
    let date = new Date() // Start from today
    if (isFibreAddress) {
      // Move to the next business day (skip weekends only)
      date = addDays(date, 1)
      while (date.getDay() === 0 || date.getDay() === 6) {
        date = addDays(date, 1)
      }
      return date
    }

    // Default: Move to the 4th business day (skip holidays)
    let businessDaysCount = 0
    while (businessDaysCount < numberOfBusinessDays) {
      date = addDays(date, 1) // Move to next day
      if (isWeekEnd(date) || isHoliday(date)) continue // don't count weekend and holiday in businessdays
      businessDaysCount++ // increate businessdays count
    }
    const atlanticDate = toZonedTime(new Date(), atlanticZone)

    // if current time is greater then 8PM (America/Halifax time) then increase date by 1
    if (atlanticDate.getHours() >= 20) date = addDays(date, 1)

    return date
  }

  // set install date
  useEffect(() => {
    if (
      formData?.customer.cf_suggested_turn_on_date === "" // for initial load
    ) {
      setTimeout(() => {
        dispatch(
          setFormData({
            customer: {
              ...formData.customer,
              cf_suggested_turn_on_date: getMinDate().toISOString(),
            },
          })
        )
      }, 1000)
    }
    // if selected date is greater than mindate then show date picker
    if (new Date(formData?.customer.cf_suggested_turn_on_date) > getMinDate()) {
      setAsapInstallDate(false)
    }
  }, [])

  if (isFibreAddress) return
  return (
    <div>
      <span className="flex gap-2">
        <div
          onClick={() => {
            setAsapInstallDate(!asapInstallDate)
            // if user checks true on asap install date
            if (!asapInstallDate) {
              dispatch(
                setFormData({
                  ...formData,
                  customer: {
                    ...formData.customer,
                    cf_suggested_turn_on_date: getMinDate().toISOString(),
                  },
                })
              )
            }
          }}
          style={{
            backgroundColor: isCheckoutPage
              ? colorTheme.MEDIUM_PURPLE
              : "#FFFFFF",
          }}
          className="w-7 h-7 relative rounded-full cursor-pointer flex items-center justify-center"
        >
          {asapInstallDate && (
            <CheckIcon
              color={isCheckoutPage ? "white" : colorTheme.MEDIUM_PURPLE}
            />
          )}
        </div>
        <h3
          style={{ color: isCheckoutPage ? "black" : "white" }}
          className={`font-medium font-sans w-[90%]`}
        >
          Install ASAP - within 4-7 business days (uncheck if you have a future
          date in mind)
        </h3>
      </span>

      <div
        className={`datepicker-inline ml-0 m-3 flex ${isCheckoutPage ? "justify-center" : "justify-center md:justify-start"} `}
      >
        {!asapInstallDate && (
          <>
            {formData?.customer?.cf_suggested_turn_on_date === "" ? (
              "Loading...."
            ) : (
              <DatePicker
                id="select_date_plan"
                onChange={(value) => handleInputDateChange(value)}
                selected={
                  !isDateBlocked(formData?.customer.cf_suggested_turn_on_date)
                    ? new Date(formData.customer.cf_suggested_turn_on_date)
                    : getMinDate()
                }
                filterDate={(date) => !isDateBlocked(date)} // Block specific dates
                minDate={getMinDate()}
                highlightDates={[new Date()]}
                inline
              />
            )}
          </>
        )}
      </div>
    </div>
  )
}
export default Calendar
