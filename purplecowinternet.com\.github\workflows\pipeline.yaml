name: Deploy Website

on:
  push:
    branches: ["master"]

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}

  GATSBY_LAMBDA: ${{ vars.GATSBY_LAMBDA }}
  GATSBY_CHARGEBEE_SITE: ${{ vars.GATSBY_CHARGEBEE_SITE }}
  GATSBY_ENVIRONMENT: ${{ vars.GATSBY_ENVIRONMENT }}
  GATSBY_PRODUCT_CATALOG_S3_BUCKET: ${{ vars.GATSBY_PRODUCT_CATALOG_S3_BUCKET }}
  
  GATSBY_CHARGEBEE_API_KEY: ${{ secrets.GATSBY_CHARGEBEE_API_KEY }}
  GATSBY_CART_KEY: ${{ secrets.GATSBY_CART_KEY }}
  GATSBY_CART_IV: ${{ secrets.GATSBY_CART_IV }}
  GATSBY_FACEBOOK_PIXEL_ID: ${{ secrets.GATSBY_FACEBOOK_PIXEL_ID }}
  GATSBY_GOOGLE_API_KEY: ${{ secrets.GATSBY_GOOGLE_API_KEY }}

jobs:
  build:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js 20.9.0
        uses: actions/setup-node@v3
        with:
          node-version: 20.9.0

      - name: Build
        run: |
          yarn install
          npm run build
          npm run deployProduction
