import React, { useEffect, useState } from "react"
import TextareaAutosize from "react-textarea-autosize"
import typing from "../../../../static/images/ezgif-3-0f02c2537d.gif"
import smiling from "../../../../static/images/smile-icon.png"
const MessageModal = ({ closepopup }: any) => {
  const [phoneNo, setPhoneNo] = useState("")
  const [phoneNoError, setPhoneNoError] = useState("")
  const [chatMessages, setChatMessages]: any = useState([])
  const [message, setMessage] = useState("")
  const [typingicon, setTypingicon] = useState(false)
  // Auto response for first message
  useEffect(() => {
    setTypingicon(true)
    setTimeout(() => {
      setTypingicon(false)
      let newmsg = {
        msg:
          'Hello. What can we help with? Please add your cell number and question below. Thanks <img src="' +
          smiling +
          '" class="face-smile-icon">',
        type: "right",
      }
      setChatMessages((chatMessages: any) => [...chatMessages, newmsg])
    }, 3000)
  }, [])

  // set input feilds values
  useEffect(() => {
    if (typeof window === "undefined") return
    const messageswindow: any = document.getElementById("msg-list-area")
    messageswindow.scrollTop = messageswindow.scrollHeight
  }, [chatMessages])
  const handleInputChange = (e: any) => {
    let id = e.target.id
    setPhoneNoError("")
    if (id == "phone") {
      let phone = e?.target?.value?.replace(/\D/g, "")
      let phoneorg = e?.target?.value?.replace(/\D/g, "")
      let phonExist = 0
      if (phoneorg?.length == 10) {
        setPhoneNoError("")
        var phoneValid = phone?.match(
          /^(\+?1 ?)?\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
        )
        if (!phoneValid) {
          phone = phoneorg
          setPhoneNoError("Please enter a valid phone number.")
        } else {
          setPhoneNoError("")
          phone = [
            "(",
            phoneValid[2],
            ") ",
            phoneValid[3],
            "-",
            phoneValid[4],
          ].join("")
        }
        setPhoneNo(phone)
        phonExist = 1
      }
      if (phoneorg.length > 10) {
        setPhoneNoError("")
        var phoneValid = phone?.match(
          /^(\+?1 ?)?\(?([0-9]{1})\)?[-. ]?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
        )
        if (!phoneValid) {
          setPhoneNoError("Please enter a valid phone number.")
          phone = phoneorg
        } else {
          setPhoneNoError("")
          phone = [
            phoneValid[2],
            "(",
            phoneValid[3],
            ") ",
            phoneValid[4],
            "-",
            phoneValid[5],
          ].join("")
        }
        setPhoneNo(phone)
        phonExist = 1
      }
      if (phonExist == 0) {
        setPhoneNo(phone)
      }
    }
    if (id == "message") {
      setMessage(e?.target?.value)
    }
  }
  /**
   * Submit Chat Form
   */
  const submitForm = (e: any) => {
    e.preventDefault()
    if (phoneNoError == "" && phoneNo && message && typingicon == false) {
      let phone = phoneNo
      let question = message
      if (phone.length < 10) {
        setPhoneNoError("Please enter a 10 or 11 digit  phone number.")
        return false
      }
      if (phone.replace(/\D/g, "").length > 10) {
        phone = phone.slice(1)
      }
      fetch(process.env.GATSBY_LAMBDA + "/text", {
        method: "POST",
        body: JSON.stringify({ phone, question }),
      })
      let newmsg: any = { msg: message, type: "left" }
      setChatMessages((chatMessages: any) => [...chatMessages, newmsg])
      setTypingicon(true)
      setTimeout(() => {
        newmsg = {
          msg: "Thanks for your message. Our team will respond by text on the provided cell phone number. If you provided a landline number please resend the message or give us a call at (*************. Feel free to close this window.",
          type: "right",
        }
        setChatMessages((chatMessages: any) => [...chatMessages, newmsg])
        setTypingicon(false)
      }, 3000)
      setPhoneNo("")
      setMessage("")
    }
  }

  return (
    <>
      <form onSubmit={submitForm}>
        <div className="message-modal justify-center items-center flex overflow-x-hidden overflow-y-auto fixed inset-0 z-50 outline-none focus:outline-none">
          <div className="relative w-auto my-6 mx-auto max-w-3xl">
            <div className="message-box rounded-2xl shadow-lg relative flex flex-col w-full bg-white outline-none focus:outline-none ">
              <div className="header-gray">
                <button
                  className="ml-auto mt-0 bg-transparent border-0 text-black leading-none outline-none focus:outline-none"
                  onClick={() => closepopup(false)}
                >
                  <span className="bg-transparent opacity-75 text-black h-6 w-6 text-4xl block outline-none focus:outline-none mr-2">
                    ×
                  </span>
                </button>
              </div>
              <div
                className="contact-us-box-content append-msg"
                id="msg-list-area"
              >
                {chatMessages.map((item: any, _index: any) => {
                  return (
                    <div
                      key={_index}
                      className={"message-" + item.type}
                      dangerouslySetInnerHTML={{ __html: item?.msg }}
                    ></div>
                  )
                })}

                {typingicon == true ? (
                  <div className="message-right1">
                    <img
                      src={typing}
                      alt="loading"
                      width="40"
                      className="message-loading"
                    />
                  </div>
                ) : (
                  ""
                )}
              </div>
              <div className="message-box-footer">
                <div className="message-input">
                  <input
                    type="text"
                    placeholder="Mobile Number"
                    onKeyDown={(e: any) => {
                      if (e.keyCode === 13) {
                        submitForm(e)
                      }
                    }}
                    value={phoneNo}
                    onChange={(e) => handleInputChange(e)}
                    id="phone"
                  />
                  <svg
                    className={`w-6 h-6 dark:text-white ${
                      phoneNoError == "" && phoneNo ? "active-msg-btn" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    ></path>
                  </svg>
                </div>
                <span className="red_error">{phoneNoError}</span>
                <div className="message-input">
                  <TextareaAutosize
                    placeholder="Message"
                    onKeyDown={(e: any) => {
                      if (e.keyCode === 13) {
                        submitForm(e)
                      }
                    }}
                    onChange={(e) => handleInputChange(e)}
                    value={message}
                    id="message"
                  />
                  <button className="send-btn">
                    <svg
                      className={`w-6 h-6 dark:text-white ${
                        message && phoneNoError == "" && phoneNo
                          ? "active-msg-btn"
                          : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z"
                      ></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="opacity-25 fixed inset-0 z-40 bg-black"></div>
      </form>
    </>
  )
}

export default MessageModal
