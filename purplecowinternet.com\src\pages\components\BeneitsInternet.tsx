import React from "react"
import { FibreHeading } from "../../components/FibreHeading"
import RoundedContainer from "../../components/RoundedContainer"
import Heart from "../../content/heart_benefits.png"
import Peace from "../../content/peace_hand_benefits.png"
import Speed from "../../content/lightning_benefits.png"
type Feature = {
  image: string
  text: string
}
const featureList: Feature[] = [
  {
    image: Speed, // Replace with actual image URLs
    text: "GIGABIT SPEED",
  },
  {
    image: Peace,
    text: "EXTREMELY\nRELIABLE",
  },
  {
    image: Heart,
    text: "RANKED #1 IN\nCUSTOMER SERVICE",
  },
]
const BeneitsInternet: React.FC = () => {
  return (
    <RoundedContainer bgColor="white">
      <FibreHeading
        className="mt-10"
        content="Benefits"
        align="text-center"
        color="black"
      />
      <div className="flex justify-center items-start gap-10 md:gap-16 lg:gap-20 flex-wrap p-6 sm:mt-10">
        {featureList.map((item, index) => (
          <div
            key={index}
            className=" flex flex-col items-center text-center max-w-[250px] md:max-w-[300px]"
          >
            <img
              src={item.image}
              alt="feature-icon"
              className="h-44 sm:h-52 md:h-60"
            />
            <h1 className="mt-2 !font-anton whitespace-pre-line">
              {item.text}
            </h1>
          </div>
        ))}
      </div>
    </RoundedContainer>
  )
}

export default BeneitsInternet
