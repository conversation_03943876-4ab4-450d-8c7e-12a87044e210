import React, { useEffect } from "react"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import CommonButtonInternet from "../../components/CommonButton"
import { calculateTotalPrice, formatMoney, scrollToTop } from "../../utils"
import { useDispatch, useSelector } from "react-redux"
import { PageStep, setFormData } from "../../redux/formSlice"
import { colorTheme } from "../../data/SrtingConstants"
import SelectTvPlan from "./SelectTvPlan"
import ProgressBarComponent from "./ProgressBarComponent"
import CheckoutBgIcons from "./Checkout_Bg_Icons"

interface Tvprops {
  tvPlans: any
  addons: any
}
const TvPlans: React.FC<Tvprops> = ({ tvPlans, addons }) => {
  const formData = useSelector((state: any) => state.form)
  const dispatch = useDispatch()

  useEffect(() => {
    scrollToTop()
  }, [])

  return (
    <div>
      <RoundedContainer
        className="relative pb-8"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <ProgressBarComponent status={PageStep.TV_PLAN} />
        <CheckoutBgIcons />
        <div className="flex flex-col gap-8 m-5 sm:ml-10 sm:my-10 md:ml-16 md:my-16">
          <FibreHeading
            className="text-start "
            align="text-left"
            color="white"
            content={"Add TV"}
          />
          <FibreDescription
            color="white"
            className="text-start w-[80%]"
            align="text-left"
            content={"Select the plan you would like to add"}
          />
        </div>

        <SelectTvPlan tvPlans={tvPlans} addons={addons} />
      </RoundedContainer>

      <div className="flex sticky bg-white py-3 bottom-0 flex-col sm:flex-row items-center justify-around">
        {!formData.monthEstimate ? (
          <h3 className="font-sans font-bold">Calculating....</h3>
        ) : (
          <h3 className="font-sans font-bold">
            Monthly: {formatMoney(calculateTotalPrice(formData?.monthEstimate))}
          </h3>
        )}
        <div className="flex justify-between gap-5">
          <CommonButtonInternet
            onClick={() =>
              dispatch(setFormData({ page: PageStep.CHECK_PLANS }))
            }
            buttonText="Back"
            textColor="black"
            bgColor="white"
            className="border-black border font-bold"
          />

          <CommonButtonInternet
            onClick={() =>
              dispatch(setFormData({ page: PageStep.HOME_PHONE_PLAN }))
            }
            buttonText="Next"
            textColor="black"
            className="font-bold"
          />
        </div>
      </div>
    </div>
  )
}

export default TvPlans
