import React, { ReactNode } from "react"
import Header from "./Header"
import Footer from "./Footer"
import { Helmet } from "react-helmet"

interface LayoutProps {
  header?: boolean
  children: ReactNode // Add the correct type for children
}

const Layout: React.FunctionComponent<LayoutProps> = ({
  children,
  header = true,
}: LayoutProps) => {
  return (
    <div className="flex flex-col justify-between min-h-screen body--lato">
      {header && <Header />}
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
      >
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Helmet>
      <div>{children}</div>
      <Footer />
    </div>
  )
}

export default Layout
