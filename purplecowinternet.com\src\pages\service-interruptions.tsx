import React from "react"
import Layout from "../components/Layout"
import { Helmet } from "react-helmet"
import { colorTheme } from "../data/SrtingConstants"
import RoundedContainer from "../components/RoundedContainer"

const interruptions = [
  {
    date: "March 11th, 2025",
    message: `You should be back online now—thank you for your patience! If you’re still experiencing any issues, please try unplugging your modem and plugging it back in.

During times like these, the entire Purple Cow team goes all hands on deck to identify the issue and get you back online as quickly as possible. Thank you for being in the herd, we appreciate you.`,
    author: "<PERSON>",
    role: "CEO",
  },
  {
    date: "March 11th, 2025",
    message: `We’re aware of a network outage in progress that is impacting several customers and a few communities.

We are working with our partners to determine the full scope of the impact and to get a timeline for restoration once this has been assessed. We appreciate your understanding and will provide regular updates as we learn more.`,
    author: "<PERSON>",
    role: "CEO",
  },
]

export default function ServiceInterruptions() {
  return (
    <Layout>
      <Helmet>
        <title>Service Interruptions</title>
      </Helmet>

      <RoundedContainer className="sm:px-10 md:px-20 xl:px-40 2xl:px-64 py-10">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold">Service Interruptions</h1>
        </div>

        <div className="space-y-10">
          {interruptions.map((update, index) => (
            <div
              key={index}
              className="bg-white shadow-lg rounded-2xl p-8 transition hover:shadow-purple-300 border border-gray-200"
            >
              <h2 style={{color:colorTheme.DARK_PURPLE}} className="text-xl font-semibold">
                {update.date}
              </h2>
              <p className="mt-4 text-gray-800 whitespace-pre-line leading-relaxed text-lg">
                {update.message}
              </p>
              <div className="mt-6 text-right text-gray-600 font-medium">
                <p>{update.author}</p>
                <p className="text-sm">{update.role}</p>
              </div>
            </div>
          ))}
        </div>
      </RoundedContainer>
    </Layout>
  )
}
