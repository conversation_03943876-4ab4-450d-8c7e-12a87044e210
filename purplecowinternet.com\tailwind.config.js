/** @type {import('tailwindcss/tailwind-config').TailwindConfig} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  mode: "jit",
  darkMode: "media", // or 'media' or 'class'
  theme: {
    fontFamily: {
      sans: ["Roboto", "sans-serif"],
      satoshi: ["Satoshi", "sans-serif"],
      anton: ["Anton", "sans-serif"],
      tt: ["Roboto", "sans-serif"],
    },
    extend: {
      typography: (theme) => ({
        DEFAULT: {
          css: {
            a: {
              color: theme("colors.primary.700"),
            },
            h1: {
              fontWeight: theme("fontWeight.normal"),
            },
            h2: {
              fontWeight: theme("fontWeight.normal"),
            },
            h3: {
              fontWeight: theme("fontWeight.normal"),
            },
            h4: {
              fontWeight: theme("fontWeight.normal"),
            },
            h5: {
              fontWeight: theme("fontWeight.normal"),
            },
            h6: {
              fontWeight: theme("fontWeight.normal"),
            },
          },
        },
      }),
      gridTemplateColumns: {
        table: "min-content 1fr min-content",
      },
      colors: {
        "primary-700": "#7a53b0",
        "primary-800": "#7900a8",
        "primary-900": "#801a80",
      },
      outline: {
        primary: "1px solid #7a53b0",
      },
    },
  },
  variants: {
    extend: {},
  },
}
