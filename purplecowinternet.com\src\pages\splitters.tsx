import React from "react"
import Layout from "../components/Layout"
import { He<PERSON><PERSON> } from "react-helmet"
import { StaticImage } from "gatsby-plugin-image"

const SplittersPage = () => (
  <Layout>
    <Helmet>
      {/* eslint-disable-next-line jsx-a11y/accessible-emoji */}
      <title>Coax Splitters – Purple Cow Internet 💜🐄</title>
    </Helmet>
    <div className="prose prose-lg lg:prose-xl sm:mx-5 md:mx-10 lg:mx-30 xl:mx-72 text-center">
      <h1 className="mt-5 leading-relaxed">Example Coax Splitters</h1>
      <p className="mt-8 leading-relaxed text-xl text-[#374151]">
        Try to follow the coax line back from your modem to see if you have any
        splitters in your home.{" "}
      </p>
      <p className="mt-8 leading-relaxed text-xl text-[#374151]">
        They come in all shapes and sizes however the below are the most popular
        ones.
      </p>
      <StaticImage
        src="../content/splitters.png"
        alt="Various splitters where one coax cable goes in, but two come out."
      />
    </div>
  </Layout>
)

export default SplittersPage
