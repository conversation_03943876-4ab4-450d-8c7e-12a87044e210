import React, { useEffect, useRef } from "react"
import { useDispatch, useSelector } from "react-redux"
import { setFormData } from "../../redux/formSlice"
import { colorTheme } from "../../data/SrtingConstants"
import GoogleAddress from "./GoogleAddress"
import { validationRegEx } from "../../data/Regex"

interface ServiceAddressProps {
  setShowInputCustomPostalCode: (flag: boolean) => void
  showInputCustomPostalCode: boolean
}

const ServiceAddress: React.FC<ServiceAddressProps> = ({
  setShowInputCustomPostalCode,
  showInputCustomPostalCode,
}) => {
  const dispatch = useDispatch()
  const formData = useSelector((state: any) => state.form)
  const inputRef = useRef<HTMLInputElement | null>(null)
  const unitRef = useRef<HTMLInputElement | null>(null)

  useEffect(() => {
    if (
      formData?.shipping_address?.PostalCode &&
      !validationRegEx.postalcode_regex.test(
        formData?.shipping_address?.PostalCode
      )
    ) {
      setShowInputCustomPostalCode(true)
    }
  }, [])

  return (
    <div className="flex justify-center w-full mb-10">
      <GoogleAddress
        googleAddressType={"shipping_address"}
        inputRef={inputRef}
        unitRef={unitRef}
        setShowInputCustomPostalCode={setShowInputCustomPostalCode}
      />
      <div>
        <div className="bg-white my-3 self-center sm:my-10 flex flex-col md:flex-row sm:items-center p-2 lg:p-1 rounded-3xl text-center w-full max-w-2xl">
          <input
            ref={inputRef}
            style={{ fontVariantLigatures: "none" }}
            type="text"
            name="address"
            className="border-none text-lg sm:text-xl font-normal font-sans placeholder:font-sans text-black bg-transparent flex-1 min-w-[280px] p-2 placeholder:text-[#A6A6A6] placeholder:font-normal"
            placeholder="Enter your address"
            defaultValue={formData?.shipping_address?.Line1 || ""}
          />
          <div
            className={`text-gray-300 hidden md:block transition-colors duration-200 group-focus-within:text-[${colorTheme.MEDIUM_PURPLE}]`}
          >
            |
          </div>
          <input
            ref={unitRef}
            style={{ fontVariantLigatures: "none" }}
            type="text"
            maxLength={10} // <-- Prevents typing more than 10 characters
            autoComplete="off"
            className="border-none text-lg sm:text-xl font-normal font-sans placeholder:font-sans text-black bg-transparent flex-1 min-w-[280px] p-2 placeholder:text-[#A6A6A6] placeholder:font-normal"
            name="line2"
            id="line2_address"
            placeholder="Unit"
            defaultValue={formData?.shipping_address?.SubBuilding}
            onChange={(e) => {
              e.target.value?.length <= 10 &&
                dispatch(
                  setFormData({
                    shipping_address: {
                      ...formData.shipping_address,
                      SubBuilding: e?.target?.value?.toUpperCase(),
                    },
                  })
                )
            }}
          />
        </div>
        {showInputCustomPostalCode && (
          <div className="flex justify-center">
            <input
              type="text"
              autoComplete="off"
              className="border-none pl-4 rounded-3xl bg-white text-lg sm:text-xl font-normal font-sans placeholder:font-sans text-black  p-2 placeholder:text-[#A6A6A6] placeholder:font-normal"
              placeholder="Postal Code"
              required
              onChange={(e) => {
                dispatch(
                  setFormData({
                    shipping_address: {
                      ...formData.shipping_address,
                      PostalCode: e.target.value,
                    },
                  })
                )
              }}
            />
          </div>
        )}
        {showInputCustomPostalCode &&
          !validationRegEx.postalcode_regex.test(
            formData?.shipping_address?.PostalCode
          ) && (
            <div className="text-white-500 flex justify-center mt-2">
              <p className="text-center rounded-xl border bg-red-100 text-red-600 p-2">
                Enter valid postal code such as A0B 0C3
              </p>
            </div>
          )}
      </div>
    </div>
  )
}

export default ServiceAddress
