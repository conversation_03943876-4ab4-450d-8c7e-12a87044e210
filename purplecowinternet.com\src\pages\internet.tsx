import React, { useEffect, useState } from "react"
import Layout from "../components/Layout"
import { Helmet } from "react-helmet"
import axios from "axios"
import MessageModal from "./components/common/MessageModal"
import RoundedContainer from "../components/RoundedContainer"
import { FibreHeading } from "../components/FibreHeading"
import HowToGetStarted from "./components/WhyPurpleCow"
import WhyPurpleFibre from "./components/WhyPurpleFibre"
import FibreNetwork from "./components/FibreNetwork"
import FasterThanBigGuys from "./components/FasterThanBigGuys"
import RatedMostReliableInternet from "./components/RatedMostReliableInternet"
import WifiThatGetsToEveryRoom from "./components/WifiThatGetsToEveryRoom"
import InTheNewsPurpleFibre from "./components/InTheNewsPurpleFibre"
import LoveYourSwitch from "./components/LoveYourSwitch"
import ReviewsInternet from "./components/ReviewsInternet"
import JoinTheHerdToday from "./components/JoinTheHerdToday"
import BeneitsInternet from "./components/BeneitsInternet"
import CommonQuestions from "../components/CommonQuestions"
import CheckPlans from "./components/CheckPlans"
import WifiInEveryRoom from "./components/WifiInEveryRoom"
import { useSelector } from "react-redux"
import { colorTheme } from "../data/SrtingConstants"
import {
  eastLinkQuestions,
  purpleFibreQuestions,
} from "../data/CommonQuestionsData"
import { navigate } from "gatsby"
import CommonButtonInternet from "../components/CommonButton"
const FrontInternetPage = () => {
  const [plans, setPlans] = useState<any>(null)
  const [messagePopup, setMessagePopup] = useState(false)
  const addressType = useSelector((state: any) => state.form.addressType)
  const [exclusions, setExclusions] = useState<null | []>(null)
  const [fibreAddresses, setFibreAddresses] = useState<null | []>(null)

  const fetchExcludedAddress = async () => {
    try {
      const response = await axios(
        "https://product-catalog-storage.s3.ca-central-1.amazonaws.com/excludedAddresses.json"
      )
      setExclusions(response?.data || [])
    } catch (err) {
      console.error("Failed to fetch excluded addresses:", err)
    }
  }
  const fetchFibreAddress = async () => {
    try {
      const response = await axios(
        "https://product-catalog-storage.s3.ca-central-1.amazonaws.com/purpleFibreAddresses.json"
      )
      setFibreAddresses(response?.data || [])
    } catch (err) {
      console.error("Failed to fetch channels:", err)
    }
  }
  const fetchData = async () => {
    try {
      const response = await axios(
        process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
          "/snapshots/latest/services/internet/plans.json"
      )
      setPlans(response.data)
    } catch (err) {
      console.error("Failed to fetch internet plans:", err)
    }
  }

  useEffect(() => {
    fetchData()
    fetchExcludedAddress()
    fetchFibreAddress()
  }, [])

  return (
    <Layout>
      <Helmet>
        <title>Internet – Purple Cow Internet 💜🐄</title>
      </Helmet>
      <div className="md:mx-10">
        {/* Check Plans */}

        <CheckPlans
          fibreAddresses={fibreAddresses}
          exclusions={exclusions}
          plans={plans}
        />

        {/* show these component if address is PurpleCow */}
        {addressType === "PurpleCow" && (
          <>
            <WhyPurpleFibre />
            <FibreNetwork />
            <FasterThanBigGuys />
            <RatedMostReliableInternet />
            <WifiThatGetsToEveryRoom />
            <InTheNewsPurpleFibre />
            <CommonQuestions questions={purpleFibreQuestions} />
          </>
        )}

        {addressType === "Eastlink" && (
          <>
            <BeneitsInternet />
            <WifiInEveryRoom />
            <CommonQuestions
              bgColor={colorTheme.MEDIUM_PURPLE}
              questions={eastLinkQuestions}
            />
          </>
        )}

        {/* Why Purple Cow vs the big telcos? */}
        <RoundedContainer className="mt-5" bgColor="white">
          <div className="flex justify-center">
            <FibreHeading
              color="black"
              align="text-center"
              content={"Why Purple Cow vs the big telcos?"}
              className="my-5 md:w-[70%]"
            />
          </div>
          <HowToGetStarted
            steps={[
              "We are local and we honestly care about your experience",
              "Purple Cow actually cares, responds quickly, and treats customers like part of the herd — like real people, not account numbers.",
              "Love us or get your money back. If you’re not completely in love with Purple Cow after your first month, we’ll refund 100% of your money — no questions asked.",
            ]}
          />
        </RoundedContainer>

        {/* Love Your Switch, Or It’s Free! */}
        <LoveYourSwitch />

        {/* Reviews */}
        <ReviewsInternet />

        {/* JOIN THE HERD TODAY */}
        <JoinTheHerdToday
          buttonContent={
            addressType === "" ? (
              <>Check availability &gt;</>
            ) : (
              <>Join the herd</>
            )
          }
        />
      </div>
      {/* Final Note. We Think You're Awesome */}
      <RoundedContainer
        className="flex flex-col justify-center items-center gap-5 sm:gap-10 p-5 sm:p-10 md:p-20"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <h1 className="!font-anton text-center text-white uppercase">
          Final Note. We Think You're <br className="hidden md:block" />
          Awesome
        </h1>
        <h3 className="!font-sans text-white text-center mt-5 lg:w-[60%]">
          If you still have a question, shoot us a message and our team will
          respond within mins
        </h3>

        <div className="flex justify-between gap-6">
          <CommonButtonInternet
            buttonText="Message Us"
            onClick={(e) => {
              const isMobile = /iPhone|iPad|iPod|Android/i.test(
                navigator.userAgent
              )
              if (isMobile) {
                e.preventDefault() // Prevent the default anchor click behavior
                window.location.href =
                  "sms:**************?body=Hey%20Purple%20Cow%20I%20have%20a%20question.%20"
              } else {
                // Open the existing popup on other devices
                setMessagePopup(true)
              }
            }}
            className="px-5 !text-white border border-white"
            bgColor="transparent"
          />
          <CommonButtonInternet
            buttonText="Join the Herd"
            onClick={() => navigate("/join-the-herd")}
            className="px-5"
          />
        </div>
      </RoundedContainer>

      {messagePopup && (
        <MessageModal closepopup={setMessagePopup}></MessageModal>
      )}
    </Layout>
  )
}

export default FrontInternetPage
