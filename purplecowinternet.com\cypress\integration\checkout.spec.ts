/// <reference types="cypress-iframe" />
/// <reference types="cypress" />

import "@testing-library/cypress/add-commands"
import { Customer, Subscription } from "chargebee-typescript/lib/resources"

import { format, addBusinessDays, addYears } from "date-fns"
import { v4 as uuid } from "uuid"
import { CustomerDetails } from "../support"

describe("checkout", () => {
  const exampleUser: CustomerDetails = {
    address: "21 Bald Eagle Pl",
    firstName: "Test",
    lastName: "User",
    postal: "B3N 3H5",
    phoneNumber: "4035550311",
    email: `eastlink+${uuid()}@purplecowinternet.com`,
    suggestedTurnOnDate: format(addBusinessDays(new Date(), 5), "MM-dd-yyyy"),
    cardNumber: "****************",
    cvv: "123",
    expiry: format(addYears(new Date(), 1), "MM/yy"),
  }
  // TODO: Tests to write
  // Referrals

  // Different taxes for different provinces? tough because they're actually the same tax rate
  // Manually entered addresses are correct also for other provinces
  // Test province code works for autocompleted addresses and written.

  // TODO: need to pull down the chargebee info for a customer and make sure it matches.

  // UI Tests
  // Test business days selection.
  // Editing address to change to make sure eveyrthing is removed

  // TODO: tests for newfoundland and other provinces (this is an example address)
  // 417 Newfoundland Dr
  // A1A 4A5
  // NL
  // St. John's

  // TODO: need a test for every different card type, discover, amex, visa, mastercard

  it("works as intended normally", () => {
    const user = { ...exampleUser, firstName: "Normal", lastName: "Test" }
    user.email = `eastlink+${uuid()}@purplecowinternet.com`
    cy.checkout(user, false)
    cy.findByText(/Join the Herd/i).click()
    cy.findByText(/Join the Herd/i).click()
    cy.findByText(/Join the Herd/i).click()

    cy.wait("@checkoutComplete", { timeout: 15000 })
    cy.findByText(/Golly, that was easy!/i)
    cy.checkCheckout(user, 1)
  })

  it("accepts manual addresses", () => {
    const user = {
      ...exampleUser,
      firstName: "Manual",
      lastName: "Address",
      email: `eastlink+${uuid()}@purplecowinternet.com`,
      addressType: "manual",
      Line1: "1545 South Park St",
      Apt: "201",
      City: "Halifax",
      PostalCode: "B3J 4B3",
      ProvinceCode: "NS",
    }

    cy.intercept({
      method: "POST",
      url: Cypress.env("LAMBDA_URL") + "estimate",
    }).as("estimate")

    cy.checkout(user, false)

    cy.wait("@estimate")

    cy.findByText(/Internet 100/i)
      .next()
      .should("contain", "$60.00 / month")

    cy.findByText(/Subtotal/i)
      .next()
      .should("contain", "$60.00+ tax")
    cy.findAllByText("Total").eq(0).next().should("contain", "$69.00")

    //TODO: this is actually WRONG, it doesn't match the recurring above that has the taxes too.
    cy.findByText(/Recurring/i)
      .next()
      .should("contain", "$60.00")

    cy.findByText(/Shipping & Packaging/i)
      .next()
      .should("contain", "$55.00")

    cy.findByText(/Taxes/i).next().should("contain", "$17.25")

    cy.findAllByText("Total").eq(1).next().should("contain", "$132.25")

    cy.findByText(/Join the Herd/i).click()

    cy.wait("@checkoutComplete", { timeout: 15000 })

    cy.checkCheckout(user)

    cy.findByText(/Golly, that was easy!/i)
  })

  it("accepts manual addresses with full spelling", () => {
    const user = {
      ...exampleUser,
      firstName: "Manual",
      lastName: "Full Province",
      email: `eastlink+${uuid()}@purplecowinternet.com`,
      addressType: "manual",
      Line1: "1545 South Park St",
      Apt: "201",
      City: "Halifax",
      PostalCode: "B3J 4B3",
      ProvinceCode: "Nova Scotia",
    }

    cy.intercept({
      method: "POST",
      url: Cypress.env("LAMBDA_URL") + "estimate",
    }).as("estimate")

    cy.checkout(user, false)

    cy.wait("@estimate")

    cy.findByText(/Internet 100/i)
      .next()
      .should("contain", "$60.00 / month")

    cy.findByText(/Subtotal/i)
      .next()
      .should("contain", "$60.00+ tax")
    cy.findAllByText("Total").eq(0).next().should("contain", "$69.00")

    //TODO: this is actually WRONG, it doesn't match the recurring above that has the taxes too.
    cy.findByText(/Recurring/i)
      .next()
      .should("contain", "$60.00")

    cy.findByText(/Shipping & Packaging/i)
      .next()
      .should("contain", "$55.00")

    cy.findByText(/Taxes/i).next().should("contain", "$17.25")

    cy.findAllByText("Total").eq(1).next().should("contain", "$132.25")

    cy.findByText(/Join the Herd/i).click()

    cy.wait("@checkoutComplete", { timeout: 15000 })

    cy.checkCheckout(user)

    cy.findByText(/Golly, that was easy!/i)
  })

  it("accepts returning customers with the same payment but only one method.", () => {
    const user = { ...exampleUser }
    user.cardNumber = "****************"
    cy.checkout(user)
    cy.findByText(/Golly, that was easy!/i)
  })

  it("accepts returning customers with a different payment", () => {
    const user = { ...exampleUser }
    user.cardNumber = "****************"

    cy.checkout(user)
    cy.findByText(/Golly, that was easy!/i)
  })

  it("declines returning customers with the same payment but two methods total.", () => {
    const user = { ...exampleUser }
    user.cardNumber = "****************"

    cy.checkout(user)
    cy.findByText("You can text us at 833 331 1163.", { exact: false })
  })

  it("allows for users with businesses that use different addresses", () => {
    const user = { ...exampleUser }
    user.email = `eastlink+${uuid()}@purplecowinternet.com`
    user.mailingAddress = "22 bald eagle pl"
    user.mailingPostal = "B3N 3H5"
    user.company = "Testing Company"

    cy.checkout(user)
    cy.findByText(/Golly, that was easy!/i)
  })

  it("adds addons properly for monthly", () => {
    const user = { ...exampleUser }
    user.email = `eastlink+${uuid()}@purplecowinternet.com`

    cy.intercept({
      method: "POST",
      url: Cypress.env("LAMBDA_URL") + "estimate",
    }).as("estimate")

    cy.checkout(user, false)

    cy.findByText("Add-ons").click()
    cy.findAllByText(/Add to Order/i)
      .first()
      .click()
    // This is the first one because we searched after we clicked the other one
    cy.findAllByText(/Add to Order/i)
      .eq(1)
      .click()
    cy.findByText("Next").click()

    cy.wait("@estimate")

    cy.findByText(/Internet 100/i)
      .next()
      .should("contain", "$60.00 / month")

    cy.findByText(/200 MBPs Add-on 2/i)
      .next()
      .should("contain", "$20.00 / month")

    cy.findByText(/Subtotal/i)
      .next()
      .should("contain", "$80.00+ tax")
    cy.findAllByText("Total").eq(0).next().should("contain", "$92.00")

    //TODO: this is actually WRONG, it doesn't match the recurring above that has the taxes too.
    cy.findByText(/Recurring/i)
      .next()
      .should("contain", "$80.00")

    cy.findByText(/Shipping & Packaging/i)
      .next()
      .should("contain", "$55.00")

    cy.findByText(/Mercku Router/i)
      .next()
      .should("contain", "$225.00")

    cy.findByText(/Taxes/i).next().should("contain", "$54.00")

    cy.findAllByText("Total").eq(1).next().should("contain", "$414.00")
  })

  it("should remove addons properly", () => {
    cy.intercept({
      method: "POST",
      url: Cypress.env("LAMBDA_URL") + "estimate",
    }).as("estimate")

    cy.findByText("Add-ons").click()

    cy.findAllByText(/remove/i)
      .eq(1)
      .click()
    cy.findByText("Next").click()

    cy.wait("@estimate")

    cy.findByText(/Mercku Router/i).should("not.exist")
  })

  it("adds addons properly for yearly", () => {
    cy.intercept({
      method: "POST",
      url: Cypress.env("LAMBDA_URL") + "estimate",
    }).as("estimate")

    cy.findByText(/Yearly Plan/i).click()
    cy.wait("@estimate")

    cy.findByText(/Internet 100 Yearly/i)
      .next()
      .should("contain", "$648.00 / year")

    cy.findByText(/200 MBPS Add-on Yearly/i)
      .next()
      .should("contain", "$216.00 / year")

    cy.findByText(/Subtotal/i)
      .next()
      .should("contain", "$864.00+ tax")
    cy.findAllByText("Total").eq(0).next().should("contain", "$993.60")

    //TODO: this is actually WRONG, it doesn't match the recurring above that has the taxes too.
    cy.findByText(/Recurring/i)
      .next()
      .should("contain", "$864.00")

    cy.findByText(/Shipping \& Packaging/i)
      .next()
      .should("contain", "$55.00")

    cy.findByText(/Taxes/i).next().should("contain", "$137.85")

    cy.findAllByText("Total").eq(1).next().should("contain", "$1,056.85")
  })

  it("handles invalid postal codes properly", () => {
    const customer = {
      address: "721 bloor st W",
      postal: "L1J 5Y6",
    }

    cy.visit("/join-the-herd/")
    cy.address(customer.address, customer.postal, /Service Address/i)

    cy.findByText(/next/i).click()

    cy.findByText(/I'm going to double check this address/i)

    // TODO: I should make this send a text too.
  })

  it("excludes addresses properly", () => {
    const customer = {
      address: "8 bay rd sandy cove",
      postal: "B0V 1E0",
    }

    cy.visit("/join-the-herd/")
    cy.address(customer.address, customer.postal, /Service Address/i)

    cy.findByText(/next/i).click()

    cy.findByText(
      /Dang! Looks like we are not able to get our internet into your home yet./i
    )
  })

  it("excludes manual addresses properly", () => {
    // TODO: double check this works
  })

  it("handles invalid manual postal codes properly", () => {
    // TODO: double check this works
  })

  it("handles apartment numbers correctly", () => {
    //TODO:
  })

  it("accepts addresses from other provinces manually", () => {})
})
