import React from "react"

interface HeadingProps {
  content: any
  className?: string
  align: "text-left" | "text-right" | "text-center"
  color: string
  id?: string
}
export const FibreHeading: React.FC<HeadingProps> = ({
  content,
  className,
  align,
  color,
  id,
}) => {
  return (
    <h1
      id={id}
      style={{ color }}
      className={`!font-anton uppercase text-6xl md:text-7xl lg:text-8xl  text-center md:${align} ${className}`}
    >
      {content}
    </h1>
  )
}

interface DescriptionProps {
  content: any
  align: "text-left" | "text-right" | "text-center"
  color: string
  className?: string
}
export const FibreDescription: React.FC<DescriptionProps> = ({
  content,
  className,
  align,
  color,
}) => {
  return (
    <h3
      style={{ color }}
      className={`!font-sans text-2xl sm:text-3xl md:text-4xl font-bold text-center md:${align} ${className}`}
    >
      {content}
    </h3>
  )
}
