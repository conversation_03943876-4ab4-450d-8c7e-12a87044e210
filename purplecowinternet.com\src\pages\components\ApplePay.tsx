import React, { useEffect } from "react"
import { decrypt, encrypt } from "../../utils"

function App(props: any) {
  // Refer chargebee documentatoin for more  (https://www.chargebee.com/checkout-portal-docs/applepay-tutorial.html)
  useEffect(() => {
    if (props?.total == undefined || props?.total == null) return // value can be 0 maye be because of referral code
    const script = document.createElement("script")
    script.src = "https://js.chargebee.com/v2/chargebee.js"
    script.async = true
    script.onload = () => {
      const cbInstance: any = window.Chargebee.init({
        site: process.env.GATSBY_CHARGEBEE_SITE || "",
        publishableKey: process.env.GATSBY_CHARGEBEE_API_KEY || "",
      })

      if (
        !(window as any).ApplePaySession ||
        !(window as any).ApplePaySession.canMakePayments()
      ) {
        console.warn("Device/browser doesn't support Apple Pay")
        return
      }

      function createPaymentIntent() {
        return fetch(`${process.env.GATSBY_LAMBDA}/create-payment-intent`, {
          method: "POST",
          body: encrypt(
            JSON.stringify({
              amount: props?.total,
              currency_code: "CAD",
              payment_method_type: "apple_pay",
            })
          ),
        })
          .then((response) => response.text())
          .then((encryptedText) => decrypt(encryptedText))
          .then((decryptedText) => JSON.parse(decryptedText))
          .then((resp) => resp)
          .catch((error) =>
            console.error("❌ Error in payment intent creation:", error)
          )
      }

      cbInstance.load("apple-pay").then((applePayHandler: any) => {
        createPaymentIntent()
          .then((payment_intent) => {
            applePayHandler.setPaymentIntent(payment_intent.payment_intent)
            return applePayHandler.mountPaymentButton("#apple-pay-button", {
              buttonType: "check-out",
            })
          })
          .then(() => {
            return props.setApplePayButtonMounted(true)
          })
          .catch(() => {
            console.warn("Error mounting button")
            return props.setApplePayButtonMounted(false)
          })
          .then(() => applePayHandler.handlePayment())
          .then((response) => {
            props.payPayment("apple_pay", response?.paymentIntent?.id)
          })
          .catch((error) => {
            console.error("Payment failed", error)
          })
      })
    }
    document.body.appendChild(script)
  }, [props?.total])

  useEffect(() => {
    const applePayButton = document.getElementById("apple-pay-button")
    if (props.applePayButtonMounted && applePayButton) {
      applePayButton.style.whiteSpace = "nowrap"
      applePayButton.style.borderRadius = "16px" // Equivalent to Tailwind 'rounded-lg'
      applePayButton.style.padding = "28px 96px" // Equivalent to 'py-7 px-24'
    } else {
      if (applePayButton) {
        applePayButton.style.padding = "0px"
      }
    }
  })

  return (
    <div className="flex w-full flex-col">
      {props.applePayButtonMounted && (
        <div className="text-center hidden sm:block my-3 text-white">
          or checkout quickly with
        </div>
      )}
      <div
        className="whitespace-nowrap py-7 rounded-2xl px-24"
        id="apple-pay-button"
      ></div>
    </div>
  )
}

export default App
