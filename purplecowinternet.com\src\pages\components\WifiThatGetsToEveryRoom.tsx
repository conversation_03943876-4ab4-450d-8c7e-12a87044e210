import React from "react"
import purplefibrLifestyle from "../../content/PurpleFibre_lifestyle.png"
import flyingCow from "../../content/Flying_Cow.gif"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
const WifiThatGetsToEveryRoom = () => {
  return (
    <RoundedContainer bgImage={purplefibrLifestyle}>
      <div className="w-full bg-cover min-h-[40dvh] sm:min-h-[80dvh] bg-center flex items-center sm:p-10 ">
        <div className="flex flex-col justify-start w-[55%]">
          <FibreHeading
            color="white"
            align="text-left"
            className="text-start" // keep text left side for mobile also
            content={
              <span className="!font-anton">
                Wifi that gets to every
                <span className="flex !font-anton">
                  room
                  <img src={flyingCow} className="h-[1em]" alt="Dancing Cow" />
                </span>
              </span>
            }
          />
          <FibreDescription
            className="text-start mt-5 hidden md:block"
            align="text-left"
            color="white"
            content={
              "Intelligent channel selection ensures that the optimal Wi-Fi channel is always selected, avoiding any Wi-Fi glitches"
            }
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default WifiThatGetsToEveryRoom
