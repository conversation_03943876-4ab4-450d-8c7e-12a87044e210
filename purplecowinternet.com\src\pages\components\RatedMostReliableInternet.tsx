import React from "react"
import cortecaVideo from "../../content/Corteca.gif"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import CommonButtonInternet from "../../components/CommonButton"
import { navigate } from "gatsby"
const RatedMostReliableInternet = () => {
  return (
    <RoundedContainer bgColor="white">
      <div className="flex sm:p-10 flex-col-reverse md:flex-row justify-between items-center min-h-[60vh] w-full">
        <div className="flex items-center justify-center md:justify-start w-full md:w-[50%]">
          {/* <video
            autoPlay
            loop
            controls
            muted
            playsInline
            className="h-auto object-contain rounded-3xl"
          >
            <source src={cortecaVideo} type="video/mp4" />
            Your browser does not support the video.
          </video> */}

          <img
            src={cortecaVideo}
            className="h-auto object-contain rounded-3xl"
            alt="cortecaVideo"
          />
        </div>

        <div className="flex flex-col justify-center items-center md:items-end text-right w-full md:w-[50%] md:mr-10 mt-5">
          <FibreHeading
            color="black"
            align="text-right"
            className="text-end"
            content={"Rated Most reliable internet"}
          />
          <FibreDescription
            color="black"
            align="text-right"
            content={
              "We built our network from the ground up make sure your connection stays live."
            }
            className="my-5 text-end"
          />
          <CommonButtonInternet
            buttonText={"Switch Today"}
            onClick={() => navigate("/join-the-herd")}
            className="mt-5 hidden md:block"
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default RatedMostReliableInternet
