import React from "react"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreHeading } from "../../components/FibreHeading"
import Circle from "../../content/CircleStepsblack.jpg"
import CommonButtonInternet from "../../components/CommonButton"
import { colorTheme } from "../../data/SrtingConstants"
import { navigate } from "gatsby"
const HowToGetStarted: React.FC = () => {
  const steps = [
    {
      number: 1,
      content: (
        <>
          Sign up <br /> online
          <br />
          <div className="flex justify-center">
            <CommonButtonInternet
              buttonText={<>Check availability &gt; </>}
              bgColor="white"
              textColor={colorTheme.MEDIUM_PURPLE}
              onClick={() => navigate("/join-the-herd")}
              className="py-4 mt-4 font-normal"
            />
          </div>
        </>
      ),
    },
    {
      number: 2,
      content: (
        <>
          Pick an install <br /> time
        </>
      ),
    },
    {
      number: 3,
      content: (
        <>
          You have the <br /> best Internet <br /> in Canada
        </>
      ),
    },
  ]
  return (
    <RoundedContainer className="p-5" bgColor="black">
      <FibreHeading
        color="white"
        className="my-16"
        align="text-center"
        content={"How to get started"}
      />

      <div className="flex flex-col gap-4 md:flex-row justify-evenly">
        {steps.map((step, index) => {
          return (
            <div
              key={index}
              className="text-white text-center flex flex-col items-center"
            >
              <div
                className="w-48 h-48 bg-black bg-no-repeat bg-center bg-contain flex items-center justify-center"
                style={{ backgroundImage: `url(${Circle})` }}
              >
                <h1 className="text-white font-sans font-bold text-9xl">
                  {step.number}
                </h1>
              </div>

              <h1 className="font-sans text-white font-bold my-16">
                {step.content}
              </h1>
            </div>
          )
        })}
      </div>
    </RoundedContainer>
  )
}

export default HowToGetStarted
