import React from "react"
import MessageModal from "../pages/components/common/MessageModal"
import { FacebookLogo, InstagramLogo, YouTubeLogo } from "../icons/Icons"
import RoundedContainer from "./RoundedContainer"
import Purple<PERSON>owLogo from "../content/cow.png"
import { navigate } from "gatsby"
const Footer = () => {
  const [messagePopup, setMessagePopup] = React.useState(false)

  return (
    <RoundedContainer bgColor="#202E39" className="!mt-0">
      <footer className="bg-[#1F2B34] text-white px-6 py-10 rounded-b-3xl">
        <div className="max-w-7xl mx-auto flex flex-wrap justify-between gap-10 text-sm">
          {/* Logo & Socials */}
          <div className="flex flex-col gap-4">
            <img
              src={PurpleCowLogo}
              alt="Purple Cow Logo"
              className="h-14 object-contain"
            />
            <div className="flex items-center gap-4">
              <a
                target="_blank"
                href="//web.facebook.com/purplecowinternet"
                className="social-icon"
              >
                <FacebookLogo />
              </a>

              <a
                target="_blank"
                href="//www.instagram.com/purplecowinternet/"
                className="social-icon"
              >
                <InstagramLogo />
              </a>
              <a
                target="_blank"
                href="https://www.youtube.com/@purplecowinternet"
                className="social-icon"
              >
                <YouTubeLogo />
              </a>
            </div>
          </div>

          {/* Links - Column 1 */}
          <div className="flex flex-col gap-2">
            <button
              className="text-start"
              onClick={() => navigate("/internet")}
            >
              Internet
            </button>
            <button className="text-start" onClick={() => navigate("/tv")}>
              TV
            </button>
            <button
              className="text-start"
              onClick={() => navigate("/home-phone")}
            >
              Phone
            </button>
            <button
              className="text-start"
              onClick={() => navigate("/purplefibre")}
            >
              Purple Fibre
            </button>
          </div>

          {/* Links - Column 2 */}
          <div className="flex flex-col gap-2">
            <button
              className="text-start"
              onClick={() => setMessagePopup(true)}
            >
              Message Us
            </button>

            <button
              className="text-start"
              onClick={() => {
                window.open("https://purplecowinternet.chargebeeportal.com")
              }}
            >
              My Account
            </button>
            <button
              className="text-start"
              onClick={() => navigate("/thoughts")}
            >
              Thoughts
            </button>

            <button
              className="text-start"
              onClick={() => navigate("/sponsorships")}
            >
              Sponsorships
            </button>
            <button
              className="text-start"
              onClick={() => navigate("/neighbor-in-need")}
            >
              Neighbor in Need
            </button>
            <button
              className="text-start"
              onClick={() => navigate("/terms-of-use")}
            >
              Terms of Service
            </button>
          </div>

          {/* Contact Info */}
          <div className="text-sm leading-6 max-w-xs">
            <p>
              Text us at{" "}
              <a href="tel:**************" className="underline">
                1–902–800–2660
              </a>{" "}
              for a customer representative between:
            </p>
            <p className="mt-2">Monday – Friday | 8:00am – 11:00pm</p>
            <p>Saturday – Sunday | 9:00am – 10:00pm</p>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-400 my-6"></div>

        {/* Copyright */}
        <p className="text-center text-sm">
          Copyright 2025 PurpleCowInternet. All Right Reserved.
        </p>

        {messagePopup ? (
          <MessageModal closepopup={setMessagePopup}></MessageModal>
        ) : null}
      </footer>
    </RoundedContainer>
  )
}

export default Footer
