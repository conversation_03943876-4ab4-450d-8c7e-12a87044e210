import { navigate } from "gatsby"
import { map, sum } from "lodash"
import React, { useState, useEffect, useRef } from "react"
import { Helmet } from "react-helmet"
import { getReferralLink, useReferralState } from "../../ReferralWrapper"
import { formatMoney, encrypt, decrypt, scrollToTop } from "../../utils"
import { getSelectedPlanData } from "../../customfunction"
import LoaderComponent from "./LoaderComponent"
import { crudSessionStorage, sessionStorageKeys } from "../../sessionStorage"
import {
    AppleLogo,
    CardIcon,
    CreditCardsIcons,
    EditIcon,
    LoaderIcon,
} from "../../icons/Icons"
import { useChargebee } from "../../hooks"
import EditSelectedServices from "./EditSelectedServices"
import ApplePay from "./ApplePay"
import OrderSummaryCheckout from "./OrderSummaryCheckout"
import CreditCardComponent from "./CreditCardComponent"
import ServiceAddressCheckout from "./ServiceAddressCheckout"
import { useDispatch, useSelector } from "react-redux"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreHeading } from "../../components/FibreHeading"
import ContactComponent from "./ContactFormComponent"
import CommonButtonInternet from "../../components/CommonButton"
import { setFormData } from "../../redux/formSlice"
import { colorTheme } from "../../data/SrtingConstants"
import { validationErrorMessages, validationRegEx } from "../../data/Regex"
import CheckoutBgIcons from "./Checkout_Bg_Icons"

interface CheckoutProps {
    plansData: any
    addonsData: any
    monthEstimatePrice: () => void
}
const CheckoutComponent: React.FC<CheckoutProps> = ({
    plansData,
    addonsData,
    monthEstimatePrice,
}) => {
    const formData = useSelector((state: any) => state.form)
    const dispatch = useDispatch()

    if (!plansData || !addonsData || !formData) {
        return <LoaderComponent values="Loading....." />
    }
    const [selectedMethod, setSelectedMethod] = useState<"card" | "apple_pay">(
        "card"
    )

    // payment methods
    const allowUserToCheckoutUsingApplePay = true
    const [applePayButtonMounted, setApplePayButtonMounted] = useState(false)

    // estimate
    const [estimateData, setEstimateData] = useState<any>(null)
    const [loadingEstimate, setLoadingEstimate] = useState(false)
    let recurring = estimateData?.invoice_estimate?.line_items?.filter(
        ({ date_from, date_to }: any) => date_from !== date_to
    )
    let recurringSubtotal = sum(map(recurring, "amount"))

    // edit contact information
    const [editContactInformation, setEditContactInformation] = useState(false)
    const [contactInfoErros, setContactInfoErrors] = useState({
        firstNameError: "",
        lastNameError: "",
        phoneError: "",
        emailError: "",
        Line1Error: "",
        PostalCodeError: "",
        businessError: "",
    })
    // update button click after editing contact information
    const handleUpdateContactInformation = async (e: any) => {
        const { first_name, last_name, phone, email, cf_company_name } =
            formData?.customer || {}
        const errors: any = {}

        if (!first_name?.trim())
            errors.firstNameError = validationErrorMessages.ENTER_FIRST_NAME
        if (!last_name?.trim())
            errors.lastNameError = validationErrorMessages.ENTER_LAST_NAME
        if (formData?.is_business && !cf_company_name?.trim())
            errors.businessError = validationErrorMessages.ENTER_BUSINESS_NAME
        const phoneDigits = phone?.replace(/\D/g, "")

        if (!validationRegEx.phone_regex.test(phoneDigits || "")) {
            errors.phoneError = validationErrorMessages.ENTER_CELL_PHONE_NUMBER
        }

        if (!validationRegEx.email_regex.test(email || "")) {
            errors.emailError = validationErrorMessages.ENTER_VALID_EMAIL_ADDRESS
        }

        if (!formData?.mailing_address?.Line1)
            errors.Line1Error = validationErrorMessages.ENTER_ADDRESS
        if (
            !validationRegEx.postalcode_regex.test(
                formData?.mailing_address?.PostalCode || ""
            )
        ) {
            errors.PostalCodeError = validationErrorMessages.ENTER_VALID_POSTAL_CODE
        }

        if (Object.keys(errors).length > 0) {
            setContactInfoErrors({ ...contactInfoErros, ...errors })
            return
        }

        e.preventDefault()
        setEditContactInformation(false)
        await postCartData()
    }
    // edit service address
    const [editServiceAddress, setEditServiceAddress] = useState(false)

    // edit selected services
    const [editServices, setEditServices] = useState({
        editServicePopup: false,
        internet: false,
        tv: false,
        homePhone: false,
    })

    // card
    const cardRef = useRef<any | null>(null)
    const isChargebeeLoaded = useChargebee()
    const [cardHolderName, setCardHolderName] = useState("")
    let [cardErrors, setCardErrors] = useState({
        name: "",
        number: "",
        expiry: "",
        cvv: "",
    })
    const tabType = false

    // referral
    const referralState = useReferralState()
    const [referralUrl, setReferralUrl] = useState<string[]>([])
    const [referralIsSet, setReferralIsSet] = useState(false)

    // updating cart and processing payment
    const [updatingCart, setUpdatingCart] = useState(false)
    const [processingPayment, setProcessingPayment] = useState(false)
    const [error, SetError] = useState<string | null>(null)

    // terms and conditions
    const [acceptedTermsAndConditions, setAcceptedTermsAndConditions] = useState(false)

    // disable buttons
    const disableJoinTheHerd =
        editContactInformation ||
        editServiceAddress ||
        editServices.internet ||
        editServices.tv ||
        editServices.homePhone ||
        !formData?.monthEstimate ||
        updatingCart ||
        processingPayment ||
        !acceptedTermsAndConditions

    const disableNeighborinNeed =
        editContactInformation ||
        editServices.internet ||
        editServices.tv ||
        editServices.homePhone ||
        !formData?.monthEstimate ||
        updatingCart ||
        processingPayment

    // once button is get mounted and while updating the cart or calculating the estimate
    const loadingApplePayButton =
        loadingEstimate ||
        updatingCart ||
        editServices.internet ||
        editServices.tv ||
        editServices.homePhone
    // card

    let orderTotal = formData?.monthEstimate?.invoice_estimate?.amount_due
    const handleChangePaymentMethod = (value: "card" | "apple_pay") => {
        setSelectedMethod(value)
    }
    if (
        !referralIsSet &&
        referralUrl?.length === 0 &&
        referralState?.length !== 0
    ) {
        setReferralUrl(referralState)
        setReferralIsSet(true)
    }

    // estimate price
    const estimatePrice = async () => {
        setLoadingEstimate(true)
        if (formData?.monthEstimate != null) {
            setEstimateData(formData?.monthEstimate)
            setLoadingEstimate(false)
        }
    }

    // calculate price
    const getTotalPrices = () => {
        let totalRecurringPrice = 0
        let totalDueTodayPrice = 0
        if (formData.monthEstimate != null) {
            let recurring =
                formData?.monthEstimate.invoice_estimate.line_items.filter(
                    ({ date_from, date_to }: any) => date_from !== date_to
                )

            let subtotal = sum(map(recurring, "amount"))
            let TaxesAmount = sum(
                map(recurring, (item) =>
                    item.tax_rate != undefined && item.tax_rate != 0
                        ? item.unit_amount * item.quantity * (item.tax_rate / 100)
                        : 0
                )
            )
            let total = subtotal + TaxesAmount
            totalRecurringPrice = total
            totalDueTodayPrice = formData?.monthEstimate?.invoice_estimate.amount_due
        }
        return [totalRecurringPrice, totalDueTodayPrice]
    }

    // update cart data in salesforce
    const postCartData = async (type = "update") => {
        try {
            let uuid = crudSessionStorage(
                "get",
                sessionStorageKeys.CART_ID,
                "",
                "single"
            )
            let isPortPhone = false
            if (
                formData?.addons?.indexOf("monthly_voip_alianza_res_home_phone") !== -1
            ) {
                isPortPhone = true
            }
            const baseTvPlanInfo =
                formData?.tv_plan != null
                    ? plansData?.tv.filter((obj: any) => obj.id === formData?.tv_plan)[0]
                    : null
            const baseTvPlan =
                formData?.tv_plan != null
                    ? baseTvPlanInfo.billing_period[0].monthly.api_name
                    : null
            const TvAddons =
                formData?.tv_plan != null && formData?.tv_optional_plans != null
                    ? addonsData.tv
                        .filter((obj: any) =>
                            formData?.tv_optional_plans.includes(obj.id)
                        )
                        .map((obj: any) =>
                            obj.billing_period[0].monthly
                                ? obj.billing_period[0].monthly.api_name
                                : obj.billing_period[0].non_recurring.api_name
                        )
                    : []
            const extraTvPlans = TvAddons.length != 0 ? TvAddons : []
            const iptvProducts = extraTvPlans?.filter((plan: string) =>
                plan?.includes("IPTV")
            )
            const epkgPackages = extraTvPlans?.filter((plan: string) =>
                plan?.includes("EPKG")
            )
            const singleChannels =
                formData?.tv_plan != null && formData?.tv_optional_single_plans != null
                    ? baseTvPlanInfo?.optional_single_channels
                        .filter((ch: any) =>
                            formData?.tv_optional_single_plans?.includes(ch.id)
                        )
                        .map((ch: any) => ch.api_name)
                    : []

            const [totalRecurringPrice, totalDueTodayPrice] = getTotalPrices()

            const newValues =
                type == "complete"
                    ? { Cart_ID__c: uuid ? uuid : "", Checkout_Complete__c: true }
                    : {
                        Cart_ID__c: uuid ? uuid : "",
                        FirstName: formData?.customer?.first_name,
                        LastName: formData?.customer?.last_name,
                        Email: formData?.customer?.email,
                        Primary_Phone__c: formData?.customer?.phone,
                        Porting_Phone_Number__c:
                            formData?.is_existing_phone && isPortPhone
                                ? formData?.customer?.port_number
                                : "",
                        Business_Name__c: formData?.is_business
                            ? formData?.customer?.cf_company_name
                            : "",
                        Requested_Install_Date__c:
                            formData?.customer?.cf_suggested_turn_on_date,
                        Mailing_Suite_Unit__c:
                            formData?.is_mailing == false
                                ? formData?.mailing_address?.SubBuilding
                                : formData?.shipping_address?.SubBuilding,
                        Mailing_Street_Number__c:
                            formData?.is_mailing == false
                                ? formData?.mailing_address?.BuildingNumber
                                : formData?.shipping_address?.BuildingNumber,
                        Mailing_Street__c:
                            formData?.is_mailing == false
                                ? formData?.mailing_address?.Line1
                                : formData?.shipping_address?.Line1,
                        Mailing_City__c:
                            formData?.is_mailing == false
                                ? formData?.mailing_address?.City
                                : formData?.shipping_address?.City,
                        Mailing_State_Province__c:
                            formData?.is_mailing == false
                                ? formData?.mailing_address?.Province
                                : formData?.shipping_address?.Province,
                        Mailing_Country__c:
                            formData?.is_mailing == false
                                ? formData?.mailing_address?.CountryIso2
                                : formData?.shipping_address?.CountryIso2
                                    ? formData?.shipping_address?.CountryIso2
                                    : "CA",
                        Mailing_Zip_Postal_Code__c:
                            formData?.is_mailing == false
                                ? formData?.mailing_address?.PostalCode
                                : formData?.shipping_address?.PostalCode,
                        Service_Suite_Unit__c: formData?.shipping_address?.SubBuilding,
                        Service_Street_Number__c:
                            formData?.shipping_address?.BuildingNumber,
                        Service_Street__c: formData?.shipping_address?.Line1,
                        Service_City__c: formData?.shipping_address?.City,
                        Service_State_Province__c: formData?.shipping_address?.Province,
                        Service_Zip_Postal_Code__c:
                            formData?.shipping_address?.PostalCode,
                        Service_Country__c: formData?.shipping_address?.CountryIso2
                            ? formData?.shipping_address?.CountryIso2
                            : "CA",
                        SA_SFRecordID__c: formData?.SA_SFRecordID,
                        Plan__c: plansData?.internet?.filter(
                            (obj: any) => obj?.id === formData?.plan_id
                        )[0]?.billing[0]?.monthly?.api_name,
                        Referral__c: getReferralLink().join(","),
                        Plan_TV_Base_Package__c: baseTvPlan,
                        Plan_TV_Extra_Packages__c: epkgPackages?.join(","),
                        Plan_TV_IPTV_Products__c:
                            iptvProducts?.length === 0
                                ? ""
                                : iptvProducts?.join(",")?.replace(/^monthly_/, ""), // remove monthly_ to match with saledforce value
                        Plan_TV_Single_Channels__c: singleChannels?.join(","),
                        Plan_HomePhone_Base_Package__c: formData?.addons?.join(","),
                        Plan_Optional_Addons__c: formData?.optional_addons?.join(","),
                        Total_Recurring_Price__c: totalRecurringPrice?.toString(),
                        Total_Due_Today_Price__c: totalDueTodayPrice?.toString(),
                    }

            setUpdatingCart(true)

            const response = await fetch(`${process.env.GATSBY_LAMBDA}/update-cart`, {
                method: "POST",
                body: encrypt(JSON.stringify(newValues)),
            })

            crudSessionStorage("delete", sessionStorageKeys.CART_ID)
            if (!response?.ok || response?.status != 200) {
                setEditServiceAddress(false)
                setUpdatingCart(false)
                return ""
            }
            setEditServiceAddress(false)
            setUpdatingCart(false)

            let respData = JSON.parse(decrypt(await response.text()))

            if (respData?.cart_id) {
                crudSessionStorage(
                    "add",
                    sessionStorageKeys.CART_ID,
                    respData?.cart_id,

                    "single"
                )
            }
        } catch (error: any) {
            console.error(error)
            setEditServiceAddress(false)

            setUpdatingCart(false)
            // setformData((prev: any) => ({ ...prev, isUpdateCart: 1 }))
        }
    }

    // processing payment (join the herd click)
    const payPayment = async (paymentMethod: string, applePayData?: any) => {
        if (paymentMethod === "card" && cardHolderName === "") {
            setCardErrors((prev: any) => ({
                ...prev,
                name: "Please enter name",
            }))
            setEditServices({
                editServicePopup: false,
                internet: false,
                tv: false,
                homePhone: false,
            })
            document.getElementById("NameonCard")?.focus()
            return
        }

        if (processingPayment) return false

        const uuid = crudSessionStorage(
            "get",
            sessionStorageKeys.CART_ID,
            "",
            "single"
        )
        const { customer, shipping_address } = formData || {}
        const { first_name, last_name } = customer || {}

        const isValidData =
            shipping_address?.Line1 &&
            typeof first_name !== "undefined" &&
            (paymentMethod === "card" ? estimateData != null : true)

        if (!isValidData) {
            setProcessingPayment(false)
            SetError("Invalid address or estimate price error.")
            return
        }

        setProcessingPayment(true)
        setEditServices({
            editServicePopup: false,
            internet: false,
            tv: false,
            homePhone: false,
        })
        SetError(null)
        localStorage.setItem("checkoutComplete", "notdone")

        try {
            const planData = getSelectedPlanData(formData, plansData, addonsData, "M")

            let addons = planData?.all_addons || []
            if (planData?.additional_plans?.length > 0)
                addons = addons.concat(planData.additional_plans)
            if (planData?.optional_plans?.length > 0 && formData?.tv_plan != null) {
                addons = addons.concat(planData.optional_plans)
            }

            const paymentData =
                paymentMethod === "card"
                    ? await cardRef?.current?.tokenize({
                        firstName: first_name,
                        lastName: last_name,
                    })
                    : applePayData

            const baseTvPlanInfo = formData?.tv_plan
                ? plansData.tv.find((obj: any) => obj.id === formData.tv_plan)
                : null

            const baseTvPlan =
                baseTvPlanInfo?.billing_period?.[0]?.monthly?.api_name || null

            const TvAddons =
                formData?.tv_plan && formData?.tv_optional_plans
                    ? addonsData.tv
                        .filter((obj: any) => formData.tv_optional_plans.includes(obj.id))
                        .map((obj: any) =>
                            obj.billing_period[0].monthly
                                ? obj.billing_period[0].monthly.api_name
                                : obj.billing_period[0].non_recurring.api_name
                        )
                    : []

            const iptvProducts = TvAddons.filter((plan: string) =>
                plan?.includes("IPTV")
            )

            const [totalRecurringPrice, totalDueTodayPrice] = getTotalPrices()

            const requestBody = {
                base_tv_plan_id: baseTvPlan,
                iptv_products_ids: iptvProducts.length
                    ? iptvProducts.join(",").replace(/^monthly_/, "")
                    : "",
                base_home_phone_plan_id: formData?.addons?.join(",") || "",
                optional_addons_ids: formData?.optional_addons?.join(",") || "",
                single_plans:
                    formData?.tv_plan && formData?.tv_optional_single_plans_name?.length
                        ? formData.tv_optional_single_plans_name.join()
                        : "",
                optional_tv_plans:
                    formData?.tv_plan && planData.optional_plans?.length
                        ? planData.optional_plans
                            .filter((p: any) => !("quantity" in p))
                            .map((p: any) => p.id)
                            .join()
                        : "",
                plan_id: planData?.internet_plan_id,
                addons,
                plan_name: planData?.plan_name,
                billing_address: {
                    ...shipping_address,
                    country: "CA",
                    first_name,
                    last_name,
                    email: customer?.email,
                    phone: customer?.phone,
                },
                shipping_address,
                SA_SFRecordID: formData?.SA_SFRecordID || "undefined",
                business: formData?.is_business,
                mailing_address: formData?.is_mailing
                    ? shipping_address
                    : formData?.mailing_address,
                is_mailing: !!formData?.is_mailing,
                token_id: paymentMethod === "card" ? paymentData.token : applePayData,
                referral: referralUrl.join(","),
                is_monthly: tabType,
                termsAndConditions: true,
                is_new_phone: formData?.is_new_phone,
                is_existing_phone: formData?.is_existing_phone,
                is_home_phon_addon: formData?.addons?.includes(
                    "monthly_voip_alianza_res_home_phone"
                )
                    ? 1
                    : 0,
                customer: {
                    first_name,
                    last_name,
                    phone: customer?.phone,
                    cf_company_name: formData?.is_business
                        ? customer?.cf_company_name
                        : "",
                    email: customer?.email,
                    cf_suggested_turn_on_date: customer?.cf_suggested_turn_on_date,
                    port_number: customer?.port_number,
                },
                cartId: uuid || "",
                total_recurring_price: totalRecurringPrice.toString(),
                total_due_today_price: totalDueTodayPrice.toString(),
                payment_method: paymentMethod,
            }

            const response = await fetch(
                `${process.env.GATSBY_LAMBDA}/tokenized-subscription`,
                {
                    method: "POST",
                    body: JSON.stringify(requestBody),
                }
            )

            const result = await response.json()

            if (result?.status === 200) {
                localStorage.setItem("checkoutComplete", "done")
                crudSessionStorage("delete", sessionStorageKeys.CART_ID)
                navigate("/thank-you")
            } else {
                SetError(result?.error || "Payment failed, please try again.")
                setTimeout(() => {
                    window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: "smooth",
                    })
                }, 200)
            }
        } catch (error: any) {
            window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })
            SetError(error?.message || "Payment failed")
        } finally {
            setProcessingPayment(false)
            setLoadingEstimate(false)
        }
    }

    useEffect(() => {
        setProcessingPayment(false)
        if (formData?.plan_id) {
            estimatePrice()
        }
    }, [formData])

    useEffect(() => {
        if (formData?.monthEstimate?.invoice_estimate.amount_due >= 0) {
            postCartData()
        }
    }, [formData?.monthEstimate?.invoice_estimate.amount_due])

    useEffect(() => {
        if (applePayButtonMounted) {
            if (window.innerWidth < 640) {
                setSelectedMethod("apple_pay")
            }
        }
    }, [applePayButtonMounted])

    useEffect(() => {
        const cartUpdateData = async () => {
            if (formData?.plan_id && formData?.isUpdateCart === 1) {
                try {
                    dispatch(setFormData({ isUpdateCart: 2 }))
                } catch (error) {
                    console.error("Error updating cart:", error)
                }
            }
        }

        // Trigger API only if `isUpdateCart` is 1 (ensure we avoid unnecessary calls)
        if (formData?.isUpdateCart === 1) {
            // dispatch(setformData({ isUpdateCart: 0 }))
            cartUpdateData()
        }

        // calculate estimate on initial load while user comes from url
        const urlParams = new URLSearchParams(window.location.search)
        const id = urlParams.get("id")
        id && monthEstimatePrice()
        scrollToTop()
    }, [])

    return (
        <RoundedContainer
            bgColor={colorTheme.MEDIUM_PURPLE}
            className="relative mb-40 lg:mx-14 xl:mx-20 md:p-5"
        >
            <div className="font-satoshi">
                <Helmet>
                    {/* eslint-disable-next-line jsx-a11y/accessible-emoji */}
                    <title>Plan – Purple Cow Internet 💜🐄</title>
                    <link
                        rel="stylesheet"
                        type="text/css"
                        href="https://ws1.postescanada-canadapost.ca/css/addresscomplete-2.30.min.css?key=bw11-fu43-na37-by38"
                    />
                </Helmet>
                {processingPayment ? <LoaderComponent values="Processing" /> : ""}
                <div className="height-100-btn-bottom">
                    <div className="mt-16 md:mx-2 xl:mx-10">
                        <FibreHeading
                            color="white"
                            align="text-left"
                            content={"Checkout"}
                        />
                        <CheckoutBgIcons isCheckoutPage />

                        <div className="md:flex justify-between mt-12">
                            <div className="md:w-[47%]">
                                <ServiceAddressCheckout formData={formData} />

                                <RoundedContainer className="!mx-0 p-3">
                                    <div className="flex items-center justify-between">
                                        <h3 className="font-medium">Contact information</h3>
                                        {!editServiceAddress &&
                                            !editContactInformation &&
                                            !editServices.editServicePopup && (
                                                <div
                                                    onClick={() => setEditContactInformation(true)}
                                                    className="cursor-pointer"
                                                >
                                                    <EditIcon />
                                                </div>
                                            )}
                                    </div>

                                    {editContactInformation ? (
                                        <div>
                                            <ContactComponent
                                                setContactInfoErrors={setContactInfoErrors}
                                                contactInfoErros={contactInfoErros}
                                                isCheckoutPage={true}
                                            />

                                            <div className="flex my-5 justify-center">
                                                <CommonButtonInternet
                                                    onClick={(e) => handleUpdateContactInformation(e)}
                                                    buttonText="Update"
                                                />
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="text-lg text-black">
                                            <p className="break-words">
                                                Name: {formData?.customer?.first_name}{" "}
                                                {formData?.customer?.last_name}
                                            </p>

                                            <p>Cell Phone: {formData?.customer?.phone}</p>
                                            <p className="break-words">
                                                Email: {formData?.customer?.email}
                                            </p>
                                            <p>
                                                Mailing Address: {formData?.mailing_address?.Line1},{" "}
                                                {formData?.mailing_address?.City},{" "}
                                                <span className="whitespace-nowrap">
                                                    {formData?.mailing_address?.Province}{" "}
                                                    {formData?.mailing_address?.PostalCode}
                                                </span>
                                            </p>
                                        </div>
                                    )}
                                </RoundedContainer>

                                <EditSelectedServices
                                    addonsData={addonsData}
                                    editServices={editServices}
                                    setEditServices={setEditServices}
                                    plansData={plansData}
                                    editServiceAddress={editServiceAddress}
                                    editContactInformation={editContactInformation}
                                    postCartData={postCartData}
                                />

                                <RoundedContainer className="!m-0 p-3">
                                    <h3 className="font-medium">Payment method</h3>
                                    <div className="flex flex-col mt-6 space-y-4">
                                        {/* Credit Card */}
                                        <label className="flex items-center space-x-3 sm:space-x-0 cursor-pointer">
                                            <div
                                                className={`h-6 w-6 border-black rounded-full sm:hidden border ${selectedMethod === "card" ? "bg-black" : ""
                                                    } flex items-center justify-center`}
                                                onClick={() => handleChangePaymentMethod("card")}
                                            >
                                                {selectedMethod === "card" && (
                                                    <div className="h-3 w-3 rounded-full bg-white"></div>
                                                )}
                                            </div>

                                            <span className="text-gray-800 gap-2 items-center flex">
                                                <CardIcon /> Card
                                            </span>
                                        </label>

                                        <div
                                            className={
                                                selectedMethod === "card"
                                                    ? "block sm:block" // Show on small if method is 'card', always on larger screens
                                                    : "hidden sm:block" // Hide on small, show on larger
                                            }
                                        >
                                            <CreditCardsIcons />
                                        </div>
                                    </div>
                                    <form>
                                        <div>
                                            <div
                                                className={
                                                    selectedMethod === "card"
                                                        ? "block sm:block" // Show on small if method is 'card', always on larger screens
                                                        : "hidden sm:block" // Hide on small, show on larger
                                                }
                                            >
                                                {isChargebeeLoaded ? (
                                                    <div>
                                                        <div className="my-3">
                                                            <label
                                                                htmlFor="NameonCard"
                                                                className="mb-1 font-normal text-sm"
                                                            >
                                                                Card holder's name
                                                            </label>
                                                            <input
                                                                value={cardHolderName}
                                                                onChange={(e) => {
                                                                    setCardHolderName(e.target.value)
                                                                    setCardErrors({ ...cardErrors, name: "" })
                                                                }}
                                                                placeholder="Full name"
                                                                autoComplete="off"
                                                                className="rounded-lg border-none placeholder:font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm bg-[#F7F7F7] w-100 focus:z-10"
                                                                id="NameonCard"
                                                                type="text"
                                                                required
                                                            />
                                                            <span className="text-red-500">
                                                                {cardErrors?.name}
                                                            </span>
                                                        </div>

                                                        <CreditCardComponent
                                                            cardRef={cardRef}
                                                            cardErrors={cardErrors}
                                                            setCardErrors={setCardErrors}
                                                        />
                                                    </div>
                                                ) : (
                                                    "Loading card..."
                                                )}
                                            </div>

                                            {applePayButtonMounted && (
                                                <div className="border-t border-black w-full my-3 sm:hidden"></div>
                                            )}
                                            {applePayButtonMounted && (
                                                <label className="flex items-center space-x-3 cursor-pointer sm:hidden">
                                                    <div
                                                        className={`h-6 w-6 border-black rounded-full border ${selectedMethod === "apple_pay" ? "bg-black" : ""
                                                            } flex items-center justify-center`}
                                                        onClick={() =>
                                                            handleChangePaymentMethod("apple_pay")
                                                        }
                                                    >
                                                        {selectedMethod === "apple_pay" && (
                                                            <div className="h-3 w-3 rounded-full bg-white"></div>
                                                        )}
                                                    </div>

                                                    {
                                                        <span className="text-gray-800 gap-2 items-center flex">
                                                            <AppleLogo color="black" /> Apple pay
                                                        </span>
                                                    }
                                                </label>
                                            )}
                                            {error && (
                                                <div className="text-center border border-red-500 text-red-500 bg-red-100 p-4 rounded-xl mt-5">
                                                    <strong>Error! </strong>
                                                    {error}
                                                </div>
                                            )}
                                        </div>
                                    </form>
                                </RoundedContainer>
                            </div>

                            <div className="md:w-[47%]">
                                <OrderSummaryCheckout
                                    recurringSubtotal={recurringSubtotal}
                                    loadingEstimate={loadingEstimate}
                                    estimateData={estimateData}
                                    formData={formData}
                                />
                                <div className="flex flex-col sm:flex-col-reverse">
                                    <p className="mt-5 text-white text-lg">
                                        <input
                                            type="checkbox"
                                            checked={acceptedTermsAndConditions}
                                            onChange={() => setAcceptedTermsAndConditions(!acceptedTermsAndConditions)}
                                            className="h-[20px] w-[20px]"
                                            style={{accentColor:colorTheme.GOLD}}
                                        />{" "}
                                        By checking the box and clicking 'Join the herd!', you consent to receive text messages for important notifications, updates on upcoming events, and responses from your dedicated representative. Message frequency may vary. Message and data rates may apply. For support, reply 'HELP' or visit our website. You may opt out at any time by replying 'STOP' to any message. Please review Purple Cow’s{" "}
                                        <a
                                            className="underline"
                                            href="https://purplecowinternet.com/terms-of-use/"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            Terms &amp; Conditions
                                        </a>{" "}
                                        and{" "}
                                        <a
                                            className="underline"
                                            href="https://purplecowinternet.com/privacy-policy/"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            Privacy Policy
                                        </a>{" "}
                                        for more information.
                                    </p>

                                    <div className="fixed sm:static z-30 sm:z-0 bottom-0 px-3 pb-1 left-0 w-full border-[#D9D9D9] border-t sm:border-none sm:p-0 bg-white sm:bg-transparent">
                                        <div>
                                            {addonsData?.misc
                                                ?.filter(
                                                    (misc: any) =>
                                                        misc?.api_name === "neighbor_in_need" &&
                                                        misc?.status === "ACTIVE"
                                                )
                                                ?.map((ele: any, index: number) => {
                                                    return (
                                                        <div key={index}>
                                                            <div className="w-full">
                                                                <div className="mt-1 sm:mt-5">
                                                                    <label
                                                                        htmlFor="need"
                                                                        className="checkbox-form mb-1"
                                                                    >
                                                                        <input
                                                                            type="checkbox"
                                                                            name="checkbox"
                                                                            className={`${disableNeighborinNeed ? "cursor-not-allowed" : ""}`}
                                                                            disabled={disableNeighborinNeed}
                                                                            id="need"
                                                                            value={
                                                                                ele?.billing_period[0]?.monthly
                                                                                    ?.api_name
                                                                            }
                                                                            checked={formData?.optional_addons?.includes(
                                                                                ele?.billing_period[0]?.monthly
                                                                                    ?.api_name
                                                                            )}
                                                                            onChange={async (e) => {
                                                                                const value = String(e.target.value)
                                                                                const current =
                                                                                    formData?.optional_addons || []
                                                                                let updatedAddons: string[]

                                                                                if (e.target.checked) {
                                                                                    updatedAddons = [...current, value] // Add
                                                                                } else {
                                                                                    updatedAddons = current.filter(
                                                                                        (v: any) => v !== value
                                                                                    ) // Remove
                                                                                }

                                                                                dispatch(
                                                                                    setFormData({
                                                                                        optional_addons: updatedAddons,
                                                                                        optionalPlanTrigger: Math.floor(
                                                                                            Math.random() * 1000000000
                                                                                        ),
                                                                                    })
                                                                                )
                                                                            }}
                                                                        />
                                                                        <p className="w-[90%] text-black sm:text-white text-xl">
                                                                            Agree to help a neighbour in need for two
                                                                            dollars a month
                                                                        </p>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )
                                                })}
                                        </div>

                                        <div className="flex gap-1 items-center justify-between sm:justify-center">
                                            <div className="font-medium sm:hidden w-[48%]">
                                                <div className="flex gap-1 justify-between">
                                                    <p>Monthly</p>
                                                    <p>{formatMoney(recurringSubtotal / 100)}</p>
                                                </div>
                                                <div className="flex gap-1 justify-between">
                                                    <p>Order total</p>
                                                    <p>
                                                        {formatMoney(
                                                            estimateData?.invoice_estimate.total / 100
                                                        )}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="flex flex-col justify-center">
                                                <CommonButtonInternet
                                                    disabled={disableJoinTheHerd}
                                                    onClick={() => payPayment("card")}
                                                    className={`${disableJoinTheHerd ? "opacity-35" : ""} whitespace-nowrap ${selectedMethod === "card" ? "block" : "hidden"} sm:block sm:mt-4 py-5 sm:py-4 px-8 sm:px-14 lg:px-16 md:text-lg bg-black`}
                                                    buttonText={
                                                        <div className="flex gap-1 items-center">
                                                            Join the herd!
                                                            <span>{updatingCart ? <LoaderIcon /> : ""}</span>
                                                        </div>
                                                    }
                                                />
                                                <div
                                                    className={`flex w-full justify-center ${selectedMethod === "apple_pay" ? "block" : "hidden"} sm:block`}
                                                >
                                                    {allowUserToCheckoutUsingApplePay && (
                                                        <>
                                                            {applePayButtonMounted &&
                                                                loadingApplePayButton ? (
                                                                <CommonButtonInternet
                                                                    onClick={() => { }}
                                                                    bgColor="black"
                                                                    textColor="white"
                                                                    className="opacity-35 whitespace-nowrap sm:block !px-5 sm:mt-5 rounded-xl"
                                                                    buttonText={
                                                                        <div className="flex gap-1 justify-center items-center text-sm sm:text-base">
                                                                            Check out with <AppleLogo color="white" />
                                                                            Pay
                                                                            {(loadingEstimate || updatingCart) && (
                                                                                <LoaderIcon />
                                                                            )}
                                                                        </div>
                                                                    }
                                                                />
                                                            ) : (
                                                                <ApplePay
                                                                    total={orderTotal}
                                                                    payPayment={payPayment}
                                                                    applePayButtonMounted={applePayButtonMounted}
                                                                    setApplePayButtonMounted={
                                                                        setApplePayButtonMounted
                                                                    }
                                                                />
                                                            )}
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </RoundedContainer>
    )
}
export default CheckoutComponent

