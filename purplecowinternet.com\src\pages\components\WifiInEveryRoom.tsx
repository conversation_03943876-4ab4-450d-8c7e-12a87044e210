import React from "react"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import Wifi from "../../content/WifiInEveryRoom.jpg"
const WifiInEveryRoom: React.FC = () => {
  return (
    <RoundedContainer>
      <div className="flex flex-col-reverse md:flex-row">
        <div className="w-full md:w-[50%]">
          <img src={Wifi} alt="modem" />
        </div>
        <div className="w-full flex flex-col justify-center md:w-[50%]">
          <FibreHeading
            content={"Wifi in Every Room"}
            align="text-left"
            className="text-start"
            color="black"
          />
          <FibreDescription
            align="text-left"
            color="black"
            content={
              "Our Wi-Fi modem, which also plays the role of a router, will impress you with its amazing technical capabilities. Its band- steering function, for example, makes for smooth web surfing on any device."
            }
            className="mt-5 text-md md:text-xl text-start font-normal xl:pr-32"
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default WifiInEveryRoom
