/* front pages css */
.home-parent header {
  position: fixed;
  top: 0px;
  width: 100%;
  z-index: 9;
  background: #ffffff;
  padding: 10px 0px;
}
header.flex.items-center {
  position: fixed;
  top: 0px;
  width: 100%;
  z-index: 9;
  background: #ffffff;
  padding: 0px 0px;
  transition: all 200ms;
}
header.flex.items-center + div {
  padding-top: 60px;
}
.home-parent .new-header-row li a.btn-bg-new,
.new-header-row li a.btn-bg-new {
  color: #ffffff;
}
.new-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 100%;
}
.height-30.gatsby-image-wrapper img {
  height: 70px;
  width: auto;
  object-fit: contain;
}
.new-header-row .self-start.block {
  margin: auto 0px auto 0px;
}
.new-header-row .self-start.block svg,
.mobile-menu .close-menu svg {
  color: #000000;
}
.new-header-row li {
  margin: 0px 10px;
  cursor: pointer;
}
.new-header-row li a {
  color: #343f4b;
  font-weight: 600;
}
.new-header-row li .border-btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #343f4b;
  background: transparent;
  border: 3px solid #343f4b;
}
.mobile-menu {
  position: fixed;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background: #f7f7f7;
  z-index: 999;
  padding-top: 100px;
  text-align: center;
}
.mobile-menu .close-menu {
  color: #343f4b;
  position: absolute;
  align-items: center;
  justify-content: center;
  top: 30px;
  right: 15px;
  font-size: 24px;
  text-align: center;
}
.mobile-menu .close-menu svg {
  width: 24px;
  height: 24px;
  fill: #343f4b;
}

/* footer new */
.footer-new {
  font-family: Arial, sans-serif;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  max-width: 1200px;
  flex-wrap: wrap;
  margin: 0 auto;
}

.footer-column {
  display: flex;
  flex-direction: column;
  align-items: center; /* Aligns items to the left */
  flex: 1;
  padding: 0 20px;
  margin-bottom: auto;
  text-align: left;
}

.footer-column.centered {
  align-items: center; /* Only center items in this column if needed */
  text-align: center;
}

div .footer-column.contact-info p {
  font-size: 14px;
  margin: 0;
  align-items: flex-start;
  text-align: left;
}

div .footer-column.contact-info a {
  font-size: 14px;
  margin: 0;
}

.footer-link {
  color: #ffffff; /* Normal state color */
  font-size: 16px;
  margin: 8px 0;
  text-decoration: none; /* No underline normally */
  transition: all 200ms; /* Smooth transition for hover effect */
}

.footer-link:hover,
.footer-link:focus {
  content: "— "; /* Add a dash before the link on hover */
  color: #cccccc; /* Lighter color on hover */
  transform: translateX(
    5px
  ); /* Move text slightly to the right, but append the pseudo element to the right */
}

.footer-new .footer-a:hover::after {
  width: 10px;
}

.social-media {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  width: 100%;
  margin-bottom: 20px;
}

.social-icon .icon {
  fill: #ffffff;
  width: 32px;
  height: 32px;
}

.footer-reminder {
  text-align: center;
  width: 100%;
  font-size: 14px;
}

@media (max-width: 1024px) {
  .footer-content {
    flex-direction: column;
    align-items: center;
  }

  .footer-column {
    align-items: center; /* Ensure content is centered in mobile views */
  }
}

@media (max-width: 768px) {
  .footer-column {
    padding: 10px 0;
  }
}

.large-view-show {
  display: block;
}

.learn-more-mobile {
  display: none;
  align-items: center;
  justify-content: flex-start;
}
.learn-more-mobile svg {
  font-size: 36px;
  width: 36px;
  height: 36px;
  margin-left: 10px;
}
.learn-more-mobile.show-detail svg {
  transform: rotate(180deg);
}
.tv-section1,
.phone-section1,
.internet-section1 {
  padding: 40px 0px;
  text-align: center;
}
.tv-section1 .h2,
.phone-section1 .h2,
.internet-section1 .h2,
.internet-section2 .h2 {
  font-size: 48px;
  line-height: 60px;
  font-weight: 700;
  color: #343f4b;
  margin: 20px auto 60px auto;
  max-width: 720px;
  text-align: center;
}
.phone-section1 .h2 {
  margin: 20px auto 0px auto;
}

.phone-section2 .selectPlan .selectPlan__box-inner {
  min-height: 340px;
  padding-bottom: 30px;
  position: relative;
}
.phone-section2 .selectPlan .selectPlan__box-inner .a-link-bold {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 8px;
}
.phone-section2 .selectPlan .selectPlan__box-inner .h3::after {
  display: none;
}
.phone-section2 .selectPlan .selectPlan__box-inner .h2 sup {
  font-size: 16px;
}
.phone-section2 .selectPlan .selectPlan__box {
  min-width: 260px;
  max-width: 260px;
  margin-bottom: 0px;
}
.phone-section2 .selectPlan .chennels-box {
  grid-gap: 16px;
}
.phone-section2 .selectPlan .selectPlan__box .plan-list {
  padding-left: 0px;
  text-align: left;
  margin-top: 20px;
}
.phone-section2 .selectPlan .selectPlan__box .plan-list li {
  line-height: 20px;
  margin-top: 10px;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.phone-section2 .selectPlan .selectPlan__box .plan-list li svg {
  width: 18px;
  height: 18px;
  margin-right: 15px;
  stroke: #edb27b;
}
.phone-section2 .selectPlan.addon-plan-1 {
  justify-content: center;
  margin-top: 50px;
}
.phone-section2 .h2,
.tv-section8 .h2,
.tv-section9 .h2,
.tv-section3-new .h2 {
  font-weight: 700;
  font-size: 48px;
  margin: 0px auto 16px auto;
  line-height: 52px;
  color: #343f4b;
}
.phone-section2 .p2.bottom {
  max-width: 610px;
  margin: auto;
  text-align: center;
  font-weight: 400;
}
.internet-section1 .h2 {
  margin-bottom: 0px;
}
.tv-section1 img.tv-banner-img {
  width: 100%;
  max-width: 800px;
  height: auto;
  margin: 30px auto 0px auto;
}
.phone-section1 img.phone-banner-img {
  width: 100%;
}
.internet-section1 img.internet-banner-img {
  width: 100%;
  max-width: 100%;
  height: auto;
  margin: -150px auto -100px auto;
}
.margin-top-reverse {
  margin-top: -150px;
}
.tv-section2 {
  padding: 40px 0px 80px 0px;
}
.tv-section2 .h3 {
  font-size: 36px;
  font-weight: 700;
  color: #343f4b;
}
.tv-section2 .p2 {
  font-size: 16px;
  font-weight: 400;
  color: #343f4b;
  margin: 0px 0px;
}
.tv-section2 .section2-text {
  max-width: 500px;
  margin: auto;
}
.tv-section2 .section2-text .h3 {
  line-height: 40px;
}
.tv-section2 .section2-text .h3.h3-small {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
}
.tv-section2 .section2-text .p2,
.home-section2 .p2,
.home-section2-new .p2,
.home-section3 .p2 {
  line-height: 26px;
  margin: 20px 0px;
}
.tv-section2 .tv-sec2-img {
  margin: auto;
  margin-left: -10px;
  min-width: 590px;
}
.tv-section2 .grid {
  max-width: 1000px;
  margin: auto;
}
.tv-section3,
.home-section3 {
  padding: 80px 0px 50px 0px;
  background: linear-gradient(#e8d7ea, #d7a7e5);
}
.tv-section3 .grid {
  max-width: 1000px;
  margin: auto;
}
.tv-section3 .h2,
.tv-section10 .h2,
.home-section3 .h2 {
  font-weight: 700;
  font-size: 48px;
  margin: 0px auto 30px auto;
  max-width: 605px;
  line-height: 52px;
}
.tv-sec3-icon {
  width: 90px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: contrast(0) brightness(0.5);
  margin: 24px auto;
}
.tv-section4 {
  padding: 80px 0px;
}
.tv-section2.gray-box {
  background: #f7f7f7;
  border-radius: 8px;
  max-width: calc(100% - 20px);
  padding: 80px 15px 40px 15px;
  margin: auto;
}
.tv-section2.gray-box .tv-sec2-img,
.tv-section2.new-sec2 .tv-sec2-img {
  min-width: 300px;
  max-width: 300px;
  margin: auto;
  border-radius: 14px;
}
.tv-sec3-box {
  background: #65226e;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
}
.tv-sec3-box .tv-sec3-icon {
  filter: contrast(0) brightness(5);
  width: 70px;
  height: 70px;
}
.tv-section3-new .h2 {
  margin-bottom: 50px !important;
}
.tv-section3-new .h5 {
  font-size: 22px;
  font-weight: 700;
  color: #343f4b;
  margin: 20px 0px 0px 0px;
}
.tv-section3-new {
  padding: 80px 0px 60px 0px;
  max-width: 1000px;
  margin: auto;
}
.tv-section4 .selectPlan .selectPlan__box-inner {
  min-height: 340px;
  padding-bottom: 30px;
  position: relative;
}
.tv-section4 .selectPlan .selectPlan__box-inner .a-link-bold {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 8px;
}
.tv-section4 .selectPlan .selectPlan__box-inner .h3::after {
  display: none;
}
.tv-section4 .selectPlan .selectPlan__box-inner .h2 sup {
  font-size: 16px;
}
.tv-section4 .selectPlan .selectPlan__box {
  min-width: 260px;
  max-width: 260px;
  margin-bottom: 0px;
}
.tv-section4 .selectPlan .chennels-box {
  grid-gap: 16px;
}
.tv-section4 .selectPlan .selectPlan__box .plan-list {
  padding-left: 0px;
  text-align: left;
  margin-top: 20px;
}
.tv-section4 .selectPlan .selectPlan__box .plan-list li {
  line-height: 20px;
  margin-top: 10px;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.tv-section4 .selectPlan .selectPlan__box .plan-list li svg {
  width: 18px;
  height: 18px;
  margin-right: 15px;
  stroke: #edb27b;
}
.tv-section4 .selectPlan.addon-plan-1 {
  justify-content: center;
  margin-top: 50px;
}
.tv-section4 .h2,
.tv-section8 .h2,
.tv-section9 .h2,
.tv-section3-new .h2 {
  font-weight: 700;
  font-size: 48px;
  margin: 0px auto 16px auto;
  line-height: 52px;
  color: #343f4b;
}
.tv-section4 .p2.bottom {
  max-width: 610px;
  margin: auto;
  text-align: center;
  font-weight: 400;
}
.tv-section5 {
  background: #65226e;
  border-radius: 8px;
  max-width: calc(100% - 20px);
  margin: auto;
  padding: 60px 0px;
}
.tv-section5 .h3 {
  font-weight: 700;
  font-size: 36px;
  margin: 0px auto 40px auto;
}
.tv-section5 .tv-section5-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px 0px;
  padding: 0px 15px;
}
.tv-section5 .tv-section5-row .input1 {
  width: 340px;
}
.tv-section5 .tv-section5-row .input2 {
  width: 140px;
  margin: 0px 10px;
}
.tv-section5 .tv-section5-row .send-btn {
  padding: 14px 15px;
  border-radius: 8px;
  font-weight: 600;
  color: #343f4b;
  background: #ffffff;
  cursor: pointer;
}

.tv-section5 .tv-section5-row .send-btn .send-btn-icon {
  width: 22px;
}
.tv-section6 {
  padding: 120px 0px 50px 0px;
}
.tv-section6 .grid {
  max-width: 1000px;
  margin: auto;
  align-items: center;
}
.tv-section6 .grid .tv-sec6-img {
  position: relative;
  right: -50px;
}
.tv-section6 .grid .section6-text {
  max-width: 300px;
  position: relative;
  text-align: left;
}
.tv-section6 .grid .section6-text .h3,
.tv-section7 .grid .section7-text .h3,
.internet-section3 .internet-section3__right .h3,
.home-section2 .h3,
.home-section2-new .h3 .home-section3 .h3,
.home-section4 .h3 {
  font-weight: 600;
}
.tv-section6 .grid .section6-text .p2,
.tv-section7 .grid .section7-text .p2,
.internet-section3 .internet-section3__right .p2,
.home-section4 .p2 {
  line-height: 24px;
  margin: 20px 0px;
}
.tv-section7 {
  padding: 80px 0px;
  background: #f7f7f7;
  margin-bottom: 50px;
}
.tv-section7 .grid .section7-text {
  max-width: 450px;
  width: 100%;
  position: relative;
  text-align: left;
}
.tv-section7 .grid .section7-right-content img {
  max-width: 400px;
  width: 100%;
  margin: auto;
}
.tv-section8 {
  padding: 40px 0px 90px 0px;
}
.tv-section8 .max-width1000 {
  margin-top: 50px;
}
.tv-section8 .block.relative.w-full {
  border-bottom: 1px solid #47525e;
}
.tv-section8 .block.relative.w-full button svg {
  border-radius: 50%;
  background: #ffffff;
  transition: ease-in-out 0.3s;
}
.tv-section8 .block.relative.w-full button:hover svg {
  background: #e9e9e9;
}
.tv-section8 .block.relative.w-full:last-child {
  border-bottom: none;
}
.tv-section8 .block button {
  font-weight: 600;
  padding-right: 10px;
  text-align: left;
}
.onboarding-section {
  padding: 0px 0px 90px 0px;
}
.onboarding-section .max-width1000 {
  margin-top: 50px;
}
.onboarding-section .block.relative.w-full {
  border-bottom: 1px solid #47525e;
}
.onboarding-section .block.relative.w-full button svg {
  border-radius: 50%;
  background: #ffffff;
  transition: ease-in-out 0.3s;
}
.onboarding-section .block.relative.w-full button:hover svg {
  background: #e9e9e9;
}
.onboarding-section .block.relative.w-full:last-child {
  border-bottom: none;
}
.onboarding-section .block button {
  font-weight: 600;
  padding-right: 10px;
  text-align: left;
}
.tv-section9 .grid .section9-text {
  position: relative;
  height: 475px;
  padding-top: 60px;
  width: 320px;
  margin: auto;
  text-align: left;
}
.tv-section9 .grid .section9-text .h3 {
  font-weight: 600;
}
.tv-section9 .grid .section9-text .p2 {
  margin: 20px 0px 40px 0px;
}
.tv-section9 .tv-sec9-img {
  margin: 30px auto 0px auto;
}
.tv-section10 {
  padding: 50px 10px 60px 10px;
  text-align: center;
  background: #b27ced;
  border-radius: 8px;
  max-width: calc(100% - 20px);
  margin: 0px auto 50px auto;
}
.internet-section2 {
  padding: 20px 0px;
  position: relative;
}
.internet-section2 .container {
  background: #f7f7f7;
  padding: 30px 50px 50px 50px;
  border-radius: 8px;
  max-width: 1110px;
}
.internet-section1 + .internet-section2 .container.mx-auto.px-4.bg-transparent {
  margin-top: -200px;
}
.internet-section2 .grid {
  max-width: 900px;
  margin: auto;
  align-items: flex-start;
}
.internet-section2 .h2,
.internet-section4 .h2 {
  font-size: 48px;
  line-height: 60px;
  font-weight: 700;
  color: #343f4b;
  margin: 20px auto 10px auto;
  max-width: 800px;
  text-align: center;
}

.internet-section2 .gray-round {
  background: #b27bed;
  border-radius: 50%;
  width: 200px;
  height: 200px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 25px;
  margin-top: 40px;
}
.internet-section2 .gray-round span {
  color: white;
  font-size: 50px;
  font-weight: 700;
}
.internet-section2 .gray-round .internet-sec2-icon {
  width: 150px;
  height: 150px;
  object-fit: contain;
  filter: contrast(0) brightness(5);
}
.internet-section2 .h4 {
  font-size: 30px;
  line-height: 34px;
  font-weight: 600;
  margin: 20px 0px;
  text-align: center;
}
.internet-section3 {
  padding: 80px 0px;
}
.internet-section3 .grid {
  max-width: 1000px;
  margin: auto;
  align-items: center;
}
.internet-section3 .grid .internet-section3__left {
  max-width: 320px;
  margin-left: 80px;
}
.internet-section3 .google-rating {
  width: 220px;
  height: auto;
  object-fit: contain;
}
.internet-section4 {
  padding: 80px 0px;
  background: #f7f7f7;
  border-radius: 8px;
}
.internet-section4 .max-width1000 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow: auto;
}
.internet-section4 .internet-section4__box {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  min-width: 300px;
}
.internet-section4 .internet-section4__box .h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0px 0px 10px 0px;
}
.internet-section4 .internet-section4__box .star-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0px;
}
.internet-section4 .internet-section4__box .star-row .star-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  margin: 0px 3px;
}
.home-section1 {
  background: url("../../static/images/home-bg.png") no-repeat;
  background-size: cover;
  background-position: bottom;
  padding: 0px 0px 80px 0px;
  position: relative;
  min-height: 105vh;
  display: flex;
  align-items: center;
}

.home-section1 .relative {
  position: relative;
  max-width: 500px;
  padding-left: 0px;
  z-index: 5;
}
.home-section1 .h2,
.home-section2 .h2 {
  font-weight: 700;
  font-size: 68px;
  line-height: 68px;
  max-width: 800px;
}
.home-section2 .h2 {
  color: #343f4b;
}
.home-section2-new .h2 {
  color: #343f4b;
  font-weight: 700;
  font-size: 48px;
  line-height: 60px;
}
.home-section2-new .grid.grid-cols-1 {
  align-items: center;
  align-content: center;
  justify-content: space-around;
}

.home-section1 .cow-right-bottom {
  position: absolute;
  right: 0px;
  bottom: 60px;
  height: 96vh;
  width: auto;
  object-fit: contain;
}
.home-section2,
.home-section4 {
  padding: 80px 0px 0px 0px;
}
.home-section2 .grid {
  align-items: center;
}
.home-section2 .home-sec2-img {
  width: 100%;
  height: auto;
  object-fit: scale-down;
}
.tv-section1 .h2.mb-2 {
  margin-bottom: 8px;
}
.home-section3 .phone-stand {
  height: 500px;
  margin: 40px auto 0px auto;
}
.home-sec3-content,
.home-sec4-content {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
}
.max-width1000 {
  max-width: 1000px;
  margin: auto;
}
.border-btn {
  border: 2px solid #343f4b;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 600;
  color: #47525e;
  cursor: pointer;
  position: relative;
  min-width: 160px;
  text-align: center;
  display: inline-block;
}
.btn-bg-white {
  background-color: #ffffff;
  cursor: pointer;
  color: #47525e;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 600;
  display: inline-block;
}
.contact-us-parent {
  position: fixed;
  right: 15px;
  bottom: 12px;
}
.contact-us-parent .contact-click-box {
  cursor: pointer;
}
.bubble-icon {
  width: 80px;
  height: 80px;
  object-fit: contain;
}
.message-box {
  width: 400px;
  background: #ffffff;
  overflow: hidden;
}
.header-gray {
  background: #e9e9e9;
  padding: 0px 0px;
  height: 40px;
  width: 100%;
  display: flex;
  align-items: flex-start;
}
.contact-us-box-content {
  padding: 20px;
  position: relative;
  max-height: 300px;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-color: #f1f1f1 transparent;
  scrollbar-width: thin;
}
.contact-us-box-content::-webkit-scrollbar,
.message-box-footer .message-input textarea::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: transparent;
  border-radius: 2px;
}
.contact-us-box-content::-webkit-scrollbar-thumb,
.message-box-footer .message-input textarea::-webkit-scrollbar-thumb {
  background-color: #f1f1f1;
  border-radius: 2px;
}
.contact-us-box-content::-webkit-scrollbar-track,
.message-box-footer .message-input textarea::-webkit-scrollbar-track {
  border: 0.0625rem solid transparent;
  border-radius: 2px;
}

.message-left {
  background: #f7f7f7;
  color: #47525e;
  border-radius: 20px;
  font-size: 14px;
  padding: 4px 10px;
  display: inline-block;
  margin: 5px 0px 5px auto;
  max-width: calc(100% - 50px);
}
.message-right {
  background: #1c86fc;
  color: #ffffff;
  border-radius: 20px;
  font-size: 14px;
  padding: 4px 10px;
  display: inline-block;
  margin: 5px auto 5px 0px;
  max-width: calc(100% - 50px);
}
.message-loading {
  border-radius: 50%;
}
.message-box-footer {
  padding: 20px;
  background: #ffffff;
}
.message-box-footer .message-input {
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 10px;
}
.message-box-footer .message-input input,
.message-box-footer .message-input textarea {
  padding: 10px 40px 10px 20px;
  border-radius: 24px;
  width: 100%;
  resize: none;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-color: #f1f1f1 transparent;
  scrollbar-width: thin;
}
.message-box-footer svg,
.message-box-footer .send-btn {
  position: absolute;
  right: 10px;
  top: 0px;
  bottom: 0px;
  margin: auto;
  cursor: pointer;
  color: #969faa;
}
.message-box-footer .send-btn svg {
  top: 0px;
  right: 0px;
}
.message-box-footer svg {
  transform: scale(1.5);
}
.message-box-footer .message-input input.filled-input ~ svg,
svg {
  color: #1c86fc;
}
.active-msg-btn {
  color: #1c86fc !important;
}
.btn-bg-new {
  background-color: #edb27b;
  cursor: pointer;
  color: #ffffff;
  border-radius: 4px;
  padding: 12px 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
}
.btn-bg-new .right-arrow {
  margin-left: 10px;
  width: 12px;
  height: 12px;
  filter: contrast(0) brightness(5);
}
.tab-type.new-tab {
  background: transparent;
  border: 1px solid #edb27b;
}
.tab-type.new-tab .tab-type__option {
  color: #edb27b;
}

.tab-type.new-tab .tab-type__option.active {
  color: #ffffff;
  background-color: #edb27b;
  border: 1px solid #edb27b;
}

.face-smile-icon {
  display: unset !important;
  max-width: 20px;
}
.bg-transparent {
  background: transparent !important;
}
.delete-price {
  font-size: 18px;
  color: #47525e;
}
.red_error {
  color: red;
}
.home-top-feild {
  display: flex;
}
.home-top-feild .input1 {
  width: 100%;
}
.home-top-feild .max-width80 {
  max-width: 160px;
  margin-left: 16px;
}
.blog-section {
  padding: 100px 0px;
  position: relative;
}
.end-blog {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: #000000;
  margin: 40px auto;
}
.end-blog::after {
  content: "";
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: #000000;
  top: 0px;
  right: -30px;
}

.end-blog::before {
  content: "";
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: #000000;
  top: 0px;
  left: -30px;
}
.font-12 {
  font-size: 12px;
}
