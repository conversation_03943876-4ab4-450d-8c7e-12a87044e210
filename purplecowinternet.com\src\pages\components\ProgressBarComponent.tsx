import React from "react"
import { PageStep } from "../../redux/formSlice"
import { colorTheme } from "../../data/SrtingConstants"

interface ProgressBarComponentProps {
  status: PageStep
}

const getProgressPercent = (status: PageStep): number => {
  switch (status?.toLowerCase()) {
    case "tvplan":
      return 25
    case "homephoneplan":
      return 50
    case "contactinformation":
      return 75
    default:
      return 0
  }
}

const ProgressBarComponent: React.FC<ProgressBarComponentProps> = ({
  status,
}) => {
  const percent = getProgressPercent(status)

  return (
    <div
      style={{
        width: "100%",
        height: "20px",
        overflow: "hidden",
      }}
      className="rounded-3xl"
      title={`Progress ${percent}%`}
    >
      <div
        style={{
          width: `${percent}%`,
          backgroundColor: colorTheme.DARK_PURPLE,
          height: "100%",
          borderRadius: "999px",
          transition: "width 0.3s ease-in-out",
        }}
      />
    </div>
  )
}

export default ProgressBarComponent
