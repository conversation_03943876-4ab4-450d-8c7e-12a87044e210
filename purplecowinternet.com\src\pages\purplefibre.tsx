import React from "react"
import Layout from "../components/Layout"
import NovaScotiasFastestPureFibreInternet from "./components/NovaScotiasFastestPureFibreInternet"
import WhyPurpleFibre from "./components/WhyPurpleFibre"
import FibreNetwork from "./components/FibreNetwork"
import FasterThanBigGuys from "./components/FasterThanBigGuys"
import RatedMostReliableInternet from "./components/RatedMostReliableInternet"
import WifiThatGetsToEveryRoom from "./components/WifiThatGetsToEveryRoom"
import InTheNewsPurpleFibre from "./components/InTheNewsPurpleFibre"
import CommonQuestions from "../components/CommonQuestions"
import { purpleFibreQuestions } from "../data/CommonQuestionsData"
import RoundedContainer from "../components/RoundedContainer"
import { FibreHeading } from "../components/FibreHeading"
import { colorTheme } from "../data/SrtingConstants"
import CommonButtonInternet from "../components/CommonButton"
import { navigate } from "gatsby"
import PricingPurpleFibre from "./components/PricingPurpleFibre"
import HowToGetStarted from "./components/HowToGetStartedFibre"

const PurpleFIbre = () => {
  return (
    <Layout>
      <div className="mt-5 mx-2 sm:mx-5">
        <NovaScotiasFastestPureFibreInternet />
        <WhyPurpleFibre />
        <FibreNetwork />
        <FasterThanBigGuys />
        <RatedMostReliableInternet />
        <WifiThatGetsToEveryRoom />
        <PricingPurpleFibre />
        <HowToGetStarted />
        <InTheNewsPurpleFibre />
        <CommonQuestions questions={purpleFibreQuestions} />
        <RoundedContainer bgColor="white">
          <div className="flex my-10 flex-col gap-20 items-center">
            <FibreHeading
              align="text-center"
              color={colorTheme.MEDIUM_PURPLE}
              content={"Join the herd today"}
            />
            <CommonButtonInternet
              buttonText={<>Check availability &gt; </>}
              textColor="white"
              bgColor={colorTheme.MEDIUM_PURPLE}
              onClick={() => navigate("/join-the-herd")}
              className="py-4"
            />
          </div>
        </RoundedContainer>
      </div>
    </Layout>
  )
}

export default PurpleFIbre
