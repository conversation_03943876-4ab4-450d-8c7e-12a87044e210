import React, { useState, useEffect } from "react"
import Layout from "../components/Layout"
import { Helmet } from "react-helmet"
import { navigate } from "gatsby"
import MessageModal from "./components/common/MessageModal"
import Thefamiliarity from "../content/Thefamiliarity.png"
import BringYourOwnDevice from "../content/BringYourOwnDevice.png"
import axios from "axios"
import { colorTheme } from "../data/SrtingConstants"
import RoundedContainer from "../components/RoundedContainer"
import CommonButtonInternet from "../components/CommonButton"
import CommonQuestions from "../components/CommonQuestions"
import { tvQuestions } from "../data/CommonQuestionsData"
import { FibreHeading } from "../components/FibreHeading"
import SplashImg from "../content/Splash_internet.png"
import PcPeace from "../content/pc_peace.png"
import DancingCow from "../content/Dancing_Cow.gif"
import RecordLiveTv from "../content/RecordLiveTv.png"
import PauseLiveTv from "../content/PauseLiveTv.png"
import UseExistingDevice from "../content/UseExistingDevice.png"
import PurpleCowTv from "../content/PurpleCowTv.png"
import FirestickTv from "../content/FireTvStick.png"
import AppleTv from "../content/AppleTv.png"
import GoogleTv from "../content/GoogleTv.png"

const FrontTVPage = () => {
  const ignoreList = ["Add 50hrs of DVR", "Pick 5 Channels", "Single Channels"]
  const [showModal, setShowModal] = useState(false)
  const [messagePopup, setMessagePopup] = React.useState(false)
  const [includedChannles, setIncludedChannles] = React.useState([])

  const getChannelImageUrls = (channels: any) => {
    setIncludedChannles(
      channels
        ?.filter((obj: any) => obj.status === "ACTIVE")
        ?.map((obj: any) => obj.image_url)
    )
    setShowModal(true)
  }

  const [tvPlans, setTvPlans] = useState<any>([])
  const [addons, setAddons] = useState<any>([])
  const features = [
    {
      img: RecordLiveTv,
      label: "Record Live TV",
      description: "Record Live TV and store in the cloud",
    },
    {
      img: PauseLiveTv,
      label: "Pause Live TV",
      description: "Pause and Replay across all devices",
    },
    {
      img: UseExistingDevice,
      label: "Use Existing Device",
      description: "No need for a clunky box in each room",
    },
  ]

  const FireStickOptions = [
    {
      logo: FirestickTv,
    },
    {
      logo: AppleTv,
    },
    {
      logo: GoogleTv,
    },
  ]
  useEffect(() => {
    const fetchData = async () => {
      try {
        const plansResponse = await axios(
          process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
            "/snapshots/latest/services/tv/plans.json"
        )
        const addonsResponse = await axios(
          process.env.GATSBY_PRODUCT_CATALOG_S3_BUCKET +
            "/snapshots/latest/services/tv/addons.json"
        )
        setTvPlans(plansResponse.data)
        setAddons(addonsResponse.data)
      } catch (err) {
        console.error("Failed to fetch internet plans:", err)
      }
    }
    fetchData()
  }, [])

  return (
    <Layout>
      <Helmet>
        <title>The best TV app with all the channels 💜🐄</title>
      </Helmet>
      {/* Same Channels as The Big Guys */}
      <RoundedContainer
        className="mt-5 relative"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <img
          src={DancingCow}
          className="w-32 mt-2 ml-2 md:w-40 lg:w-56 sm:ml-5 sm:mt-5"
          alt="Dancing cow"
        />
        <div className="sm:p-1">
          <div className="flex flex-col justify-center items-center">
            <FibreHeading
              align="text-center"
              color="white"
              content={
                <div className="leading-tight flex flex-col items-center gap-4 text-center">
                  <div className="relative font-anton">
                    {/* Text content */}
                    <div className="relative !font-anton z-20">
                      Same Channels as The <br className="hidden md:block" />{" "}
                      Big Guys
                    </div>

                    {/* Background image */}
                    <img
                      src={SplashImg}
                      alt=""
                      className="absolute top-0 left-10 md:left-0 z-10 -translate-x-2"
                    />
                  </div>
                </div>
              }
            />

            <h3 className="!font-sans text-white text-center mt-5 z-20 sm:mb-10 md:mb-20 lg:mb-32">
              Only less expensive ;)
            </h3>
            <img
              src={PurpleCowTv}
              alt="Purple Cow Tv"
              className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 z-10 "
            />
          </div>
        </div>
        <div className="flex justify-end">
          <img
            src={PcPeace}
            className="w-20 md:w-24 mt-2 mb-1 sm:mt-0 mr-5 invisible md:visible"
            alt="Peace"
          />
        </div>
      </RoundedContainer>

      {/* Simple, Packaging, Add additional packs, Add Single Channels  */}
      <RoundedContainer className="flex flex-col items-center">
        <h1 className="!font-anton text-center uppercase mt-32 sm:mt-40 md:mt-52 lg:mt-64">
          Simple, Packaging
        </h1>
        <h3 className="!font-sans text-center mt-5">
          At industry leading price!
        </h3>

        <div className="flex flex-wrap justify-center gap-6 mt-10">
          {tvPlans
            ?.filter((plan: any) => plan?.status === "ACTIVE")
            .map((tvPlan: any, i: number) => {
              return (
                <div
                  key={i}
                  className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
                >
                  <div>
                    <h2 className="!font-anton mb-2">{tvPlan?.name || ""}</h2>
                    <div className="flex my-5">
                      <h1 className="!font-anton">
                        ${tvPlan?.billing_period[0]?.monthly?.price || 0}
                      </h1>
                      <p className="text-lg flex items-end ml-2">/month</p>
                    </div>

                    <div className="grid grid-cols-3 gap-2 mb-5">
                      {tvPlan?.featured_channels?.map(
                        (channel: any, index: number) => (
                          <img
                            key={index}
                            src={channel?.image_url}
                            alt={channel?.name || "Channel"}
                            className={`w-[50px] h-[50px] object-contain`}
                          />
                        )
                      )}
                    </div>
                    <p className="my-4 font-sans text-[#606060]">
                      {tvPlan?.description}
                    </p>
                  </div>
                  <div className="flex justify-center">
                    <CommonButtonInternet
                      className={`!w-full`}
                      buttonText={<>View included Channels</>}
                      onClick={() =>
                        getChannelImageUrls(tvPlan?.included_channels)
                      }
                    />
                  </div>
                </div>
              )
            })}
        </div>

        <h1 className="!font-anton text-center uppercase mt-20">
          Add additional packs
        </h1>

        <div className="flex flex-wrap justify-center gap-6 my-10">
          {addons
            ?.filter(
              (addon: any) =>
                !ignoreList.includes(addon.name) && addon.status === "ACTIVE"
            )
            ?.map((additionalPackage: any, i: number) => {
              return (
                <div
                  key={i}
                  className="bg-white border-4 h-[400px] border-[#d9d9d9] rounded-3xl shadow-lg w-80 p-6 flex flex-col justify-between"
                >
                  <div>
                    <h2 className="!font-anton mb-2">
                      {additionalPackage?.name || ""}
                    </h2>
                    <div className="flex my-5">
                      <h1 className="!font-anton">
                        $
                        {additionalPackage?.billing_period[0]?.monthly?.price ||
                          0}
                      </h1>
                      <p className="text-lg flex items-end ml-2">/month</p>
                    </div>

                    <div className="grid grid-cols-3 gap-2 mb-5">
                      {additionalPackage?.included_channels?.map(
                        (channel: any, index: number) => (
                          <img
                            key={index}
                            src={channel.image_url}
                            alt={channel.name || "Channel"}
                            className={`w-[60px] h-[60px] object-contain`}
                          />
                        )
                      )}
                    </div>

                    <p className="my-4 font-sans text-[#606060]">
                      {additionalPackage?.description}
                    </p>
                  </div>
                </div>
              )
            })}
        </div>

        <h1 className="!font-anton text-center uppercase mt-10">
          Add Single Channels
        </h1>
        <h3 className="!font-sans text-center mt-5 md:w-[60%]">
          Add any additional channels for $4 each or 5 you want for only $15.
          This can be added after checkout with the team.
        </h3>

        <div className="md:mx-10">
          {addons
            ?.filter((addon: any) => addon.name === "Single Channels")
            ?.map((addon: any, addonIndex: number) => (
              <div
                key={addonIndex}
                className="flex flex-wrap justify-center mt-10 gap-2"
              >
                {addon.included_channels
                  .filter((channel: any) => channel.status === "ACTIVE")
                  .map((channel: any, i: number) => (
                    <div
                      key={i}
                      className="w-[65px] h-[45px] sm:w-[85px] sm:h-[47px] lg:w-[140px] lg:h-[80px] bg-white shadow-lg scale-95 hover:scale-100 hover:shadow-purple-300 rounded-2xl border border-[#DDDDDD] flex items-center justify-center"
                    >
                      <img
                        src={channel.image_url}
                        alt={channel.name || "channel"}
                        className="max-h-[60%] max-w-[80%] object-contain"
                      />
                    </div>
                  ))}
              </div>
            ))}
        </div>

        <h3 className="!font-sans text-center my-10 md:w-[60%]">
          Not seeing a channel you want? Let us know and we will see if we can
          get it for you!
        </h3>
        <CommonButtonInternet
          buttonText={"Check My Eligibility"}
          onClick={() => navigate("/join-the-herd")}
          className="px-4"
        />
      </RoundedContainer>

      {/* The familiarity you're used to, */}
      <RoundedContainer>
        <div className="flex flex-col lg:flex-row m-2 sm:m-8 md:m-14 lg:m-20">
          <div className="w-full flex flex-col justify-center lg:w-[50%]">
            <h1 className="!font-anton uppercase mt-5">
              The familiarity you're used to, with the ability to watch TV on
              most of your existing devices in any room.
            </h1>
            <h3 className="!font-sans my-5">
              We built our TV app from the ground up to make it super simple to
              use and setup. Quite simply just download the Purple Cow TV app,
              put in your password and you're watching TV.
            </h3>
          </div>
          <div className="hidden w-full sm:flex justify-center items-center lg:w-[50%]">
            <img
              className="w-80 hover:scale-105 transition-all duration-150 rounded-3xl"
              src={Thefamiliarity}
              alt="modem"
            />
          </div>
        </div>
      </RoundedContainer>

      {/* Bring Your Own Device */}
      <RoundedContainer className="flex flex-col items-center">
        <div className="flex flex-col lg:flex-row mt-10">
          <div className="w-full flex justify-center items-center lg:w-[50%]">
            <img
              className="transition-all duration-150 sm:w-[40%] lg:w-[60%] hover:scale-105"
              src={BringYourOwnDevice}
              alt="modem"
            />
          </div>
          <div className="w-full flex flex-col justify-center lg:w-[50%]">
            <h1 className="!font-anton uppercase mt-5">
              Bring Your Own Device
            </h1>
            <h3 className="!font-sans text-[#111111] mt-5">
              Download the Purple Cow TV app on your compatible devices. Can
              register up to 15 devices on your TV account, and watch up to 5
              simultaneously for one low price!!
            </h3>
            <h3 className="!font-sans text-[#111111] mt-5">
              Downloadable on any Apple, Android, or firestick device and soon
              just in your computer browser.
            </h3>

            <div className="flex gap-4 mt-5">
              {FireStickOptions?.map((ele, i) => {
                return (
                  <div
                    className="rounded-xl shadow-md transition-all duration-200 hover:shadow-purple-400 hover:scale-105 flex justify-center items-center p-2"
                    key={i}
                  >
                    <img
                      className="w-16 object-contain "
                      src={ele.logo}
                      alt=""
                      key={i}
                    />
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </RoundedContainer>

      {/* All Your Channels and Features */}
      <RoundedContainer className="flex flex-col items-center">
        <h1 className="!font-anton text-center uppercase mt-20">
          All Your Channels and Features
        </h1>
        <div className="flex flex-wrap justify-center gap-4 mt-14">
          {features.map((feature, i: number) => {
            return (
              <div
                key={i}
                className="flex flex-col items-center gap-5 py-7 px-10 border border-[#D9D9D9CC] shadow-lg hover:shadow-purple-400 hover:scale-105 transition-all duration-200 rounded-2xl w-80 sm:w-96 h-64"
              >
                <img src={feature.img} alt="" />
                <h3 className="!font-sans font-medium text-center">
                  {feature.label}
                </h3>
                <h4 className="!font-sans text-[#4D4D4D] text-center">
                  {feature.description}
                </h4>
              </div>
            )
          })}
        </div>
        <CommonButtonInternet
          buttonText={"Check My Eligibility"}
          onClick={() => navigate("/join-the-herd")}
          className="px-4 my-10"
        />
      </RoundedContainer>

      {/* Common Questions */}
      <CommonQuestions
        bgColor="white"
        textColor="black"
        questions={tvQuestions}
      />
      {/* Or If You're Ready... */}
      <RoundedContainer
        className="flex flex-col justify-center items-center gap-5 sm:gap-10 p-5 sm:p-10 md:p-20"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <h1 className="!font-anton text-center text-white uppercase">
          Or If You're Ready...
        </h1>
        <h3 className="!font-sans text-white text-center mt-5 lg:w-[60%]">
          If you still have a question, shoot us a message and our team will
          respond within mins
        </h3>

        <div className="flex justify-between gap-6">
          <CommonButtonInternet
            buttonText="Message Us"
            onClick={(e) => {
              const isMobile = /iPhone|iPad|iPod|Android/i.test(
                navigator.userAgent
              )
              if (isMobile) {
                e.preventDefault() // Prevent the default anchor click behavior
                window.location.href =
                  "sms:**************?body=Hey%20Purple%20Cow%20I%20have%20a%20question.%20"
              } else {
                // Open the existing popup on other devices
                setMessagePopup(true)
              }
            }}
            className="px-5 !text-white border border-white"
            bgColor="transparent"
          />
          <CommonButtonInternet
            buttonText="Join the Herd"
            onClick={() => navigate("/join-the-herd")}
            className="px-5"
          />
        </div>
      </RoundedContainer>
      {/* Message Us popup */}
      {messagePopup && <MessageModal closepopup={setMessagePopup} />}

      {/* Included Channels popup */}
      {showModal && (
        <>
          <div className="justify-center items-center flex overflow-x-hidden overflow-y-auto fixed inset-0 z-50 outline-none focus:outline-none">
            <div className="relative w-auto my-6 mx-auto max-w-3xl">
              {/*content*/}
              <div className="border-0 rounded-lg shadow-lg relative flex flex-col w-full bg-white outline-none focus:outline-none">
                {/*header*/}
                <button
                  className="ml-auto mt-0 bg-transparent border-0 text-white leading-none outline-none focus:outline-none"
                  onClick={() => setShowModal(false)}
                >
                  <span className="bg-transparent opacity-25 text-black h-6 w-6 text-4xl block outline-none focus:outline-none mr-2">
                    ×
                  </span>
                </button>
                <div className="text-center">
                  <h4 className="text-1xl text-gray-600">Included Channels</h4>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="#47525E"
                    className="w-6 h-6 m-auto cursor-pointer"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19.5 8.25l-7.5 7.5-7.5-7.5"
                    />
                  </svg>
                </div>
                {/*body*/}
                <div className="relative p-6 chennel-modal-parent">
                  <div className="chennels-box">
                    {includedChannles.map((item, index) => {
                      return (
                        <div key={index}>
                          <div className="chennels-box__icon" key={index + "s"}>
                            <img src={item}></img>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="opacity-25 fixed inset-0 z-40 bg-black"></div>
        </>
      )}
    </Layout>
  )
}

export default FrontTVPage
