import React, { useEffect, useRef } from "react"
import { navigate } from "gatsby"
import { Helmet } from "react-helmet"
import Layout from "../components/Layout"
import RoundedContainer from "../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../components/FibreHeading"
import { colorTheme } from "../data/SrtingConstants"
import { CheckIcon } from "../icons/Icons"
import Circle from "../content/CircleSteps.png"
import { useDispatch, useSelector } from "react-redux"
import { defaultFormData, setFormData } from "../redux/formSlice"
import { crudSessionStorage, sessionStorageKeys } from "../sessionStorage"

export default function Thankyou() {
  const addressType = useSelector((state: any) => state.form.addressType)
  const dispatch = useDispatch()
  useEffect(() => {
    let page: any = localStorage.getItem("checkoutComplete")
    if (page != "done") {
      navigate("/")
      dispatch(setFormData(defaultFormData()))
      crudSessionStorage("delete", sessionStorageKeys?.CART_ID)
    } else {
      if (typeof window.fbq === "function") {
        window.fbq("track", "Purchase", { currency: "CAD", value: 16.0 })
      }
      localStorage.removeItem("checkoutComplete")
    }
  }, [])

  const hasMounted = useRef(false)

  const handleExit = () => {
    dispatch(setFormData(defaultFormData()))
    crudSessionStorage("delete", sessionStorageKeys?.CART_ID)
  }

  // dont combine useEffect
  useEffect(() => {
    hasMounted.current = true

    const handleBeforeUnload = () => {
      handleExit() // On refresh or tab close
    }

    window.addEventListener("beforeunload", handleBeforeUnload)

    // Cleanup: both for unload and component unmount (Gatsby navigation)
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload)

      if (hasMounted.current) {
        handleExit() // On navigation (component unmount)
      }
    }
  }, [])

  const portalFeatures = [
    "Add, change or remove products on your account",
    "Update billing information",
    "See your referrals",
    "And much more!",
  ]

  const eastLinkSteps = [
    "We probably just sent you a text to confirm a few things.",
    "We get everything setup on our end and will let you know when we are on our way to drop off the modem.",
    "Plug it in and everything should be good to go.",
  ]

  const purpleFibreSteps = [
    "We've just sent you a text, please review and select an appointment time that works for you.",
    "We will remind you as your appointment date approaches.",
    "Our awesome team will visit your home and get your home and devices connected to Purple Fibre.",
  ]

  const nextSeps =
    addressType === "PurpleCow" ? purpleFibreSteps : eastLinkSteps

  return (
    <Layout>
      {process.env.GATSBY_ENVIRONMENT === "PROD" ? (
        <Helmet>
          {/* eslint-disable-next-line jsx-a11y/accessible-emoji */}
          <title>Thank You 💜🐄</title>
          <script>{`
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window,document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init',${process.env.GATSBY_FACEBOOK_PIXEL_ID}); 
        `}</script>
          <noscript>{`
          <img height="1" width="1" style="display:none"
          src="https://www.facebook.com/tr?id=${process.env.GATSBY_FACEBOOK_PIXEL_ID}&ev=PageView&noscript=1"/>
        `}</noscript>
        </Helmet>
      ) : (
        <Helmet>
          {/* eslint-disable-next-line jsx-a11y/accessible-emoji */}
          <title>Thank You 💜🐄</title>
        </Helmet>
      )}
      <RoundedContainer className="mt-4" bgColor={colorTheme.MEDIUM_PURPLE}>
        <div className="m-2 sm:m-10">
          <FibreHeading
            className="text-start md:w-[70%]"
            align="text-left"
            color="white"
            content={"Thanks for Joining the herd"}
          />
          <FibreDescription
            content={
              addressType === "PurpleCow"
                ? "You are going to love Purple Fibre! It is the best internet in the province and we are so excited you’re going to get to try it out!!"
                : "You should have just received an email with a link to be able to log into your customer portal. In the portal you can control almost everything including:"
            }
            color="white"
            align="text-left"
            className="text-start mt-5 sm:my-10"
          />

          {addressType === "Eastlink" && (
            <ul>
              {portalFeatures.map((feature, i: number) => {
                return (
                  <li key={i}>
                    <span className="flex gap-2 mt-7">
                      <div className="w-9 h-9 relative bg-white rounded-full cursor-pointer flex items-center justify-center">
                        <CheckIcon color={colorTheme.MEDIUM_PURPLE} />
                      </div>
                      <h3 className="text-white font-medium font-sans w-[90%]">
                        {feature}
                      </h3>
                    </span>
                  </li>
                )
              })}
            </ul>
          )}
        </div>
      </RoundedContainer>

      <RoundedContainer className="!p-0">
        <iframe
          className="rounded-3xl w-full"
          height="630"
          src="https://www.youtube.com/embed/RJb3aDMrScs?si=fz1KkMZC3nYY-OGx"
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerPolicy="strict-origin-when-cross-origin"
          allowFullScreen
        ></iframe>
      </RoundedContainer>

      <RoundedContainer>
        <FibreHeading
          color="black"
          align="text-center"
          content={"Next Steps"}
          className="my-10"
        />
        <ul>
          {nextSeps.map((step, i: number) => {
            return (
              <li key={i}>
                <span className="flex flex-col sm:flex-row items-center gap-5 mt-5">
                  <div
                    className="w-48 h-48 sm:w-56 sm:h-56 bg-no-repeat bg-center bg-contain flex items-center justify-center shrink-0"
                    style={{ backgroundImage: `url(${Circle})` }}
                  >
                    <h1 className="font-sans font-bold text-8xl">{i + 1}</h1>
                  </div>
                  <h3 className="font-sans w-[70%] font-bold text-center sm:text-left">
                    {step}
                  </h3>
                </span>
              </li>
            )
          })}
        </ul>
      </RoundedContainer>
    </Layout>
  )
}
