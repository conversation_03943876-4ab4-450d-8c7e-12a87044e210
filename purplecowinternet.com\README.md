# README

## Getting Started Run locally

Make sure you have Node and NPM installed,

Node.js v20.x.x is required. Ensure that you are using exactly version 20, neither above nor below.

1. Clone the git repo

`git clone https://github.com/Purple-Cow-Internet/purplecowinternet.com.git`

2. Go to the directory

`cd purplecowinternet.com`

3. If making changes, switch to your branch using git checkout <branch-name>.

4. Run `npm install` (to install packages)

5. Create a .env.development file in the root of your project directory.

`touch .env.development`

6. Add the following environment variables to the .env.development file:

```
   GATSBY_LAMBDA=<lambda_url>
   GATSBY_CHARGEBEE_SITE=<chargebee_name>
   GATSBY_CHARGEBEE_API_KEY=<chargebee_publishable_key>
   GATSBY_CART_KEY=<encryption_key>
   GATSBY_CART_IV=<encryption_iv>
   GATSBY_FACEBOOK_PIXEL_ID=<your_facebook_pixel_id>
   GATSBY_PRODUCT_CATALOG_S3_BUCKET=<s3_bucket_name>
   GATSBY_ENVIRONMENT=<your_environment> (DEV or PROD)
```

7. Run `npm run develop` or `gatsby develop -H 127.0.0.1 -p 8000` to start the development server.

8. Go to your browser and visit (http://localhost:8000)

## Building

To see the production build use:

npm run build

npm run serve

```

This will start the server on [localhost:9000]

## Testing

The e2e tests should be run from staging as that environment is identical to production except:

- The API keys are different (for the server and client)
- The emails intended for salesforce are sent to eastlink

To run them, first deploy the client and lambdas to staging. Next run the tests:

```

npm run test:e2e:staging

````

## Deploying

Our website is automatically deployed using a CI/CD pipeline. This means that any time changes are pushed to the master branch, those changes are automatically tested and deployed to our live website.

This is what happens:

- Push to Master: When your changes are ready, push your commits to the master branch. This action will automatically trigger the CI/CD pipeline.
- CI/CD Pipeline: Our CI/CD tool will then take over, automatically testing and building the application before deploying it to the live site.
- Production Site: If all steps in the pipeline pass successfully, the changes you pushed will be live. Any issues during this process will be logged and need to be addressed as per the error messages.

Please be mindful that every push to master will trigger a new deployment. So, always ensure you test your changes locally before pushing to master.

# Editing the Website

## Addons

Most everything about addons are controlled in the JSON Meta Data field in an addon from chargebee.

```json
{
  "showOnWebsite": true,
  "sale_price": 299,
  "website_title": "Make your Wifi Bulletproof"
}
````

`showOnWebsite` must be set to true for them to be listed to customers. `sale_price` adds a crossed off sale price. `website_title` is shown above the addon and is required. `priority` controls how high in the list the addon occurs, the higher the number the higher up it shows. So if you have two addons, one with priority 10 and another with priority 50, the one with priority 50 will show first. Ones with no priority show up at the bottom.

You can add an image by taking it's name from chargebee and then adding it in static/images/addons as a png.

### Yearly Addons

If you want to add a yearly price to an addon you must first create a yearly version of it on chargebee, and then go to the lambdas repository and open the file at `./src/yearlyAddonMap.js` and add a translation from the original add on id to the yearly addon id. For example:

```javascript
module.exports = {
  "200-mbps-add-on": "200-mbps-add-on_Yearly",
  "static-ip": "static-ip-yearly",
}
```

# Addresses

We're overloading the address terminology and fields so it gets really confusing. We have three kinds of addresses and so does Chargebee.

Service Address (where to ship the router) – Shipping Address (in chargebee) (subscription.shipping*address)
Mailing Address (where to send bills) – Mailing Address (in chargebee) (customer.cf_mailing*)
Billing Address (For credit card) – Billing Address (in chargebee) (customer.billing_address)

Building excludiosion tool
https://docs.google.com/document/d/1qKyzsJa8W5FQv3sUvk8-i9QZamfiIdl3NVjyuiFprIU/edit

list of buidlings to be excluded
http://staging-purplecow-dwgarrdrscnn7al2a9recdksyrrxmqvz.s3-website-us-east-1.amazonaws.com/address-tool/

# AWS Deployment

The website is hosted on AWS

### Cloudfront

    *Invalidate*

### Certificate

    have to be on us-east-01

### IAM

### S3

### Route53

### Postal Code Lookups

Adding a postal code means updating the postal-codes.txt file in src directory

# Waiting List Mode

go to `gatsby-config.js` and search for isWaitingListMode, change that value to true or false to control whether users are directed to a waiting list or if they can actually use the checkout functionality.
