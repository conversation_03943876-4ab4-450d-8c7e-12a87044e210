import React from "react"
import { <PERSON> } from "gatsby"
import { colorTheme } from "../../../data/SrtingConstants"

interface LastSectionProps {
  title: string
}

const LastSection: React.FC<LastSectionProps> = ({ title }) => {
  return (
    <div className="container mx-auto">
      <section className="tv-section10">
        <h2 className="h2 text-white text-center">{title}</h2>
        <Link
          style={{ backgroundColor: colorTheme.GOLD, color: "black" }}
          className="btn-bg-new mx-auto mt-8"
          to="/join-the-herd/"
        >
          Join the herd
        </Link>
      </section>
    </div>
  )
}

export default LastSection
