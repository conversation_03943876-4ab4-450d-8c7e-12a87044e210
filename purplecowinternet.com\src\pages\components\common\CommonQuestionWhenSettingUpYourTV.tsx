import React, { useState } from "react"
import { Icon } from "../../../icons/Icons"

const CommonQuestion = () => {
  const [open, setOpen] = useState(0)

  const handleOpen = (value: number) => {
    setOpen(open === value ? 0 : value)
  }

  const questions = [
    {
      id: 1,
      question: <>I can not find the Purple Cow TV app.</>,
      answer: (
        <>
          Please make sure you are searching the app store in one of the below
          approved devices. If you are in a different device you might be in a
          different app store and we may not setup there yet.
          <p>
            <strong>Apple TV</strong>
          </p>
          <li>tvOS 15 or later</li>
          <br></br>
          <strong>iOS Mobile</strong>
          <li>iOS 15 or later</li>
          <br></br>
          <strong>Android Mobile</strong>
          <li>7.0 or later</li>
          <br></br>
          <strong>Android TV</strong>
          <li>
            Minimum OS supported is Android/Google TV 9.0 or higher (do not
            support android mobile release on STB)
          </li>
          <br></br>
          <strong>Google TV</strong>
          <li>All versions</li>
          <br></br>
          <strong>Fire TV</strong>
          <li>Fire TV (Gen 1) - AFTB (released April 12, 2014))</li>
          <li>Fire TV Stick (Gen 1) - AFTM - (released Nov 19, 2014)</li>
          <li>Fire TV (Gen 2) - AFTS - (released Sep 29, 2015)</li>
          <li>Fire TV Stick (Gen 2) - AFTT (released Oct 20, 2016)</li>
          <li>Fire TV Edition - Element 4K (2017) - AFTRS</li>
        </>
      ),
    },
    {
      id: 2,
      question: <> How do I record shows?</>,
      answer: (
        <>
          To record shows you will need to add the DVR feature. Add DVR for $5 a
          month to record up to 50 hours of content that can be watched on any
          of your devices from any room. If you have just added the DVR feature
          and are not seeing it on your screen please login and out of your TV
          account.
        </>
      ),
    },
    {
      id: 3,
      question: <> Can I add other channels?</>,
      answer: (
        <>
          Yes, for sure. You can pretty much add anything you want to the Local
          News package. Also if you want to add a channel we currently do not
          have in our packages let us know and we will see if we can get it for
          you.
        </>
      ),
    },
    {
      id: 4,
      question: (
        <>
          Can I see a complete list of channels Purple Cow offers and channel
          numbers?
        </>
      ),
      answer: (
        <>
          <p>
            A complete channel guide:{" "}
            <a
              href="https://purplecowinternet.com/tv/channel-guide/"
              style={{ color: "purple" }}
            >
              CHANNEL GUIDE
            </a>
          </p>
        </>
      ),
    },
  ]

  return (
    <>
      <section className="tv-section8">
        <div className="container mx-auto px-4">
          <h2 className="h2 text-secondary text-center">
            Some common setup questions
          </h2>
          <div className="max-width1000">
            {questions.map(({ id, question, answer }) => (
              <div key={id} className="border-b">
                <div
                  onClick={() => handleOpen(id)}
                  className="w-full cursor-pointer font-semibold text-left py-4 px-2 flex justify-between items-center text-xl"
                >
                  {question}
                  <Icon id={id} open={open ?? 0} />
                </div>
                {open === id && (
                  <div className="p-2 text-sm text-gray-700">{answer}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  )
}

export default CommonQuestion
