import React from "react"
import Layout from "../components/Layout"
import { He<PERSON><PERSON> } from "react-helmet"
import { StaticImage } from "gatsby-plugin-image"

const PowerNumbersPage = () => (
  <Layout>
    <Helmet>
      {/* eslint-disable-next-line jsx-a11y/accessible-emoji */}
      <title>Power Numbers – Purple Cow Internet 💜🐄</title>
    </Helmet>
    <div className="flex items-center justify-center min-h-screen">
      <div className="p-6">
        <h1>Power Numbers</h1>
        <p className="mt-8 leading-relaxed text-xl text-[#374151]">
          1. Connect to your Purple Cow Wifi.
        </p>
        <p className="mt-6 leading-relaxed text-xl text-[#374151]">
          2. Click this link:{" "}
          <a className="!text-black underline" href="http://192.168.100.1">
            Power Numbers
          </a>
        </p>
        <p className="mt-6 leading-relaxed text-xl text-[#374151]">
          3. Once you pull up the website, please screenshot or save the image
          to your device.
        </p>
        <p className="mt-6 leading-relaxed text-xl text-[#374151]">
          4. Email image to {""}
          <a
            href="mailto:<EMAIL>?subject=Power Numbers&body=Here are my power numbers"
            target="_blank"
            rel="noreferrer"
            className="!text-black underline"
          >
            <EMAIL>
          </a>
        </p>

        <h2 className="my-5">Example Image</h2>
        <StaticImage
          layout="fixed"
          alt="Screenshot of power numbers"
          src="../content/powernumbers.png"
        />
      </div>
    </div>
  </Layout>
)

export default PowerNumbersPage
