import {
  Card<PERSON><PERSON>po<PERSON>,
  CardCVV,
  CardExpiry,
  CardNumber,
} from "@chargebee/chargebee-js-react-wrapper"
import React from "react"

interface CreditCardProps {
  cardRef: any
  setCardErrors: any
  cardErrors: any
}
const CreditCardComponent: React.FC<CreditCardProps> = ({
  cardRef,
  setCardErrors,
  cardErrors,
}) => {
  return (
    <div className="chargebee-card-components">
      <CardComponent
        ref={cardRef}
        styles={{
          base: {
            "::placeholder": {
              color: "#b1b1b1",
              fontFamily: "Satoshi, sans-serif",
            },
          },
        }}
        onChange={(e: {
          field: "expiry" | "cvv" | "number"
          error: string
        }) => {
          setCardErrors((cardErrors: any) => {
            return {
              ...cardErrors,
              [e.field]: Boolean(e.error),
            }
          })
        }}
      >
        <div className="mb-3">
          <label htmlFor="NameonCard" className="mb-1 font-normal text-sm">
            Credit card or visa debit number
          </label>
          <CardNumber
            placeholder="Card number"
            className={`card-input-field ${
              cardErrors?.number ? "border-red-500" : "border-"
            }`}
          />
        </div>
        <div className="flex gap-4 justify-between">
          <div className="w-[50%]">
            <label htmlFor="NameonCard" className="mb-1 font-normal text-sm">
              Expiry (MM/YY)
            </label>
            <CardExpiry
              placeholder="MM/YY"
              className={`card-input-field ${
                cardErrors?.expiry ? "border-red-500" : "border-"
              }`}
            />
          </div>
          <div className="w-[50%]">
            <label htmlFor="NameonCard" className="mb-1 font-normal text-sm">
              Security code
            </label>
            <CardCVV
              placeholder="CVV"
              className={`card-input-field my-font ${
                cardErrors?.cvv ? "border-red-500" : "border-"
              }`}
            />
          </div>{" "}
        </div>
      </CardComponent>
    </div>
  )
}

export default CreditCardComponent
