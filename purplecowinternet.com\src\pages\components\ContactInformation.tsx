import React, { useEffect, useState } from "react"
import { calculateTotalPrice, formatM<PERSON>, scrollToTop } from "../../utils"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import { useDispatch, useSelector } from "react-redux"
import { PageStep, setFormData } from "../../redux/formSlice"
import CommonButtonInternet from "../../components/CommonButton"
import ContactComponent from "./ContactFormComponent"
import { colorTheme } from "../../data/SrtingConstants"
import ProgressBarComponent from "./ProgressBarComponent"
import { validationErrorMessages, validationRegEx } from "../../data/Regex"
import CheckoutBgIcons from "./Checkout_Bg_Icons"

interface ContactInformationProps {}
const ContactInformation: React.FC<ContactInformationProps> = () => {
  const formData = useSelector((state: any) => state.form)
  const dispatch = useDispatch()
  const [contactInfoErros, setContactInfoErrors] = useState({
    firstNameError: "",
    lastNameError: "",
    phoneError: "",
    emailError: "",
    Line1Error: "",
    PostalCodeError: "",
    businessError: "",
  })
  const handleBack = () => {
    dispatch(setFormData({ page: PageStep.HOME_PHONE_PLAN }))
  }

  const handleNext = () => {
    const { first_name, last_name, phone, email, cf_company_name } =
      formData?.customer || {}
    const errors: any = {}

    if (!first_name?.trim())
      errors.firstNameError = validationErrorMessages.ENTER_FIRST_NAME
    if (!last_name?.trim())
      errors.lastNameError = validationErrorMessages.ENTER_LAST_NAME
    if (formData?.is_business && !cf_company_name?.trim())
      errors.businessError = validationErrorMessages.ENTER_BUSINESS_NAME

    const phoneDigits = phone?.replace(/\D/g, "")

    if (!validationRegEx.phone_regex.test(phoneDigits || "")) {
      errors.phoneError = validationErrorMessages.ENTER_CELL_PHONE_NUMBER
    }

    if (!validationRegEx.email_regex.test(email || "")) {
      errors.emailError = validationErrorMessages.ENTER_VALID_EMAIL_ADDRESS
    }

    if (!formData?.mailing_address?.Line1)
      errors.Line1Error = validationErrorMessages.ENTER_ADDRESS
    if (
      !validationRegEx.postalcode_regex.test(
        formData?.mailing_address?.PostalCode || ""
      )
    ) {
      errors.PostalCodeError = validationErrorMessages.ENTER_VALID_POSTAL_CODE
    }

    if (Object.keys(errors).length > 0) {
      setContactInfoErrors({ ...contactInfoErros, ...errors })
      return
    }

    dispatch(setFormData({ page: PageStep.CHECKOUT }))
  }

  useEffect(() => {
    scrollToTop()
  }, [])

  return (
    <>
      <RoundedContainer className="relative" bgColor={colorTheme.MEDIUM_PURPLE}>
        <ProgressBarComponent status={PageStep.CONTACT_INFORMATION} />
        <CheckoutBgIcons />
        <div className="m-2 mt-10 sm:m-10 md:m-16">
          <FibreHeading
            className="text-start"
            align="text-left"
            color="white"
            content="Contact Information"
          />
          <FibreDescription
            color="white"
            className="text-start my-2"
            align="text-left"
            content="Tell us about you"
          />

          <ContactComponent
            contactInfoErros={contactInfoErros}
            setContactInfoErrors={setContactInfoErrors}
            isCheckoutPage={false}
          />
        </div>
      </RoundedContainer>
      <div className="flex sticky bg-white py-3 bottom-0 flex-col sm:flex-row items-center justify-around">
        {!formData.monthEstimate ? (
          <h3 className="font-sans font-bold">Calculating....</h3>
        ) : (
          <h3 className="font-sans font-bold">
            Monthly: {formatMoney(calculateTotalPrice(formData?.monthEstimate))}
          </h3>
        )}
        <div className="flex justify-between gap-5">
          <CommonButtonInternet
            onClick={handleBack}
            buttonText="Back"
            textColor="black"
            bgColor="white"
            className="border-black border font-bold"
          />
          <CommonButtonInternet
            onClick={handleNext}
            buttonText="Next"
            textColor="black"
            className="font-bold"
          />
        </div>
      </div>
    </>
  )
}

export default ContactInformation
