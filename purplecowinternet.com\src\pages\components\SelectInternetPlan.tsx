import React from "react"
import CommonButtonInternet from "../../components/CommonButton"
import { PurpleCheckedIcon } from "../../icons/Icons"
import { splitDescription } from "../../utils"
import { useDispatch, useSelector } from "react-redux"
import { setFormData } from "../../redux/formSlice"

interface InternetPlanProps {
  plans: any
  onSelectInternetPlan: () => void
}

const SelectInternetPlan: React.FC<InternetPlanProps> = ({
  plans,
  onSelectInternetPlan,
}) => {
  const formData = useSelector((state: any) => state.form)
  const dispatch = useDispatch()
  const activeEastLinkPlans =
    plans?.filter(
      (plan: any) => plan?.provider === "Eastlink" && plan?.status === "ACTIVE"
    ) || []

  const activePurpleFibrePlans =
    plans?.filter(
      (plan: any) => plan?.provider === "PurpleCow" && plan?.status === "ACTIVE"
    ) || []

  const finalizedPlans =
    formData?.addressType === "PurpleCow"
      ? activePurpleFibrePlans.length > 0
        ? activePurpleFibrePlans
        : activeEastLinkPlans
      : formData?.addressType === "Eastlink"
        ? activeEastLinkPlans
        : []

  // click event of select internet plan
  const onClickInternetPlan = (plan: any) => {
    dispatch(setFormData({ plan_id: plan?.id })) // set plan id
    onSelectInternetPlan() // add your custom action
  }

  return (
    <div className="flex flex-wrap justify-center gap-6 md:gap-9">
      {finalizedPlans?.map((plan: any) => (
        <div
          key={plan.id}
          className="bg-white border-4 border-[#d9d9d9] rounded-3xl shadow-lg w-72 p-6 flex flex-col justify-between"
        >
          <div>
            <h2 className="!font-anton mb-2">{plan?.name || ""}</h2>
            <div className="flex">
              <h1 className="!font-anton">
                ${plan?.billing[0]?.monthly?.price}
              </h1>
              <p className="text-lg flex items-end ml-2">/month</p>
            </div>
            <ul className="plan-list text-[#666666] my-5">
              {splitDescription(plan.description).map((line, i) => (
                <li className="my-2 flex items-center gap-3 !font-sans" key={i}>
                  <PurpleCheckedIcon />
                  {/* remove "✔️" sign and add svg*/}
                  {line?.replace("✔️", "")?.trim()}
                </li>
              ))}
            </ul>
          </div>
          <div className="flex justify-center">
            <CommonButtonInternet
              buttonText={`${formData?.plan_id === plan?.id ? "Selected" : "Select Plan"}`}
              onClick={() => onClickInternetPlan(plan)}
              className={`${formData?.plan_id === plan?.id ? "font-bold" : ""}`}
            />
          </div>
        </div>
      ))}
    </div>
  )
}

export default SelectInternetPlan
