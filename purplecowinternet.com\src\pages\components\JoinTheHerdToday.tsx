import React, { ReactNode } from "react"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreHeading } from "../../components/FibreHeading"
import CommonButtonInternet from "../../components/CommonButton"
import { navigate } from "gatsby"

interface JoinHerdProps {
  buttonContent: ReactNode
}

const JoinTheHerdToday: React.FC<JoinHerdProps> = ({ buttonContent }) => {
  return (
    <RoundedContainer bgColor="white">
      <div className="flex flex-col items-center justify-center bg-white p-6 sm:p-20 w-full text-center">
        <FibreHeading
          color="black"
          align="text-center"
          content={"JOIN THE HERD TODAY"}
        />

        {/* Address Input Box */}
        <div className="w-full flex justify-center mt-12">
          <CommonButtonInternet
            buttonText={buttonContent}
            onClick={() => navigate("/join-the-herd")}
          />
        </div>
      </div>
    </RoundedContainer>
  )
}

export default JoinTheHerdToday
