import React, { useEffect, useState } from "react"
import RoundedContainer from "../../components/RoundedContainer"
import { FibreDescription, FibreHeading } from "../../components/FibreHeading"
import ServiceAddress from "./ServiceAddress"
import {
  Address,
  isAddressExcluded,
  testAddressIsPurpleFibre,
} from "../../addressValidator"
import customers from "raw-loader!../../content/new-pictou-customers.txt"
import fibercustomers from "raw-loader!../../content/new-purple-cow-fiber.txt"
import { useDispatch, useSelector } from "react-redux"
import { PageStep, setFormData } from "../../redux/formSlice"
import { colorTheme } from "../../data/SrtingConstants"
import SelectInternetPlan from "./SelectInternetPlan"
import { navigate } from "gatsby"
import { formatAddress, formatPhoneNumber, scrollToTop } from "../../utils"
import Loading from "../../components/Loading"
import CommonButtonInternet from "../../components/CommonButton"
import CheckoutBgIcons from "./Checkout_Bg_Icons"
import { validationErrorMessages, validationRegEx } from "../../data/Regex"
interface CheckPLansProps {
  plans: any
  exclusions: any
  fibreAddresses: any
}
const CheckPlans: React.FC<CheckPLansProps> = ({
  plans,
  exclusions,
  fibreAddresses,
}) => {
  const [doubleCheckAddress, setDoubleCheckAddress] = useState<any>(false)
  const dispatch = useDispatch()
  const formData = useSelector((state: any) => state.form)
  const [loader, setLoader] = useState<boolean>(false)
  const [phone, setPhone] = useState<string>("")
  const [phone_error, setPhoneError] = useState("")
  const [error, setError] = useState(false)
  const [showInputCustomPostalCode, setShowInputCustomPostalCode] =
    useState(false)
  let allowedPostalCodes = import(
    "raw-loader!../../content/postal-codes.txt"
  ).then((codes) => codes.default)
  const normalisePostalCode = (postalCode: string) => {
    return postalCode.replace(/\s/g, "").toLowerCase()
  }

  const checkpictoutCustomerAddress = async (caddress: any) => {
    let customer_address_list: any = await pictoutCustomer
    if (customer_address_list) {
      return customer_address_list.some((exclusion: any) =>
        Object.keys(exclusion).every(
          (key: string) =>
            exclusion[key as keyof Address] === caddress[key as keyof Address]
        )
      )
    }
    return false
  }

  const pictoutCustomer = new Promise((resolve, reject) => {
    try {
      const parsedCustomers = JSON.parse(customers)
      resolve(parsedCustomers)
    } catch (error) {
      reject(error)
    }
  })

  const testShippingPostalCodeIsValid = async (formState: any) => {
    const postalCode = formState?.PostalCode
    if (!postalCode) return false

    const normalized = normalisePostalCode(postalCode)
    const allowed = await allowedPostalCodes

    return allowed?.includes(normalized)
  }

  const fiberCustomer: any = new Promise((resolve, reject) => {
    try {
      const fibercustomerss = JSON.parse(fibercustomers)
      resolve(fibercustomerss)
    } catch (error) {
      reject(error)
    }
  })

  const checkAddressIsPurpleFibre = async (formState: any) => {
    return testAddressIsPurpleFibre(formState, fibreAddresses)
  }

  const checkFibercustomerAddress = async (caddress: any) => {
    let customer_address_list: any = await fiberCustomer
    if (customer_address_list) {
      return customer_address_list.some((exclusion: any) =>
        Object.keys(exclusion).every(
          (key: string) =>
            exclusion[key as keyof Address] === caddress[key as keyof Address]
        )
      )
    }
    return false
  }

  const sendEmail = async (e: any) => {
    e && e.preventDefault()

    if (!phone) {
      setPhoneError("Enter phone number")
      return
    }

    if (!phone_error && phone) {
      setError(false)
      setLoader(true)

      try {
        const response = await fetch(
          `${process.env.GATSBY_LAMBDA}/email-salesforce`,
          {
            method: "POST",
            body: JSON.stringify({
              address: { Label: formatAddress(doubleCheckAddress) },
              phone,
            }),
          }
        )
        if (!response.ok) {
          setError(true)
          return
        }
        await response.json() // Ensure response is read properly
        alert("We'll shoot you a text back at the provided number.")
        dispatch(
          setFormData({
            ...formData,
            shipping_address: {
              Line1: "",
              SubBuilding: "",
              City: "",
              Province: "",
              PostalCode: "",
            },
          })
        )
        setDoubleCheckAddress(false)
        setPhone("")
      } catch (error) {
        setError(true)
      } finally {
        setLoader(false)
      }
    }
  }

  const checkPhoneNo = async (phone: any) => {
    if (phone) {
      let pNumber = phone.replace(/\D/g, "")
      phone = formatPhoneNumber(pNumber)
      let phoneValid = pNumber.match(
        /^(\+1 ?)?\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
      )
      if (!phoneValid) {
        phoneValid = pNumber.match(
          /^(\+?1 ?)?\(?([0-9]{1})\)?[-. ]?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})/
        )
      }
      if (!phoneValid) {
        setPhoneError(validationErrorMessages.ENTER_CELL_PHONE_NUMBER)
      } else {
        setPhoneError("")
      }
    }
    setPhone(phone)
  }

  const checkAddress = async () => {
    setDoubleCheckAddress(false)

    // reset date and SA_SFRecordID  on address change
    dispatch(
      setFormData({
        customer: { ...formData?.customer, cf_suggested_turn_on_date: "" },
        SA_SFRecordID: "",
      })
    )

    if (
      isAddressExcluded(formData.shipping_address, exclusions) ||
      !(await testShippingPostalCodeIsValid(formData?.shipping_address)) ||
      formData.shipping_address == null
    ) {
      // for invalid address
      dispatch(
        setFormData({
          shipping_address: {
            Line1: "",
            SubBuilding: "",
            City: "",
            Province: "",
            PostalCode: "",
          },
        })
      )

      setDoubleCheckAddress(formData?.shipping_address)
      dispatch(setFormData({ ...formData, addressType: "" }))
    } else {
      // for valid address
      if (await checkpictoutCustomerAddress(formData?.shipping_address)) {
        dispatch(
          setFormData({
            Service_Area: "Pictou_Customers",
          })
        )
      } else if (await checkFibercustomerAddress(formData?.shipping_address)) {
        dispatch(
          setFormData({
            Service_Area: "Purple_Cow_Fiber_Customer",
          })
        )
      } else {
        dispatch(
          setFormData({
            Service_Area: "Eastlink_Customer",
          })
        )
      }

      if (plans) {
        const isPurpleFibre = await checkAddressIsPurpleFibre(
          formData.shipping_address
        )

        if (isPurpleFibre) {
          dispatch(
            setFormData({
              SA_SFRecordID: isPurpleFibre?.SA_SFRecordID ?? "",
            })
          )
        }

        dispatch(
          setFormData({
            addressType: isPurpleFibre ? "PurpleCow" : "Eastlink",
          })
        )
      }
    }
  }

  useEffect(() => {
    formData?.shipping_address?.Line1 && checkAddress()
  }, [formData?.shipping_address])

  useEffect(() => {
    scrollToTop()
  }, [])

  return (
    <RoundedContainer
      className="relative mt-4 pb-8"
      bgColor={colorTheme.MEDIUM_PURPLE}
    >
      <CheckoutBgIcons />

      <div className="flex flex-col gap-8 ml-5 mt-5 sm:ml-10 sm:mt-10 md:ml-16 md:mt-16">
        <FibreHeading
          className="text-start "
          align="text-left"
          color="white"
          content={
            <span className="!font-anton">
              Check <br className="block sm:hidden" /> Plans
            </span>
          }
        />
        <FibreDescription
          className="text-start w-[80%]"
          color="white"
          align="text-left"
          content={
            <span>
              See what plans are available in your neck of the{" "}
              <br className="hidden lg:block" /> woods
            </span>
          }
        />
        <div className="flex flex-col sm:flex-row gap-1">
          <FibreDescription
            color="black"
            className="text-start whitespace-nowrap"
            align="text-left"
            content={"Step 1:"}
          />
          <FibreDescription
            color="white"
            className="text-start whitespace-nowrap"
            align="text-left"
            content={"Enter Service Address"}
          />
        </div>
      </div>

      <div className="w-full">
        <ServiceAddress
          setShowInputCustomPostalCode={setShowInputCustomPostalCode}
          showInputCustomPostalCode={showInputCustomPostalCode}
        />
      </div>

      {doubleCheckAddress && (
        <form className="m-4" onSubmit={sendEmail}>
          <FibreDescription
            className="text-start m-5 sm:ml-10 md:ml-16"
            color="white"
            align="text-left"
            content={"Looks like we need to double-check this address"}
          />

          <p className="text-start m-5 sm:ml-10 md:ml-16 text-white">
            Let me know your number, and I will double-check the address you
            entered.
          </p>

          <div className="flex flex-col mt-4 sm:mx-10 md:mx-16">
            <label className="mb-1 text-sm text-white" htmlFor="number">
              Mobile phone
            </label>
            <input
              id="number"
              className="rounded-3xl border-none placeholder:font-satoshi placeholder:text-[#A9A9A9] placeholder:text-sm w-100 max-w-xl"
              maxLength={40}
              name="mobile"
              size={20}
              type="tel"
              disabled={loader}
              autoComplete="off"
              placeholder="Mobile phone"
              required
              onChange={(e) => checkPhoneNo(e.target.value)}
              value={phone}
            />
            <span className="mt-2 text-sm text-red-600">{phone_error}</span>
            <div className="flex flex-col mt-6">
              {loader ? (
                <Loading />
              ) : (
                <CommonButtonInternet
                  onClick={(e) => sendEmail(e)}
                  className="mt-6 mb-1 py-4 px-12 sm:px-16"
                  buttonText="Double check address"
                />
              )}
              {error && (
                <div className="p-4 text-center bg-red-100 border border-red-400 text-red-700 rounded-lg">
                  <p className="text-sm font-medium">
                    There was a problem sending a message. You can try texting
                    us at:
                  </p>
                  <div className="mt-2 text-lg font-semibold text-red-900">
                    **************
                  </div>
                </div>
              )}
            </div>
          </div>
        </form>
      )}

      {(!showInputCustomPostalCode ||
        (showInputCustomPostalCode &&
          validationRegEx.postalcode_regex.test(
            formData?.shipping_address?.PostalCode
          ))) && (
        <SelectInternetPlan
          onSelectInternetPlan={() => {
            dispatch(setFormData({ page: PageStep.TV_PLAN }))
            navigate("/join-the-herd")
          }}
          plans={plans}
        />
      )}
    </RoundedContainer>
  )
}

export default CheckPlans
