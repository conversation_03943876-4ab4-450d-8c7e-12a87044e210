// Given an address and a list of exlusions, if all of the fields of any exclusion matches, then this address is invalid.
export function isAddressExcluded(
  address: Address,
  exclusionList: Array<Partial<Address>>
) {
  return exclusionList?.some((exclusion: Partial<Address>) =>
    Object.keys(exclusion).every(
      (key: string) =>
        exclusion[key as keyof Address] === address[key as keyof Address]
    )
  )
}

export const allowedProvinces = ["NS", "NL", "PE"]

export function testAddressIsPurpleFibre(
  address: Address,
  exclusionList: Array<Partial<Address>>
): Partial<Address> | undefined {
  const normalizedAddress = { ...address }

  // Normalize Street: replace " crt" at the end with " ct"
  if (normalizedAddress.Street?.toLocaleLowerCase()?.endsWith(" ct")) {
    normalizedAddress.Street =
      normalizedAddress?.Street?.toLocaleLowerCase()?.replace(/ ct$/, " crt")
  }

  // Normalize Street: replace " crescent" at the end with " cres"
  if (normalizedAddress.Street?.toLocaleLowerCase()?.endsWith(" crescent")) {
    normalizedAddress.Street =
      normalizedAddress?.Street?.toLocaleLowerCase()?.replace(
        / crescent$/,
        " cres"
      )
  }

  return exclusionList.find((exclusion: Partial<Address>) =>
    Object.keys(exclusion).every((key: string) => {
      if (key === "SA_SFRecordID") return true // Skip this key
      return (
        exclusion[key as keyof Address]?.toLocaleLowerCase() ===
        normalizedAddress[key as keyof Address]?.toLocaleLowerCase()
      )
    })
  )
}

export interface Address {
  Id: string
  DomesticId: string
  Language: string
  LanguageAlternatives: string
  Department: string
  Company: string
  SubBuilding: string
  BuildingNumber: string
  BuildingName: string
  SecondaryStreet: string
  Street: string
  Block: string
  Neighbourhood: string
  District: string
  City: string
  Line1: string
  Line2: string
  Line3: string
  Line4: string
  Line5: string
  AdminAreaName: string
  AdminAreaCode: string
  Province: string
  // ProvinceName: string
  // ProvinceCode: string
  PostalCode: string
  CountryName: string
  CountryIso2: string
  Country: string
  CountryIso3: string
  CountryIsoNumber: string
  SortingNumber1: string
  SortingNumber2: string
  Barcode: string
  POBoxNumber: string
  Label: string
  Type: string
  DataLevel: string
  FormattedLine1: string
  SA_SFRecordID?: string
}
