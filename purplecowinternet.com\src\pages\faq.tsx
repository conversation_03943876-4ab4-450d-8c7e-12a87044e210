import React from "react"
import Layout from "../components/Layout"
import { Helmet } from "react-helmet"
import MessageModal from "./components/common/MessageModal"
import phoneImg1 from "../../static/images/phone-banner.png"
import CommonButtonInternet from "../components/CommonButton"
import { colorTheme } from "../data/SrtingConstants"
import RoundedContainer from "../components/RoundedContainer"
import { navigate } from "gatsby"
import CommonQuestions from "../components/CommonQuestions"
import {
  eastLinkQuestions,
  homePageQuestions,
  homePhoneQuestions,
  tvQuestions,
} from "../data/CommonQuestionsData"

const FaqPage = () => {
  const [messagePopup, setMessagePopup] = React.useState(false)
  return (
    <Layout>
      <Helmet>
        <title>FAQs – Purple Cow Internet 💜🐄</title>
      </Helmet>
      <RoundedContainer>
        <h1 className="text-4xl text-center font-extrabold m-5">FAQs</h1>
        <img
          src={phoneImg1}
          alt="Faq-img"
          className="rounded-3xl object-cover object-center w-full min-h-[200px]"
        />
      </RoundedContainer>

      <CommonQuestions
        bgColor={colorTheme.MEDIUM_PURPLE}
        questions={homePageQuestions}
      />
      <CommonQuestions
        bgColor={"white"}
        textColor="black"
        title="Internet questions"
        questions={eastLinkQuestions}
      />
      <CommonQuestions
        bgColor={colorTheme.MEDIUM_PURPLE}
        title={`Television questions`}
        questions={tvQuestions}
      />
      <CommonQuestions
        bgColor={"white"}
        textColor="black"
        title="Home phone questions"
        questions={homePhoneQuestions}
      />

      <RoundedContainer
        className="flex flex-col justify-center items-center gap-5 sm:gap-10 p-5 sm:p-10 md:p-20"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <h1 className="!font-anton text-center text-white uppercase">
          Or If You're Ready...
        </h1>
        <h3 className="!font-sans text-white text-center mt-5 lg:w-[60%]">
          If you still have a question, shoot us a message and our team will
          respond within mins
        </h3>

        <div className="flex justify-between gap-6">
          <CommonButtonInternet
            buttonText="Message Us"
            onClick={(e) => {
              const isMobile = /iPhone|iPad|iPod|Android/i.test(
                navigator.userAgent
              )
              if (isMobile) {
                e.preventDefault() // Prevent the default anchor click behavior
                window.location.href =
                  "sms:**************?body=Hey%20Purple%20Cow%20I%20have%20a%20question.%20"
              } else {
                setMessagePopup(true)
              }
            }}
            className="px-5 !text-white border border-white"
            bgColor="transparent"
          />
          <CommonButtonInternet
            buttonText="Join the Herd"
            onClick={() => navigate("/join-the-herd")}
            className="px-5"
          />
        </div>
      </RoundedContainer>
      {/* Message Us popup */}
      {messagePopup && <MessageModal closepopup={setMessagePopup} />}
    </Layout>
  )
}

export default FaqPage
