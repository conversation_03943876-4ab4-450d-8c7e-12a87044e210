import React from "react"
import Layout from "../components/Layout"
import { Helmet } from "react-helmet"
import MessageModal from "./components/common/MessageModal"
import handsIn from "../../static/images/handsIn.png"
import graphphoto from "../../static/images/neighborinneedupdate.png"
import totalphoto from "../../static/images/totalphoto.png"
import RoundedContainer from "../components/RoundedContainer"
import { FibreHeading } from "../components/FibreHeading"
import { colorTheme } from "../data/SrtingConstants"
import CommonButtonInternet from "../components/CommonButton"
import { navigate } from "gatsby"
import CommonQuestions from "../components/CommonQuestions"
import { neighborInNeedQuestions } from "../data/CommonQuestionsData"

const FrontTVPage = () => {
  const [messagePopup, setMessagePopup] = React.useState(false)
  const testimonials = [
    {
      name: "Anonymous, Halifax, Nova Scotia",
      rating: 0,
      description:
        "I lost my job unexpectedly, and it was hard keeping up with my bills. The Neighbor in Need program at Purple Cow helped me stay connected while I got back on my feet. It’s amazing to see a company that genuinely cares about their customers.",
    },
    {
      name: "Anonymous, Sackville, Nova Scotia",
      rating: 0,
      description:
        "When an unexpected medical expense hit, I wasn’t sure how I’d pay my internet bill. Purple Cow’s Neighbor in Need program stepped in and covered it for me. The process was so simple, and it helped me focus on getting through a tough time without losing my internet.",
    },
    {
      name: "Anonymous, Halifax, Nova Scotia",
      rating: 0,
      description:
        "I’ve always loved Purple Cow, but their Neighbor in Need program took it to another level. When finances got tight, they made sure I stayed connected. Knowing that they have my back made all the difference.",
    },
  ]

  return (
    <Layout>
      <Helmet>
        <title>TV – Purple Cow Internet 💜🐄</title>
      </Helmet>

      <RoundedContainer>
        <FibreHeading
          content={"The herd helping the herd"}
          align="text-center"
          color="black"
          className="my-5"
        />

        <img
          src={handsIn}
          alt="TV"
          className="rounded-3xl object-cover object-center w-full min-h-[200px]"
        />
      </RoundedContainer>

      <RoundedContainer
        className="flex flex-col lg:flex-row p-5 md:p-20"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <div className="w-full lg:w-[50%]">
          <FibreHeading
            content={"What is Purple Cow's Neighbor in Need program?"}
            align="text-left"
            color="white"
          />
        </div>
        <div className="w-full lg:w-[50%]">
          <h3 className="mt-5 !font-sans text-white">
            We all know times are tough, and we want to help make a difference.
            That’s why we’re partnering with the herd to create positive change.
            We're asking members to donate just $2 a month, and Purple Cow will
            match each donation dollar for dollar.
          </h3>
          <h3 className="mt-10 !font-sans text-white">
            These funds will go directly to people who are facing financial
            hardship to keep their internet connected as they work to get back
            on their hooves.
          </h3>
        </div>
      </RoundedContainer>

      <div className="flex flex-col p-4">
        <FibreHeading
          content={"Where are we at?"}
          align="text-center"
          color="black"
        />
        <h3 className="!font-sans text-center font-medium my-10">
          Our goal is to maintain complete transparency with the Neighbor in
          Need program, <br className="hidden md:block" /> and we will provide
          regular updates on the funds contributed and distributed.
        </h3>
        <img
          src={graphphoto}
          alt="graph-photo"
          className="mx-auto rounded-3xl"
        />
        <img
          src={totalphoto}
          alt="total-photo"
          className="mx-auto rounded-3xl"
        />
      </div>

      <RoundedContainer bgColor={colorTheme.MEDIUM_PURPLE}>
        <FibreHeading
          align="text-center"
          color="white"
          content={"The herd helping the herd"}
          className="my-10"
        />

        <div className="px-4 sm:px-6 lg:px-20 xl:px-40 py-12">
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-white shadow-md rounded-2xl p-6 flex flex-col justify-between hover:shadow-lg transition-shadow duration-300"
              >
                <p className="text-gray-700 mb-4 text-base leading-relaxed">
                  “{testimonial.description}”
                </p>
                <div className="mt-4">
                  <p className="font-semibold text-purple-700">
                    {testimonial.name}
                  </p>
                  {testimonial.rating > 0 && (
                    <div className="flex items-center mt-1">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${
                            i < testimonial.rating
                              ? "text-yellow-400"
                              : "text-gray-300"
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.966a1 1 0 00.95.69h4.18c.969 0 1.371 1.24.588 1.81l-3.388 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.921-.755 1.688-1.54 1.118L10 13.348l-3.388 2.46c-.784.57-1.838-.197-1.539-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.608 9.393c-.783-.57-.38-1.81.588-1.81h4.18a1 1 0 00.95-.69l1.286-3.966z" />
                        </svg>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </RoundedContainer>

      <CommonQuestions questions={neighborInNeedQuestions} />

      {/* Or If You're Ready... */}
      <RoundedContainer
        className="flex flex-col justify-center items-center gap-5 sm:gap-10 p-5 sm:p-10 md:p-20"
        bgColor={colorTheme.MEDIUM_PURPLE}
      >
        <h1 className="!font-anton text-center text-white uppercase">
          Or If You're Ready...
        </h1>
        <h3 className="!font-sans text-white text-center mt-5 lg:w-[60%]">
          If you still have a question, shoot us a message and our team will
          respond within mins
        </h3>

        <div className="flex justify-between gap-6">
          <CommonButtonInternet
            buttonText="Message Us"
            onClick={(e) => {
              const isMobile = /iPhone|iPad|iPod|Android/i.test(
                navigator.userAgent
              )
              if (isMobile) {
                e.preventDefault() // Prevent the default anchor click behavior
                window.location.href =
                  "sms:**************?body=Hey%20Purple%20Cow%20I%20have%20a%20question.%20"
              } else {
                // Open the existing popup on other devices
                setMessagePopup(true)
              }
            }}
            className="px-5 !text-white border border-white"
            bgColor="transparent"
          />
          <CommonButtonInternet
            buttonText="Join the Herd"
            onClick={() => navigate("/join-the-herd")}
            className="px-5"
          />
        </div>
      </RoundedContainer>
      {messagePopup == true ? (
        <MessageModal closepopup={setMessagePopup}></MessageModal>
      ) : null}
    </Layout>
  )
}

export default FrontTVPage
