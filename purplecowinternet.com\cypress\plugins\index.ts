/// <reference types="cypress" />
// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)

import { ChargeBee } from "chargebee-typescript"
import { Customer } from "chargebee-typescript/lib/resources"

/**
 * @type {Cypress.PluginConfig}
 */
// eslint-disable-next-line no-unused-vars
module.exports = (on, config) => {
  const chargebee = new ChargeBee()
  chargebee.configure({
    site: process.env.GATSBY_CHARGEBEE_SITE,
    api_key: process.env.GATSBY_CHARGEBEE_API_KEY,
  })

  // `on` is used to hook into various events Cypress emits
  // `config` is the resolved Cypress config
  on("task", {
    async getChargebeeUserAndSubscriptions(email: string) {
      let { list: customers } = await chargebee.customer
        .list({
          email: {
            is: email,
          },
        })
        .request()

      if (customers.length > 1) {
        return { status: "error", message: "Multiple users found." }
      }

      const customer = customers[0].customer as Customer
      // get subscription for customer

      const { list: subscriptions } = await chargebee.subscription
        .list({
          customer_id: {
            is: customer.id,
          },
        })
        .request()

      return {
        customer,
        subscriptions: subscriptions.map(({ subscription }) => subscription),
      }
    },
  })
}
