.wpbr-wrap .swiper-pagination {
  position: static;
  text-align: center;
}
.wpbr-wrap .swiper-pagination-bullet {
  background: #000;
  border-radius: 100%;
  display: inline-block;
  height: 10px;
  opacity: 0.15;
  width: 10px;
}
.wpbr-wrap button.swiper-pagination-bullet {
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-box-shadow: none;
  appearance: none;
  border: none;
  box-shadow: none;
  margin: 0;
  padding: 0;
}
.wpbr-wrap .swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.wpbr-wrap .swiper-pagination-bullet-active {
  background: #3498db;
  opacity: 1;
}
.wpbr-wrap
  .swiper-container-horizontal
  > .swiper-pagination-bullets
  .swiper-pagination-bullet {
  margin: 0 5px;
}
.wpbr-collection-wrap {
  padding-bottom: 0.1px;
}
.wpbr-collection--list .wpbr-collection__item {
  margin-bottom: 20px;
}
.wpbr-collection--list .wpbr-collection__item:last-child {
  margin-bottom: 0;
}
.wpbr-collection--carousel {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 10px;
}
.wpbr-collection--carousel .wpbr-collection__item {
  -ms-flex-negative: 0;
  -webkit-flex-shrink: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-shrink: 0;
  margin-bottom: 0;
}
.wpbr-collection--gallery {
  -ms-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  border: solid transparent;
  border-width: 0 20px 0 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  margin: 0 -20px -20px;
  min-width: 100%;
}
.wpbr-collection--gallery .wpbr-collection__item {
  background-clip: padding-box;
  border: 0 solid transparent;
  border-width: 0 0 20px 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}
@media (min-width: 960px) {
  .wpbr-collection--2-col .wpbr-collection__item,
  .wpbr-collection--3-col .wpbr-collection__item,
  .wpbr-collection--4-col .wpbr-collection__item,
  .wpbr-collection--auto-fit .wpbr-collection__item {
    width: 50%;
  }
}
@media (min-width: 1140px) {
  .wpbr-collection--3-col .wpbr-collection__item,
  .wpbr-collection--4-col .wpbr-collection__item,
  .wpbr-collection--auto-fit .wpbr-collection__item {
    width: 33.333%;
  }
}
@media (min-width: 1440px) {
  .wpbr-collection--4-col .wpbr-collection__item {
    width: 25%;
  }
}
@media (min-width: 601px) {
  .widget .wpbr-collection--auto-fit .wpbr-collection__item,
  .wpbr-collection-widget .wpbr-collection--auto-fit .wpbr-collection__item {
    width: 100%;
  }
}
@supports (display: grid) {
  .wpbr-collection--gallery {
    border: 0;
    display: grid;
    grid-gap: 20px;
    margin: 0;
  }
  .wpbr-collection--gallery .wpbr-collection__item {
    border: 0;
    min-width: 0;
    width: auto;
  }
  @media (min-width: 960px) {
    .wpbr-collection--2-col,
    .wpbr-collection--3-col,
    .wpbr-collection--4-col {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  @media (min-width: 1140px) {
    .wpbr-collection--3-col,
    .wpbr-collection--4-col {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  @media (min-width: 1440px) {
    .wpbr-collection--4-col {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  @media (min-width: 601px) {
    .wpbr-collection--auto-fit {
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
    .widget .wpbr-collection--auto-fit,
    .wpbr-collection-widget .wpbr-collection--auto-fit {
      grid-template-columns: 1fr;
    }
  }
}
.wpbr-wrap {
  font-family: sans-serif;
  font-size: 16px;
  margin-bottom: 20px;
}
.wpbr-wrap *,
.wpbr-wrap :after,
.wpbr-wrap :before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.wpbr-wrap:last-child {
  margin-bottom: 0;
}
.wpbr-reco {
  -ms-flex-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 0;
}
.wpbr-reco__text {
  -ms-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.wpbr-review {
  color: #5d6f80;
  font-size: 1em;
  margin-bottom: 20px;
  position: relative;
  text-align: left;
  width: 100%;
}
.wpbr-review:last-child {
  margin-bottom: 0;
}
.wpbr-review__header {
  -ms-flex-align: center;
  -ms-flex-wrap: wrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -webkit-flex-wrap: wrap;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
}
.wpbr-review__reviewer-image {
  -ms-flex-negative: 0;
  -ms-flex-preferred-size: 60px;
  -webkit-flex-basis: 60px;
  -webkit-flex-shrink: 0;
  background-size: cover;
  flex-basis: 60px;
  flex-shrink: 0;
  height: 60px;
  margin-right: 10px;
  overflow: hidden;
  width: 60px;
}
.wpbr-review__reviewer-image img {
  -o-object-fit: cover;
  display: block;
  height: 100%;
  object-fit: cover;
  width: 100%;
}
.wpbr-review__reviewer-image--custom img {
  border-radius: 9999px;
}
.wpbr-review__reviewer-image .wpbr-icon {
  color: #cfd9db;
  height: 100%;
  width: 100%;
}
.wpbr-review__details {
  -ms-flex: 1;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  overflow: hidden;
}
.wpbr-review__details :last-child {
  margin-bottom: 0;
}
.wpbr-review__platform-icon {
  -webkit-box-shadow: none;
  background-repeat: no-repeat;
  box-shadow: none;
  display: block;
  height: 18px;
  position: absolute;
  right: 0;
  top: 0;
  width: 18px;
}
.wpbr-review__reviewer-name {
  color: #5d6f80 !important;
  font-family: inherit !important;
  font-size: 1em !important;
  font-weight: 700 !important;
  line-height: 1.3 !important;
  margin: 0 0 2.5px !important;
  padding: 0 !important;
  text-align: left !important;
  text-transform: none !important;
  word-wrap: break-word !important;
}
.wpbr-review__platform-icon + .wpbr-review__reviewer-name {
  margin-right: 30px !important;
}
.wpbr-review__rating {
  display: block;
  line-height: 0;
  margin-bottom: 5px;
}
.wpbr-review__reco {
  margin-bottom: 5px;
}
.wpbr-review__timestamp {
  color: #5d6f80 !important;
  display: block;
  font-size: 75% !important;
  line-height: 1.3 !important;
}
.wpbr-review__content {
  margin-top: 10px;
  width: 100%;
}
.wpbr-review__content p {
  color: #5d6f80 !important;
  font-size: 1em !important;
  line-height: 1.5 !important;
  margin: 0 0 20px !important;
  padding: 0 !important;
  text-align: left !important;
  width: 100%;
}
.wpbr-review__content:first-child {
  margin-top: 0 !important;
}
.wpbr-review__content > :last-child {
  margin-bottom: 0 !important;
}
.wpbr-review__omission {
  white-space: nowrap;
}
.wpbr-review-source > :last-child {
  margin-bottom: 0;
}
.wpbr-review-source__name {
  color: #5d6f80 !important;
  display: block !important;
  font-family: inherit !important;
  font-size: 1em !important;
  font-weight: 700 !important;
  line-height: 1.3 !important;
  margin: 0 0 2.5px !important;
  padding: 0 !important;
  text-align: left !important;
  text-decoration: none;
  text-transform: none !important;
  word-wrap: break-word !important;
}
.wpbr-review-source a.wpbr-review-source__name:active,
.wpbr-review-source a.wpbr-review-source__name:focus,
.wpbr-review-source a.wpbr-review-source__name:hover {
  text-decoration: underline;
}
.wpbr-review-source__rating {
  display: block;
  margin-bottom: 5px;
}
.wpbr-review-source__address,
.wpbr-review-source__id {
  display: block;
  font-size: 0.875em;
}
.wpbr-review-source__id {
  color: #9facb9;
}
.wpbr-review-source__image {
  display: block;
}
.wpbr-review-source button.wpbr-review-source__button {
  margin-top: 5px;
}
.wpbr-zomato-rating {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 75%;
  height: 20px;
  line-height: 20px;
}
.wpbr-zomato-rating--large {
  font-size: 100%;
  height: 20px;
  line-height: 20px;
}
.wpbr-zomato-rating__text {
  font-weight: 700;
  margin-right: 5px;
  text-transform: uppercase;
}
.wpbr-zomato-rating__number {
  background: grey;
  border-radius: 5px;
  color: #fff;
  display: inline-block;
  padding: 0 5px;
  text-align: center;
}
.wpbr-zomato-rating__number--level-new {
  background: #89959b;
}
.wpbr-zomato-rating__number--level-0 {
  background: #cdcdcd;
}
.wpbr-zomato-rating__number--level-1 {
  background: #cd1c26;
}
.wpbr-zomato-rating__number--level-2 {
  background: #de1d0f;
}
.wpbr-zomato-rating__number--level-3 {
  background: #ff7800;
}
.wpbr-zomato-rating__number--level-4 {
  background: #ffba00;
}
.wpbr-zomato-rating__number--level-5 {
  background: #cdd614;
}
.wpbr-zomato-rating__number--level-6 {
  background: #9acd32;
}
.wpbr-zomato-rating__number--level-7 {
  background: #5ba829;
}
.wpbr-zomato-rating__number--level-8 {
  background: #3f7e00;
}
.wpbr-zomato-rating__number--level-9 {
  background: #305d02;
}
.wpbr-theme-custom .wpbr-stars {
  background-position: -100px -10px;
  background-repeat: no-repeat;
  background-size: 200px 94px;
  display: inline-block;
  height: 18px;
  width: 90px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-custom .wpbr-stars {
  }
}
.wpbr-theme-custom .wpbr-stars--1 {
  background-position: -82px -10px;
}
.wpbr-theme-custom .wpbr-stars--1-half {
  background-position: -82px -38px;
}
.wpbr-theme-custom .wpbr-stars--2 {
  background-position: -64px -10px;
}
.wpbr-theme-custom .wpbr-stars--2-half {
  background-position: -64px -38px;
}
.wpbr-theme-custom .wpbr-stars--3 {
  background-position: -46px -10px;
}
.wpbr-theme-custom .wpbr-stars--3-half {
  background-position: -46px -38px;
}
.wpbr-theme-custom .wpbr-stars--4 {
  background-position: -28px -10px;
}
.wpbr-theme-custom .wpbr-stars--4-half {
  background-position: -28px -38px;
}
.wpbr-theme-custom .wpbr-stars--5 {
  background-position: -10px -10px;
}
.wpbr-theme-custom .wpbr-review__platform-icon {
  background-position: -10px -66px;
  background-size: 200px 94px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-custom .wpbr-review__platform-icon {
  }
}
.wpbr-theme-custom.wpbr-admin-column-thumbnail,
.wpbr-theme-custom .wpbr-image-selector__image,
.wpbr-theme-custom .wpbr-review__reviewer-image img {
  border-radius: 9999px;
}
.wpbr-theme-dark .wpbr-reco__text,
.wpbr-theme-dark .wpbr-review,
.wpbr-theme-dark .wpbr-review__content,
.wpbr-theme-dark .wpbr-review__content p,
.wpbr-theme-dark .wpbr-review__reviewer-name,
.wpbr-theme-dark .wpbr-review__timestamp {
  color: #fff !important;
}
.wpbr-theme-dark :not(.wpbr-theme-highlight) .wpbr-review a {
  color: #30ceff !important;
}
.wpbr-theme-dark :not(.wpbr-theme-highlight) .wpbr-review a:active,
.wpbr-theme-dark :not(.wpbr-theme-highlight) .wpbr-review a:focus,
.wpbr-theme-dark :not(.wpbr-theme-highlight) .wpbr-review a:hover {
  color: #3498db !important;
}
.wpbr-theme-dark.wpbr-collection--carousel .wpbr-review,
.wpbr-theme-dark.wpbr-collection--gallery .wpbr-review,
.wpbr-theme-dark.wpbr-collection--list {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  background-color: #fff;
  background-color: #23282d;
  border: 1px solid #cfd9db;
  border-radius: 5px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
  padding: 20px;
  width: 100%;
}
.wpbr-theme-dark.wpbr-collection--carousel .wpbr-review:last-child,
.wpbr-theme-dark.wpbr-collection--gallery .wpbr-review:last-child,
.wpbr-theme-dark.wpbr-collection--list:last-child {
  margin-bottom: 0;
}
.wpbr-theme-dark.wpbr-collection--carousel .wpbr-review__platform-icon,
.wpbr-theme-dark.wpbr-collection--gallery .wpbr-review__platform-icon {
  right: 20px;
  top: 20px;
}
.wpbr-theme-dark.wpbr-collection--carousel .wpbr-collection__item {
  padding: 5px;
}
.wpbr-theme-facebook .wpbr-stars {
  background-position: -100px -10px;
  background-repeat: no-repeat;
  background-size: 200px 96px;
  display: inline-block;
  height: 18px;
  width: 90px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-facebook .wpbr-stars {
  }
}
.wpbr-theme-facebook .wpbr-stars--1 {
  background-position: -82px -10px;
}
.wpbr-theme-facebook .wpbr-stars--1-half {
  background-position: -82px -38px;
}
.wpbr-theme-facebook .wpbr-stars--2 {
  background-position: -64px -10px;
}
.wpbr-theme-facebook .wpbr-stars--2-half {
  background-position: -64px -38px;
}
.wpbr-theme-facebook .wpbr-stars--3 {
  background-position: -46px -10px;
}
.wpbr-theme-facebook .wpbr-stars--3-half {
  background-position: -46px -38px;
}
.wpbr-theme-facebook .wpbr-stars--4 {
  background-position: -28px -10px;
}
.wpbr-theme-facebook .wpbr-stars--4-half {
  background-position: -28px -38px;
}
.wpbr-theme-facebook .wpbr-stars--5 {
  background-position: -10px -10px;
}
.wpbr-theme-facebook .wpbr-reco__icon {
  -ms-flex-preferred-size: 20px;
  -webkit-flex-basis: 20px;
  background-position: -66px -66px;
  background-size: 200px 96px;
  flex-basis: 20px;
  height: 20px;
  margin-right: 5px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-facebook .wpbr-reco__icon {
  }
}
.wpbr-theme-facebook .wpbr-reco__icon--negative {
  background-position: -96px -66px;
}
.wpbr-theme-facebook .wpbr-review__platform-icon {
  background-position: -10px -66px;
  background-size: 200px 96px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-facebook .wpbr-review__platform-icon {
  }
}
.wpbr-theme-highlight .wpbr-theme-facebook .wpbr-review__platform-icon {
  background-position-x: -10px;
}
.wpbr-theme-facebook.wpbr-admin-column-thumbnail,
.wpbr-theme-facebook .wpbr-image-selector__image,
.wpbr-theme-facebook .wpbr-review__reviewer-image img {
  border-radius: 9999px;
}
.wpbr-theme-dark .wpbr-theme-facebook .wpbr-review__platform-icon {
  background-position-x: -38px;
}
.wpbr-theme-google-places .wpbr-stars {
  background-position: -100px -10px;
  background-repeat: no-repeat;
  background-size: 200px 94px;
  display: inline-block;
  height: 18px;
  width: 90px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-google-places .wpbr-stars {
    background-image: url(/images/<EMAIL>);
  }
}
.wpbr-theme-google-places .wpbr-stars--1 {
  background-position: -82px -10px;
}
.wpbr-theme-google-places .wpbr-stars--1-half {
  background-position: -82px -38px;
}
.wpbr-theme-google-places .wpbr-stars--2 {
  background-position: -64px -10px;
}
.wpbr-theme-google-places .wpbr-stars--2-half {
  background-position: -64px -38px;
}
.wpbr-theme-google-places .wpbr-stars--3 {
  background-position: -46px -10px;
}
.wpbr-theme-google-places .wpbr-stars--3-half {
  background-position: -46px -38px;
}
.wpbr-theme-google-places .wpbr-stars--4 {
  background-position: -28px -10px;
}
.wpbr-theme-google-places .wpbr-stars--4-half {
  background-position: -28px -38px;
}
.wpbr-theme-google-places .wpbr-stars--5 {
  background-position: -10px -10px;
}
.wpbr-theme-google-places .wpbr-review__platform-icon {
  background-position: -10px -66px;
  background-size: 200px 94px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-google-places .wpbr-review__platform-icon {
    background-image: url(/images/<EMAIL>);
  }
}
.wpbr-theme-google-places.wpbr-admin-column-thumbnail--custom,
.wpbr-theme-google-places .wpbr-image-selector__image--custom {
  border-radius: 9999px;
}
.wpbr-theme-light.wpbr-collection--carousel .wpbr-review,
.wpbr-theme-light.wpbr-collection--gallery .wpbr-review {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  background-color: #fff;
  border: 1px solid #cfd9db;
  border-radius: 5px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
  padding: 20px;
  width: 100%;
}
.wpbr-theme-light.wpbr-collection--carousel .wpbr-review:last-child,
.wpbr-theme-light.wpbr-collection--gallery .wpbr-review:last-child {
  margin-bottom: 0;
}
.wpbr-theme-light.wpbr-collection--list {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  background-color: #fff;
  border: 1px solid #cfd9db;
  border-radius: 5px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  margin-bottom: 20px;
  padding: 20px;
  width: 100%;
}
.wpbr-theme-light.wpbr-collection--list:last-child {
  margin-bottom: 0;
}
.wpbr-theme-light.wpbr-collection--carousel .wpbr-review__platform-icon,
.wpbr-theme-light.wpbr-collection--gallery .wpbr-review__platform-icon {
  right: 20px;
  top: 20px;
}
.wpbr-theme-light.wpbr-collection--carousel .wpbr-collection__item {
  padding: 5px;
}
.wpbr-theme-transparent.wpbr-collection--gallery {
  grid-gap: 40px;
}
.wpbr-theme-yelp .wpbr-stars {
  background-position: -10px -10px;
  background-repeat: no-repeat;
  background-size: 122px 318px;
  display: inline-block;
  height: 18px;
  width: 102px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-yelp .wpbr-stars {
  }
}
.wpbr-theme-yelp .wpbr-stars--1 {
  background-position-y: -38px;
}
.wpbr-theme-yelp .wpbr-stars--1-half {
  background-position-y: -66px;
}
.wpbr-theme-yelp .wpbr-stars--2 {
  background-position-y: -94px;
}
.wpbr-theme-yelp .wpbr-stars--2-half {
  background-position-y: -122px;
}
.wpbr-theme-yelp .wpbr-stars--3 {
  background-position-y: -150px;
}
.wpbr-theme-yelp .wpbr-stars--3-half {
  background-position-y: -178px;
}
.wpbr-theme-yelp .wpbr-stars--4 {
  background-position-y: -206px;
}
.wpbr-theme-yelp .wpbr-stars--4-half {
  background-position-y: -234px;
}
.wpbr-theme-yelp .wpbr-stars--5 {
  background-position-y: -262px;
}
.wpbr-theme-yelp .wpbr-review__platform-icon {
  background-position: -10px -290px;
  background-size: 122px 318px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-yelp .wpbr-review__platform-icon {
  }
}
.wpbr-theme-highlight .wpbr-theme-yelp .wpbr-review__platform-icon {
  background-position-x: -10px;
}
.wpbr-theme-yelp.wpbr-admin-column-thumbnail,
.wpbr-theme-yelp .wpbr-image-selector__image,
.wpbr-theme-yelp .wpbr-review__reviewer-image img {
  border-radius: 9999px;
}
.wpbr-theme-dark .wpbr-theme-yelp .wpbr-review__platform-icon {
  background-position-x: -38px;
}
.wpbr-theme-yp .wpbr-stars {
  background-position: -135px -10px;
  background-repeat: no-repeat;
  background-size: 266px 94px;
  display: inline-block;
  height: 18px;
  width: 121px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-yp .wpbr-stars {
  }
}
.wpbr-theme-yp .wpbr-stars--1 {
  background-position: -110px -10px;
}
.wpbr-theme-yp .wpbr-stars--1-half {
  background-position: -110px -38px;
}
.wpbr-theme-yp .wpbr-stars--2 {
  background-position: -85px -10px;
}
.wpbr-theme-yp .wpbr-stars--2-half {
  background-position: -85px -38px;
}
.wpbr-theme-yp .wpbr-stars--3 {
  background-position: -60px -10px;
}
.wpbr-theme-yp .wpbr-stars--3-half {
  background-position: -60px -38px;
}
.wpbr-theme-yp .wpbr-stars--4 {
  background-position: -35px -10px;
}
.wpbr-theme-yp .wpbr-stars--4-half {
  background-position: -35px -38px;
}
.wpbr-theme-yp .wpbr-stars--5 {
  background-position: -10px -10px;
}
.wpbr-theme-yp .wpbr-review__platform-icon {
  background-position: -10px -66px;
  background-size: 266px 94px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-yp .wpbr-review__platform-icon {
  }
}
.wpbr-theme-highlight .wpbr-theme-yp .wpbr-review__platform-icon {
  background-position-x: -10px;
}
.wpbr-theme-yp.wpbr-admin-column-thumbnail,
.wpbr-theme-yp .wpbr-image-selector__image,
.wpbr-theme-yp .wpbr-review__reviewer-image img {
  border-radius: 9999px;
}
.wpbr-theme-dark .wpbr-theme-yp .wpbr-review__platform-icon {
  background-position-x: -38px;
}
.wpbr-theme-zomato .wpbr-review__platform-icon {
  background-size: 18px 18px;
}
@media screen and (-webkit-min-device-pixel-ratio: 2),
  screen and (min-resolution: 2dppx) {
  .wpbr-theme-zomato .wpbr-review__platform-icon {
  }
}
.wpbr-theme-zomato.wpbr-admin-column-thumbnail,
.wpbr-theme-zomato .wpbr-image-selector__image,
.wpbr-theme-zomato .wpbr-review__reviewer-image img {
  border-radius: 9999px;
}