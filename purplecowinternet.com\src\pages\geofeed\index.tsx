import * as React from "react";

const GeofeedPage: React.FC = () => {
  const [geofeedText, setGeofeedText] = React.useState<string>("Generating...");

  React.useEffect(() => {
    const fetchAndDownload = async () => {
      const response = await fetch('/geofeed.csv');
      const text = await response.text();
      const lines = text.split('\n').filter(line => line.trim() && !line.startsWith('Prefix'));

      const now = new Date().toUTCString();
      const header = [
        '', // dont delete. empty line to match our original format
        '# OpenGeoFeed by PURPLE COW INTERNET',
        '# Feed for AS397545',
        '#',
        '# This file contains a self-published geofeed as defined in http://tools.ietf.org/html/draft-google-self-published-geofeeds-02(draft02)',
        `# Last modified: ${now}`,
        `# Number of records: ${lines.length}`
      ].join('\n');

      const geofeedContent = header + '\n' + lines.join('\n');

      setGeofeedText(geofeedContent);

      const blob = new Blob([geofeedContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'PurpleCowInternetGeofeed.csv';
      a.click();
      URL.revokeObjectURL(url);
    };

    fetchAndDownload();
  }, []);

  return (
    <pre style={{ wordWrap: "break-word", whiteSpace: "pre-wrap" }}>
      {geofeedText}
    </pre>
  );
};

export default GeofeedPage;
