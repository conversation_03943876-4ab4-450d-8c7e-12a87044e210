import React, { useState, useEffect } from "react"
import CryptoJ<PERSON> from "crypto-js"

const storedHash = "0b25468bc23e07bea58892ff768d6619"

const SecureLogin = () => {
  const [username, setUsername] = useState("")
  const [password, setPaassword] = useState("")
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    const session = JSON.parse(localStorage.getItem("authFlag"))
    if (session && session.valid && new Date().getTime() < session.expires) {
      setIsAuthenticated(true)
    } else {
      localStorage.removeItem("authFlag")
    }
  }, [])

  const md5 = (str: any) => CryptoJS.MD5(str).toString()

  const setSession = () => {
    const expirationTime = new Date().getTime() + 24 * 60 * 60 * 1000 // 24 hours from now
    localStorage.setItem(
      "authFlag",
      JSON.stringify({ valid: true, expires: expirationTime })
    )
    setIsAuthenticated(true)
  }

  const handleLogin = (e: any) => {
    e.preventDefault()
    const inputHash = md5(`${username}:${password}`)

    if (inputHash === storedHash) {
      setSession()
      window.location.href = "/"
    } else {
      alert("Invalid credentials!")
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      window.location.href = "/"
    }
  }, [isAuthenticated])

  return (
    <div className="flex justify-center items-center h-screen bg-gray-100">
      {isAuthenticated ? (
        <div className="bg-white p-8 rounded-lg shadow-md text-center w-96">
          <h1 className="text-2xl font-semibold mb-4">Welcome</h1>
          <p>You now have access to the staging site.</p>
        </div>
      ) : (
        <div className="bg-white p-8 rounded-3xl mx-2 shadow-md text-center w-96">
          <img
            src="https://www.purplecowinternet.com/static/d27a10f94aca03a3e4abfcd1b9ad7390/c0997/cow.webp"
            alt="Logo"
            className="w-24 mx-auto mb-4"
          />
          <form onSubmit={handleLogin}>
            <h2 className="text-xl font-semibold mb-4">Login</h2>
            <input
              autoComplete="off"
              type="text"
              placeholder="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full p-3 border border-purple-300 rounded-md mb-3 focus:outline-none focus:ring-1 focus:ring-purple-300"
            />
            <input
              type="password"
              placeholder="Password"
              autoComplete="off"
              value={password}
              onChange={(e) => setPaassword(e.target.value)}
              className="w-full p-3 border border-purple-300 rounded-md mb-3 focus:outline-none focus:ring-1 focus:ring-purple-300"
            />
            <button className="w-full p-3 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition duration-300">
              Login
            </button>
          </form>
        </div>
      )}
    </div>
  )
}

export default SecureLogin
